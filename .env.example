# RiseMap 环境配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用环境
NODE_ENV=production

# 服务端口配置（统一服务）
PORT=3001

# 静态文件配置
STATIC_PATH=apps/api/static

# 数据库配置
DB_PATH=./data/earthquakes.db

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# API 配置
API_BASE_URL=http://localhost:3001

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# PM2 配置
PM2_INSTANCES=1
PM2_MAX_MEMORY=1G

# 部署配置
DEPLOY_USER=deploy
DEPLOY_HOST=your-server-ip
DEPLOY_PATH=/var/www/rise-map
DEPLOY_REPO=https://github.com/your-username/rise-map-fullstack.git

# 监控配置
ENABLE_MONITORING=true
HEALTH_CHECK_INTERVAL=30000

# 安全配置
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# 缓存配置
CACHE_TTL=300
ENABLE_CACHE=true
