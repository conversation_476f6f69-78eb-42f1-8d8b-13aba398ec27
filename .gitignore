node_modules

# 环境变量文件
.env
.env.local
.env.*.local

# 构建输出
dist/
build/
apps/api/static/
apps/web/dist/
apps/api/dist/

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# PM2 相关
.pm2/
pids/
*.pid
*.seed
*.pid.lock

# 运行时数据
.nyc_output
.coverage
.cache

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 相关
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
tmp/
temp/

# Docker 相关
.dockerignore

# 数据库文件
*.db
*.sqlite
*.sqlite3
data/earthquakes.db

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar