# Cloudflared 内网穿透部署指南

## 🌐 概述

本项目已配置 Cloudflared 内网穿透，可以将本地应用安全地暴露到互联网，无需公网IP或端口转发。

## 🚀 快速开始

### 1. 安装和配置 Cloudflared

```bash
# 安装 cloudflared 并完成初始配置
pnpm tunnel:setup
```

此命令会：
- 安装 cloudflared（如果未安装）
- 引导您登录 Cloudflare 账户
- 创建隧道并生成配置文件

### 2. 快速启动（推荐新手）

```bash
# 快速启动应用和临时隧道
pnpm tunnel:quick
```

这将：
- 构建并启动应用
- 创建临时域名（如: https://xxx.trycloudflare.com）
- 自动打开隧道

### 3. 使用固定隧道（生产环境）

```bash
# 启动完整配置的隧道
pnpm tunnel:start
```

## 📋 可用命令

| 命令 | 说明 |
|------|------|
| `pnpm tunnel:setup` | 安装和配置 cloudflared |
| `pnpm tunnel:quick` | 快速启动临时隧道 |
| `pnpm tunnel:start` | 启动完整配置的隧道 |
| `pnpm tunnel:web` | 仅为Web应用创建临时隧道 |
| `pnpm tunnel:api` | 仅为API服务创建临时隧道 |

## 🔧 服务端口

- **Web应用**: http://localhost:3000
- **API服务**: http://localhost:3001

## 📝 配置文件

- `cloudflared.yml` - 主配置文件
- `scripts/cloudflared-setup.sh` - 安装配置脚本
- `scripts/quick-tunnel.sh` - 快速启动脚本
- `scripts/start-tunnel.sh` - 完整启动脚本

## 🔒 安全说明

- Cloudflared 创建加密隧道，无需开放本地端口
- 支持 Cloudflare 的 DDoS 保护和 WAF
- 所有流量通过 Cloudflare 网络路由

## 🎯 使用场景

1. **开发测试**: 使用临时域名快速分享应用
2. **演示展示**: 获得稳定的公网访问地址
3. **生产部署**: 结合 PM2 实现稳定的服务运行

## 📊 监控和日志

```bash
# 查看应用状态
pnpm pm2:logs

# 查看隧道日志
tail -f logs/cloudflared.log
```

## ⚠️ 注意事项

1. 首次使用需要 Cloudflare 账户
2. 临时域名每次重启会变化
3. 固定域名需要完成隧道配置
4. 确保本地防火墙允许应用端口 