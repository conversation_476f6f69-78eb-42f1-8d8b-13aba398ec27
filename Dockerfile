# RiseMap 地震数据可视化平台 Docker 镜像
# 多阶段构建：统一前后端部署

# ================================
# 构建阶段
# ================================
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@8.15.0

# 复制 package.json 文件
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml ./
COPY apps/api/package.json ./apps/api/
COPY apps/web/package.json ./apps/web/

# 安装依赖
RUN pnpm install

# 复制源代码
COPY . .

# 安装子项目依赖
RUN cd apps/web && pnpm install
RUN cd apps/api && pnpm install

# 构建前端和后端
RUN pnpm build:web && pnpm build:api

# 创建静态文件目录并拷贝前端构建产物到后端静态目录
RUN mkdir -p apps/api/static && cp -r apps/web/dist/* apps/api/static/

# ================================
# 生产运行阶段
# ================================
FROM node:18-alpine AS production

# 安装必要的系统依赖
RUN apk add --no-cache \
    sqlite \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S risemap -u 1001

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm@8.15.0

# 复制 package.json 文件
COPY package.json pnpm-lock.yaml* pnpm-workspace.yaml ./
COPY apps/api/package.json ./apps/api/

# 安装生产依赖
RUN pnpm install --prod

# 安装API子项目的生产依赖
WORKDIR /app/apps/api
RUN pnpm install --prod

# 回到根目录
WORKDIR /app

# 设置Node.js模块路径，解决monorepo依赖问题
ENV NODE_PATH=/app/node_modules:/app/apps/api/node_modules

# 设置静态文件路径环境变量
ENV STATIC_PATH=/app/apps/api/static

# 从构建阶段复制构建产物
COPY --from=builder /app/apps/api/dist ./apps/api/dist
COPY --from=builder /app/apps/api/static ./apps/api/static

# 复制其他必要文件
COPY apps/api/src/db ./apps/api/src/db
COPY data ./data

# 在API目录中创建符号链接到根目录的node_modules
RUN ln -sf /app/node_modules /app/apps/api/node_modules

# 创建日志目录
RUN mkdir -p logs && \
    chown -R risemap:nodejs /app && \
    chmod -R 755 /app

# 切换到应用用户
USER risemap

# 暴露端口 - 只需要后端端口，前端通过后端静态文件服务
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# 使用 dumb-init 作为 PID 1
ENTRYPOINT ["dumb-init", "--"]

# 设置工作目录到API目录
WORKDIR /app/apps/api

# 默认启动命令 - 直接运行Node.js应用
CMD ["node", "dist/server.js"]
