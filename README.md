# RiseMap 地震数据可视化平台

<div align="center">
  <h3>基于Web的地震数据可视化平台</h3>
  <p>为地震监测和研究提供实时、高性能的数据展示和分析工具</p>
</div>

## 📋 项目概述

RiseMap是一个专业的地震数据可视化平台，支持多监测区域的数据管理，具备可扩展性，数据每分钟更新。平台整合了地震事件、断层线、井轨迹和监测台站等多维度数据，为地震研究提供全面的可视化分析工具。

## ✨ 主要特性

- 🗺️ **实时地图可视化** - 基于MapLibre GL JS的高性能地图渲染
- 📊 **多维数据展示** - 地震事件、断层线、井轨迹、监测台站
- ⏱️ **时间轴播放** - 按时间播放地震事件，支持播放速度控制
- 🎛️ **交互式控制** - 图层控制、时间范围选择、数据筛选
- 📱 **响应式设计** - 支持桌面和平板设备
- 🔄 **实时更新** - 数据每分钟自动更新
- 🌍 **多区域支持** - 可动态添加新监测区域

## 🏗️ 技术架构

### 前端技术栈
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的JavaScript
- **MapLibre GL JS** - 高性能地图渲染引擎
- **Deck.gl** - WebGL数据可视化框架
- **Tailwind CSS** - 实用优先的CSS框架
- **React Query** - 数据获取和状态管理
- **Zustand** - 轻量级状态管理
- **Vite** - 快速构建工具

### 后端技术栈
- **Fastify** - 高性能Node.js Web框架
- **TypeScript** - 类型安全的服务端开发
- **SQLite** - 轻量级关系型数据库
- **JWT** - 用户认证和授权

### 包管理
- **pnpm** - 快速、节省磁盘空间的包管理器

## 📁 项目结构

```
rise-map-fullstack/
├── apps/
│   ├── web/                 # 前端应用
│   │   ├── src/
│   │   ├── package.json
│   │   └── vite.config.ts
│   └── api/                 # 后端API
│       ├── src/
│       ├── package.json
│       └── tsconfig.json
├── data/                    # 数据文件
│   ├── 地震事件点数据/
│   ├── 断层数据/
│   ├── 井轨迹数据/
│   └── 台站位置/
├── package.json             # 根项目配置
└── 项目需求文档.md          # 详细需求文档
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
# 安装根项目依赖
pnpm install

# 安装前端依赖
cd apps/web && pnpm install

# 安装后端依赖
cd apps/api && pnpm install
```

### 数据库初始化

```bash
# 数据库迁移
pnpm db:migrate

# 导入初始数据
pnpm db:seed

# 重置数据库（可选）
pnpm db:reset
```

### 启动开发服务器

```bash
# 同时启动前端和后端开发服务器
pnpm dev

# 或者分别启动
pnpm dev:web    # 前端服务器 (localhost:3000)
pnpm dev:api    # 后端服务器 (localhost:3001)
```

### 构建生产版本

```bash
# 构建所有应用
pnpm build

# 分别构建
pnpm build:web
pnpm build:api
```

### 生产环境部署

项目现在支持统一部署模式，后端服务器集成了静态文件托管功能：

```bash
# 统一构建前后端
pnpm build:unified

# 启动统一服务（包含前端静态文件和后端API）
pnpm start:api
```

#### 环境变量配置

可以通过 `.env` 文件或环境变量配置以下参数：

```bash
# 统一服务配置
PORT=3001                          # 服务端口
STATIC_PATH=apps/api/static        # 静态文件目录
```

示例 `.env` 文件配置：
```bash
# 复制 .env.example 为 .env 并修改配置
cp .env.example .env
```

## 🐳 Docker 部署

### 快速开始

```bash
# 构建 Docker 镜像
docker build -t risemap .

# 启动服务
docker-compose up -d

# 访问应用
# 统一服务: http://localhost:3001
```

### 使用 Nginx 反向代理

```bash
# 启动服务（包含 Nginx）
./docker-deploy.sh start --nginx

# 访问应用: http://localhost
```

### 其他 Docker 命令

```bash
# 查看日志
./docker-deploy.sh logs

# 停止服务
./docker-deploy.sh stop

# 重启服务
./docker-deploy.sh restart

# 清理资源
./docker-deploy.sh clean
```

详细的 Docker 部署说明请参考 [DOCKER.md](./DOCKER.md)

## 📊 数据结构

### 地震事件数据
- 事件ID、发生时间、经纬度坐标
- 深度(km)、震级、区域信息
- 总计6,237条记录

### 断层数据
- 三级断层分级（LX_fault1/2/3.txt）
- 使用">"分隔不同断层段
- 支持不同等级的可视化样式

### 井轨迹数据
- 103个井平台的轨迹数据
- 包含轨迹坐标和井平台名称
- 支持多点轨迹路径展示

### 监测台站数据
- 89个监测台站位置
- 台站名称、坐标、监测状态
- 实时状态更新

## 🎯 核心功能

### 地图可视化
- 地震事件点位展示，按震级分级显示
- 断层线图层，三级断层分级展示
- 井轨迹路径可视化
- 监测台站位置标记

### 交互功能
- 地图点击查看详情
- 数据表格与地图联动
- 图层控制和筛选功能
- 时间范围选择

### 时间轴播放
- 按时间播放地震事件
- 播放速度控制
- 时间范围选择

## 🔧 开发规范

### 代码规范
- UI文本使用中文
- 代码注释使用中文
- 变量命名使用英文规范
- Git提交信息使用中文

### 性能要求
- 地图渲染保持60fps流畅度
- 大数据量加载优化
- 图层切换响应迅速
- 内存使用控制

## 📝 API文档

后端API提供以下主要接口：

- `GET /api/earthquakes` - 获取地震事件数据
- `GET /api/faults` - 获取断层数据
- `GET /api/wells` - 获取井轨迹数据
- `GET /api/stations` - 获取监测台站数据

详细API文档可通过访问 `http://localhost:3001/docs` 查看Swagger文档。
