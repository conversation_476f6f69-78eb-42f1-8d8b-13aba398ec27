# RiseMap API 服务

基于 Fastify 框架的地震数据可视化平台后端 API 服务。

## 🚀 功能特性

- ✅ **完整的 RESTful API**: 地震事件、断层、井轨迹、台站、区域数据管理
- ✅ **用户认证系统**: JWT 令牌认证，支持多角色权限控制
- ✅ **数据库管理**: SQLite 数据库，完整的迁移和种子系统
- ✅ **数据导入功能**: 批量数据导入和处理
- ✅ **API 文档**: 完整的 Swagger 文档
- ✅ **错误处理**: 全局错误处理和请求日志
- ✅ **类型安全**: 完整的 TypeScript 类型定义

## 📋 技术栈

- **框架**: Fastify 4.x
- **语言**: TypeScript
- **数据库**: SQLite
- **认证**: JWT
- **文档**: Swagger/OpenAPI
- **包管理**: pnpm

## 🛠️ 开发环境设置

### 安装依赖

```bash
pnpm install
```

### 数据库初始化

```bash
# 创建数据库表
pnpm db:migrate

# 插入种子数据
pnpm db:seed

# 重置数据库（可选）
pnpm db:reset
```

### 启动开发服务器

```bash
pnpm dev
```

服务器将在 http://localhost:3001 启动

## 📚 API 文档

启动服务器后，访问 http://localhost:3001/docs 查看完整的 API 文档。

## 🔐 默认管理员账户

- **邮箱**: <EMAIL>
- **密码**: admin123

## 📊 API 端点概览

### 认证相关
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/refresh` - 刷新令牌

### 地震事件
- `GET /api/earthquakes` - 获取地震事件列表
- `GET /api/earthquakes/:id` - 获取地震事件详情
- `GET /api/earthquakes/stats` - 获取统计信息

### 断层数据
- `GET /api/faults` - 获取断层列表
- `GET /api/faults/:id` - 获取断层详情
- `GET /api/faults/level/:level` - 按等级获取断层
- `GET /api/faults/stats` - 获取统计信息

### 井轨迹数据
- `GET /api/wells` - 获取井轨迹列表
- `GET /api/wells/:id` - 获取井轨迹详情
- `GET /api/wells/platforms` - 获取井平台列表
- `GET /api/wells/stats` - 获取统计信息

### 监测台站
- `GET /api/stations` - 获取台站列表
- `GET /api/stations/:id` - 获取台站详情
- `GET /api/stations/active` - 获取活跃台站
- `GET /api/stations/stats` - 获取统计信息

### 区域管理
- `GET /api/regions` - 获取区域列表
- `GET /api/regions/:id` - 获取区域详情
- `GET /api/regions/:id/stats` - 获取区域统计

### 用户管理（管理员）
- `GET /api/users` - 获取用户列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息
- `DELETE /api/users/:id` - 删除用户
- `GET /api/users/stats` - 获取用户统计

### 数据导入（管理员）
- `POST /api/import/all` - 批量导入所有数据
- `POST /api/import/earthquakes` - 导入地震事件数据
- `POST /api/import/faults` - 导入断层数据
- `POST /api/import/wells` - 导入井轨迹数据
- `POST /api/import/stations` - 导入台站数据

## 🧪 测试

### API 测试
```bash
pnpm test:api
```

### 健康检查
```bash
curl http://localhost:3001/health
```

## 📁 项目结构

```
apps/api/
├── src/
│   ├── config/          # 配置文件
│   ├── db/              # 数据库相关
│   │   ├── database.ts  # 数据库连接
│   │   ├── migrate.ts   # 数据库迁移
│   │   ├── seed.ts      # 种子数据
│   │   └── reset.ts     # 数据库重置
│   ├── plugins/         # Fastify 插件
│   │   ├── auth.ts      # 认证插件
│   │   ├── errorHandler.ts # 错误处理
│   │   └── requestLogger.ts # 请求日志
│   ├── routes/          # API 路由
│   │   ├── auth.ts      # 认证路由
│   │   ├── earthquakes.ts # 地震事件
│   │   ├── faults.ts    # 断层数据
│   │   ├── wells.ts     # 井轨迹
│   │   ├── stations.ts  # 监测台站
│   │   ├── regions.ts   # 区域管理
│   │   ├── users.ts     # 用户管理
│   │   └── import.ts    # 数据导入
│   ├── scripts/         # 工具脚本
│   ├── types/           # 类型定义
│   ├── utils/           # 工具函数
│   └── server.ts        # 服务器入口
├── package.json
├── tsconfig.json
└── README.md
```

## 🔧 可用脚本

- `pnpm dev` - 启动开发服务器
- `pnpm build` - 构建生产版本
- `pnpm start` - 启动生产服务器
- `pnpm db:migrate` - 运行数据库迁移
- `pnpm db:seed` - 插入种子数据
- `pnpm db:reset` - 重置数据库
- `pnpm import:data` - 导入数据
- `pnpm test:api` - 运行 API 测试

## 🌟 下一步计划

1. 添加单元测试和集成测试
2. 实现数据缓存机制
3. 添加实时数据更新功能
4. 优化数据库查询性能
5. 添加数据备份和恢复功能

## 📝 开发说明

- 所有 API 响应都遵循统一的格式
- 使用 JWT 进行身份认证
- 支持三种用户角色：admin、user、viewer
- 数据库使用 SQLite，支持事务操作
- 完整的错误处理和日志记录
- 支持分页、排序、筛选等查询功能
