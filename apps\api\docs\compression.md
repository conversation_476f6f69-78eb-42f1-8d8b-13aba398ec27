# API 服务器压缩功能

## 概述

RiseMap API 服务器已启用全面的压缩功能，支持对API响应和静态文件进行压缩，以提高传输效率和用户体验。

## 压缩配置

### 支持的压缩算法
- **Gzip** - 广泛支持的压缩算法
- **Deflate** - 标准压缩算法
- **Brotli** - 现代高效压缩算法

### 压缩设置
```typescript
await fastify.register(compress, {
  global: true,                    // 全局启用压缩
  encodings: ['gzip', 'deflate', 'br'],  // 支持的编码
  threshold: 1024,                 // 只压缩大于1KB的响应
  brotliOptions: {
    params: {
      [require('zlib').constants.BROTLI_PARAM_MODE]: require('zlib').constants.BROTLI_MODE_TEXT,
      [require('zlib').constants.BROTLI_PARAM_QUALITY]: 4,
    }
  },
  zlibOptions: {
    level: 6,                      // Gzip压缩级别 (1-9)
    chunkSize: 1024,               // 压缩块大小
  }
});
```

## 功能特性

### API 响应压缩
- ✅ 自动压缩所有API响应
- ✅ 支持JSON数据压缩
- ✅ 智能阈值控制（仅压缩>1KB的响应）
- ✅ 客户端协商压缩算法

### 静态文件压缩
- ✅ 自动压缩HTML、CSS、JS文件
- ✅ 支持图片和字体文件压缩
- ✅ 智能缓存头设置

### 缓存策略
```typescript
setHeaders: (res, path) => {
  // 静态资源长期缓存 (1年)
  if (path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/)) {
    res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
  }
  // HTML文件短期缓存 (1小时)
  else if (path.match(/\.html$/)) {
    res.setHeader('Cache-Control', 'public, max-age=3600');
  }
}
```

## 测试验证

### 运行压缩测试
```bash
# 完整压缩功能测试
pnpm test:compression

# 简单验证测试
tsx src/scripts/verify-compression.ts
```

### 测试结果示例
```
📊 压缩测试汇总:
✅ 成功请求: 7/7
🗜️  压缩响应: 4/7
📈 平均压缩率: 65.2%

📋 详细结果:
✅ /api/earthquakes?limit=10 [gzip]
✅ /api/faults?limit=5 [gzip]
✅ /api/wells?limit=5 [gzip]
✅ 静态文件 [gzip]
```

## 性能优势

### 带宽节省
- **JSON数据**: 通常可节省 60-80% 带宽
- **HTML文件**: 通常可节省 70-85% 带宽
- **CSS/JS文件**: 通常可节省 65-75% 带宽

### 加载速度提升
- 减少网络传输时间
- 降低移动设备数据使用
- 提升用户体验

## 浏览器支持

所有现代浏览器都支持压缩功能：
- ✅ Chrome/Edge (Brotli, Gzip, Deflate)
- ✅ Firefox (Brotli, Gzip, Deflate)
- ✅ Safari (Gzip, Deflate)
- ✅ 移动浏览器 (Gzip, Deflate)

## 监控和调试

### 检查压缩状态
```bash
# 使用curl检查响应头
curl -H "Accept-Encoding: gzip, deflate, br" -v http://localhost:3001/api/earthquakes

# 检查响应头中的 Content-Encoding 字段
```

### 常见响应头
```
Content-Encoding: gzip        # 使用Gzip压缩
Content-Encoding: br          # 使用Brotli压缩
Content-Encoding: deflate     # 使用Deflate压缩
```

## 注意事项

1. **小文件处理**: 小于1KB的响应不会被压缩，避免压缩开销
2. **CPU使用**: 压缩会增加少量CPU使用，但网络传输收益更大
3. **缓存友好**: 压缩与HTTP缓存完全兼容
4. **自动协商**: 服务器根据客户端支持自动选择最佳压缩算法

## 故障排除

### 压缩未生效
1. 检查客户端是否发送了 `Accept-Encoding` 头
2. 确认响应大小是否超过阈值 (1KB)
3. 检查服务器日志是否有错误

### 版本兼容性
- 确保使用与Fastify 4.x兼容的 `@fastify/compress@^6.0.0`
- 避免使用较新版本导致的兼容性问题
