{"name": "@rise-map/api", "version": "1.0.0", "description": "RiseMap 地震数据可视化平台 API 服务", "main": "dist/server.js", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:migrate": "tsx src/db/migrate.ts", "db:seed": "tsx src/db/seed.ts", "db:reset": "tsx src/db/reset.ts", "import:data": "tsx src/scripts/import-data.ts", "test:api": "tsx src/scripts/test-api.ts", "test:compression": "tsx src/scripts/test-compression.ts"}, "dependencies": {"@fastify/compress": "^6.0.0", "@fastify/cors": "^9.0.1", "@fastify/jwt": "^8.0.1", "@fastify/static": "^7.0.0", "@fastify/swagger": "^8.15.0", "@fastify/swagger-ui": "^4.1.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^12.2.0", "fastify": "^4.28.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/node": "^20.14.10", "tsx": "^4.16.2", "typescript": "^5.5.3", "undici": "^7.13.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}