// Swagger 文档配置
export const swaggerConfig = {
  swagger: {
    info: {
      title: 'RiseMap API',
      description: `
# RiseMap 地震数据可视化平台 API

RiseMap 是一个专业的地震数据可视化平台，提供实时地震监测数据的查询、分析和可视化功能。

## 功能特性

- 🌍 **地震事件数据**: 提供详细的地震事件信息，支持按时间、震级、区域等条件筛选
- 🗺️ **断层数据**: 三级断层数据管理，支持地图可视化
- 🏗️ **井轨迹数据**: 井平台和轨迹数据管理
- 📡 **监测台站**: 地震监测台站信息和状态管理
- 👥 **用户管理**: 完整的用户认证和权限管理系统
- 📊 **数据导入**: 批量数据导入和处理功能
- 🔒 **安全认证**: JWT 令牌认证，支持多角色权限控制

## 认证说明

大部分 API 需要用户认证。请先通过 \`/api/auth/login\` 接口登录获取访问令牌，然后在请求头中添加：

\`\`\`
Authorization: Bearer <your-token>
\`\`\`

## 用户角色

- **admin**: 管理员，拥有所有权限
- **user**: 普通用户，可以查看和操作数据
- **viewer**: 只读用户，仅可查看数据

## 默认管理员账户

- 邮箱: <EMAIL>
- 密码: admin123

## 响应格式

所有 API 响应都遵循统一格式：

\`\`\`json
{
  "success": true,
  "data": { ... },
  "message": "操作成功"
}
\`\`\`

错误响应：

\`\`\`json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
\`\`\`

## 分页参数

支持分页的接口通用参数：

- \`page\`: 页码，从 1 开始
- \`limit\`: 每页数量，最大 100
- \`sort\`: 排序字段
- \`order\`: 排序方向，asc 或 desc
      `,
      version: '1.0.0',
      contact: {
        name: 'RiseMap 开发团队',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    host: process.env.API_HOST || 'localhost:3001',
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    tags: [
      {
        name: '用户认证',
        description: '用户登录、注册和令牌管理'
      },
      {
        name: '用户管理',
        description: '用户信息管理（管理员功能）'
      },
      {
        name: '地震事件',
        description: '地震事件数据查询和统计'
      },
      {
        name: '断层数据',
        description: '断层数据管理和查询'
      },
      {
        name: '井轨迹数据',
        description: '井轨迹和井平台数据管理'
      },
      {
        name: '监测台站',
        description: '地震监测台站信息管理'
      },
      {
        name: '区域管理',
        description: '监测区域信息管理'
      },
      {
        name: '数据导入',
        description: '批量数据导入功能（管理员功能）'
      }
    ],
    securityDefinitions: {
      Bearer: {
        type: 'apiKey' as const,
        name: 'Authorization',
        in: 'header' as const,
        description: '请输入 Bearer <token>'
      }
    },
    security: [
      {
        Bearer: []
      }
    ]
  }
};

// Swagger UI 配置
export const swaggerUiConfig = {
  routePrefix: '/docs',
  exposeRoute: true,
  staticCSP: true,
  transformStaticCSP: (header: string) => header,
  uiConfig: {
    docExpansion: 'list',
    deepLinking: true,
    defaultModelRendering: 'example',
    defaultModelsExpandDepth: 2,
    defaultModelExpandDepth: 2,
    displayOperationId: false,
    displayRequestDuration: true,
    filter: true,
    showExtensions: false,
    showCommonExtensions: false,
    tryItOutEnabled: true,
    requestSnippetsEnabled: true,
    validatorUrl: null
  },
  uiHooks: {
    onRequest: function (request: any, reply: any, next: any) {
      next();
    },
    preHandler: function (request: any, reply: any, next: any) {
      next();
    }
  },
  theme: {
    title: 'RiseMap API 文档',
    css: [
      {
        filename: 'theme.css',
        content: `
          .swagger-ui .topbar { display: none; }
          .swagger-ui .info .title { color: #1f2937; }
          .swagger-ui .info .description { color: #4b5563; }
          .swagger-ui .scheme-container { background: #f9fafb; }
        `
      }
    ]
  }
} as any;
