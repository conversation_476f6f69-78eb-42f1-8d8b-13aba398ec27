import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

// 数据库连接实例
let db: Database.Database | null = null;

// 数据库文件路径
const DB_PATH = path.join(process.cwd(), 'data', 'risemap.db');

// 初始化数据库连接
export async function initDatabase(): Promise<Database.Database> {
  // 确保数据目录存在
  const dataDir = path.dirname(DB_PATH);
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  try {
    db = new Database(DB_PATH);
    console.log('✅ SQLite 数据库连接成功');

    // 启用外键约束
    db.pragma('foreign_keys = ON');

    return db;
  } catch (err) {
    console.error('数据库连接失败:', err);
    throw err;
  }
}

// 获取数据库实例
export function getDatabase(): Database.Database {
  if (!db) {
    throw new Error('数据库未初始化，请先调用 initDatabase()');
  }
  return db;
}

// 关闭数据库连接
export async function closeDatabase(): Promise<void> {
  if (db) {
    try {
      db.close();
      console.log('✅ 数据库连接已关闭');
      db = null;
    } catch (err) {
      console.error('关闭数据库连接失败:', err);
      throw err;
    }
  }
}

// 数据库查询辅助函数
export class DatabaseHelper {
  private db: Database.Database;

  constructor() {
    this.db = getDatabase();
  }

  // 执行查询（返回所有结果）
  async all<T = any>(sql: string, params: any[] = []): Promise<T[]> {
    try {
      const stmt = this.db.prepare(sql);
      const rows = stmt.all(params) as T[];
      return rows;
    } catch (err) {
      throw err;
    }
  }

  // 执行查询（返回单个结果）
  async get<T = any>(sql: string, params: any[] = []): Promise<T | undefined> {
    try {
      const stmt = this.db.prepare(sql);
      const row = stmt.get(params) as T | undefined;
      return row;
    } catch (err) {
      throw err;
    }
  }

  // 执行更新/插入/删除操作
  async run(sql: string, params: any[] = []): Promise<{ lastID: number; changes: number }> {
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(params);
      return {
        lastID: Number(result.lastInsertRowid),
        changes: result.changes
      };
    } catch (err) {
      throw err;
    }
  }

  // 执行事务
  async transaction<T>(callback: (helper: DatabaseHelper) => Promise<T>): Promise<T> {
    // better-sqlite3 的事务必须是同步的，所以我们使用传统的 BEGIN/COMMIT 方式
    try {
      await this.run('BEGIN TRANSACTION');
      const result = await callback(this);
      await this.run('COMMIT');
      return result;
    } catch (error) {
      await this.run('ROLLBACK');
      throw error;
    }
  }

  // 批量插入优化方法
  async batchInsert(sql: string, dataArray: any[][]): Promise<number> {
    const stmt = this.db.prepare(sql);
    const transaction = this.db.transaction((data: any[][]) => {
      let insertedCount = 0;
      for (const params of data) {
        const result = stmt.run(params);
        if (result.changes > 0) {
          insertedCount++;
        }
      }
      return insertedCount;
    });

    return transaction(dataArray);
  }
}
