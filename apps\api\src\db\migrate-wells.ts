import { initDatabase, DatabaseHelper, closeDatabase } from './database';

// 井轨迹和井平台分离迁移脚本
export async function migrateWells() {
  console.log('🔄 开始井轨迹和井平台分离迁移...');
  
  try {
    await initDatabase();
    const dbHelper = new DatabaseHelper();

    // 1. 创建新的井平台表
    console.log('📋 创建井平台表...');
    await dbHelper.run(`
      CREATE TABLE IF NOT EXISTS well_platforms (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        region TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (region) REFERENCES regions(name)
      )
    `);

    // 2. 创建新的井轨迹表
    console.log('📋 创建新的井轨迹表...');
    await dbHelper.run(`
      CREATE TABLE IF NOT EXISTS well_trajectories_new (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        platform_id INTEGER NOT NULL,
        coordinates TEXT NOT NULL,
        region TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (platform_id) REFERENCES well_platforms(id),
        FOREIGN KEY (region) REFERENCES regions(name)
      )
    `);

    // 3. 迁移现有数据
    console.log('🔄 迁移现有数据...');
    
    // 获取现有的井轨迹数据
    const existingWells = await dbHelper.all<{
      id: number;
      platform_name: string;
      coordinates: string;
      region: string;
      created_at: string;
    }>('SELECT * FROM well_trajectories');

    console.log(`📊 找到 ${existingWells.length} 条现有井轨迹数据`);

    // 处理每条记录
    for (const well of existingWells) {
      try {
        const coordinates = JSON.parse(well.coordinates);
        
        // 判断是井轨迹还是井平台
        const isTrajectory = Array.isArray(coordinates) && 
                            coordinates.length > 0 && 
                            Array.isArray(coordinates[0]) && 
                            coordinates[0].length > 1;

        if (isTrajectory) {
          // 这是井轨迹数据
          const trajectoryName = well.platform_name;
          
          // 提取井平台名称
          const platformName = extractPlatformName(trajectoryName);
          
          // 获取或创建井平台
          let platform = await dbHelper.get<{ id: number }>(
            'SELECT id FROM well_platforms WHERE name = ?',
            [platformName]
          );

          if (!platform) {
            // 创建井平台，使用轨迹的第一个坐标点
            const firstPoint = coordinates[0][0];
            await dbHelper.run(
              'INSERT INTO well_platforms (name, latitude, longitude, region) VALUES (?, ?, ?, ?)',
              [platformName, firstPoint[1], firstPoint[0], well.region]
            );
            
            platform = await dbHelper.get<{ id: number }>(
              'SELECT id FROM well_platforms WHERE name = ?',
              [platformName]
            );
          }

          // 创建井轨迹记录
          if (platform) {
            await dbHelper.run(
              'INSERT INTO well_trajectories_new (name, platform_id, coordinates, region) VALUES (?, ?, ?, ?)',
              [trajectoryName, platform.id, well.coordinates, well.region]
            );
            console.log(`✅ 迁移井轨迹: ${trajectoryName} -> 井平台: ${platformName}`);
          }
        } else {
          // 这是井平台数据（单点）
          const platformName = well.platform_name;
          const point = coordinates[0];
          
          // 创建或更新井平台
          await dbHelper.run(
            'INSERT OR REPLACE INTO well_platforms (name, latitude, longitude, region) VALUES (?, ?, ?, ?)',
            [platformName, point[1], point[0], well.region]
          );
          console.log(`✅ 迁移井平台: ${platformName}`);
        }
      } catch (error) {
        console.error(`❌ 迁移记录失败: ${well.platform_name}`, error);
      }
    }

    // 4. 删除旧表，重命名新表
    console.log('🔄 替换旧表...');
    await dbHelper.run('DROP TABLE IF EXISTS well_trajectories_old');
    await dbHelper.run('ALTER TABLE well_trajectories RENAME TO well_trajectories_old');
    await dbHelper.run('ALTER TABLE well_trajectories_new RENAME TO well_trajectories');

    // 5. 创建索引
    console.log('📋 创建索引...');
    await dbHelper.run('CREATE INDEX IF NOT EXISTS idx_well_platforms_region ON well_platforms(region)');
    await dbHelper.run('CREATE INDEX IF NOT EXISTS idx_well_platforms_name ON well_platforms(name)');
    await dbHelper.run('CREATE INDEX IF NOT EXISTS idx_well_trajectories_platform_id ON well_trajectories(platform_id)');
    await dbHelper.run('CREATE INDEX IF NOT EXISTS idx_well_trajectories_region ON well_trajectories(region)');

    console.log('✅ 井轨迹和井平台分离迁移完成！');

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  } finally {
    await closeDatabase();
  }
}

// 从井轨迹名称提取井平台名称
function extractPlatformName(trajectoryName: string): string {
  // 匹配 "名称-数字" 格式，提取前缀作为井平台名称
  const match = trajectoryName.match(/^(.+)-\d+$/);
  return match ? match[1] : trajectoryName;
}

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  migrateWells();
}
