import { initDatabase, DatabaseHelper, closeDatabase } from './database';

// 数据库表创建 SQL
const createTables = [
  // 用户表
  `CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user', 'viewer')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`,

  // 区域表
  `CREATE TABLE IF NOT EXISTS regions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    bounds TEXT NOT NULL, -- JSON 格式存储边界坐标
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`,

  // 地震事件表
  `CREATE TABLE IF NOT EXISTS earthquakes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_id TEXT UNIQUE NOT NULL,
    occurred_at DATETIME NOT NULL,
    latitude REAL NOT NULL,
    longitude REAL NOT NULL,
    depth REAL NOT NULL,
    magnitude REAL NOT NULL,
    region TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (region) REFERENCES regions(name)
  )`,

  // 断层表
  `CREATE TABLE IF NOT EXISTS faults (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    level INTEGER NOT NULL CHECK (level IN (1, 2, 3)),
    coordinates TEXT NOT NULL, -- JSON 格式存储坐标数组
    region TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (region) REFERENCES regions(name)
  )`,

  // 井轨迹表
  `CREATE TABLE IF NOT EXISTS well_trajectories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform_name TEXT NOT NULL,
    coordinates TEXT NOT NULL, -- JSON 格式存储坐标数组
    region TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (region) REFERENCES regions(name)
  )`,

  // 监测台站表
  `CREATE TABLE IF NOT EXISTS stations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    latitude REAL NOT NULL,
    longitude REAL NOT NULL,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance')),
    region TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (region) REFERENCES regions(name)
  )`,

  // 震源机制解表
  `CREATE TABLE IF NOT EXISTS focal_mechanisms (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    time TEXT NOT NULL,
    latitude REAL NOT NULL,
    longitude REAL NOT NULL,
    depth REAL NOT NULL,
    magnitude REAL NOT NULL,
    strike1 REAL NOT NULL,
    dip1 REAL NOT NULL,
    rake1 REAL NOT NULL,
    strike2 REAL NOT NULL,
    dip2 REAL NOT NULL,
    rake2 REAL NOT NULL,
    misfit REAL,
    mxx REAL,
    myy REAL,
    mzz REAL,
    mxy REAL,
    mxz REAL,
    myz REAL,
    region TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (region) REFERENCES regions(name)
  )`
];

// 创建索引
const createIndexes = [
  'CREATE INDEX IF NOT EXISTS idx_earthquakes_occurred_at ON earthquakes(occurred_at)',
  'CREATE INDEX IF NOT EXISTS idx_earthquakes_magnitude ON earthquakes(magnitude)',
  'CREATE INDEX IF NOT EXISTS idx_earthquakes_region ON earthquakes(region)',
  'CREATE INDEX IF NOT EXISTS idx_earthquakes_location ON earthquakes(latitude, longitude)',
  'CREATE INDEX IF NOT EXISTS idx_faults_level ON faults(level)',
  'CREATE INDEX IF NOT EXISTS idx_faults_region ON faults(region)',
  'CREATE INDEX IF NOT EXISTS idx_stations_status ON stations(status)',
  'CREATE INDEX IF NOT EXISTS idx_stations_region ON stations(region)',
  'CREATE INDEX IF NOT EXISTS idx_well_trajectories_region ON well_trajectories(region)',
  'CREATE INDEX IF NOT EXISTS idx_focal_mechanisms_date ON focal_mechanisms(date)',
  'CREATE INDEX IF NOT EXISTS idx_focal_mechanisms_magnitude ON focal_mechanisms(magnitude)',
  'CREATE INDEX IF NOT EXISTS idx_focal_mechanisms_region ON focal_mechanisms(region)',
  'CREATE INDEX IF NOT EXISTS idx_focal_mechanisms_location ON focal_mechanisms(latitude, longitude)'
];

// 执行数据库迁移
async function migrate() {
  try {
    console.log('🔄 开始数据库迁移...');

    // 初始化数据库连接
    await initDatabase();
    const dbHelper = new DatabaseHelper();

    // 创建表
    console.log('📋 创建数据表...');
    for (const sql of createTables) {
      await dbHelper.run(sql);
    }

    // 创建索引
    console.log('🔍 创建索引...');
    for (const sql of createIndexes) {
      await dbHelper.run(sql);
    }

    console.log('✅ 数据库迁移完成');

  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  } finally {
    await closeDatabase();
  }
}

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  migrate();
}

export { migrate };
