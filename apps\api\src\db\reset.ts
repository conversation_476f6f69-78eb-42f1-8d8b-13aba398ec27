import { initDatabase, DatabaseHelper, closeDatabase } from './database';
import { migrate } from './migrate';
import { seed } from './seed';

// 删除所有表的 SQL
const dropTables = [
  'DROP TABLE IF EXISTS earthquakes',
  'DROP TABLE IF EXISTS faults',
  'DROP TABLE IF EXISTS well_trajectories',
  'DROP TABLE IF EXISTS stations',
  'DROP TABLE IF EXISTS focal_mechanisms',
  'DROP TABLE IF EXISTS users',
  'DROP TABLE IF EXISTS regions'
];

// 重置数据库
async function reset() {
  try {
    console.log('🔄 开始重置数据库...');

    // 初始化数据库连接
    await initDatabase();
    const dbHelper = new DatabaseHelper();

    // 删除所有表
    console.log('🗑️  删除现有表...');
    for (const sql of dropTables) {
      await dbHelper.run(sql);
    }

    console.log('✅ 数据库表删除完成');

    // 关闭连接，准备重新迁移
    await closeDatabase();

    // 重新执行迁移
    console.log('🔄 重新执行迁移...');
    await migrate();

    // 执行种子数据
    console.log('🌱 重新执行种子数据...');
    await seed();

    console.log('✅ 数据库重置完成');

  } catch (error) {
    console.error('❌ 数据库重置失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行重置
if (require.main === module) {
  reset();
}

export { reset };
