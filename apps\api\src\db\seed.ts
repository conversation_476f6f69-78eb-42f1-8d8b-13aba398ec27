import bcrypt from 'bcryptjs';
import { initDatabase, DatabaseHelper, closeDatabase } from './database';

// 种子数据
const seedData = {
  // 默认管理员用户
  admin: {
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
  },

  // 默认区域
  regions: [
    {
      name: '龙门山断裂带',
      bounds: JSON.stringify({
        north: 32.5,
        south: 30.5,
        east: 104.5,
        west: 102.5
      }),
      description: '四川龙门山断裂带地震监测区域'
    }
  ],

  // 示例地震事件
  earthquakes: [
    {
      event_id: 'EQ_2024_001',
      occurred_at: '2024-01-15 14:30:00',
      latitude: 31.0,
      longitude: 103.4,
      depth: 12.5,
      magnitude: 4.2,
      region: '龙门山断裂带'
    },
    {
      event_id: 'EQ_2024_002',
      occurred_at: '2024-01-16 09:15:00',
      latitude: 31.2,
      longitude: 103.6,
      depth: 8.3,
      magnitude: 3.8,
      region: '龙门山断裂带'
    }
  ],

  // 示例监测台站
  stations: [
    {
      name: 'LMS001',
      latitude: 31.1,
      longitude: 103.5,
      status: 'active',
      region: '龙门山断裂带'
    },
    {
      name: 'LMS002',
      latitude: 31.3,
      longitude: 103.7,
      status: 'active',
      region: '龙门山断裂带'
    }
  ]
};

// 执行数据种子
async function seed() {
  try {
    console.log('🌱 开始数据种子...');
    
    // 初始化数据库连接
    await initDatabase();
    const dbHelper = new DatabaseHelper();

    // 插入管理员用户
    console.log('👤 创建管理员用户...');
    const hashedPassword = await bcrypt.hash(seedData.admin.password, 10);
    
    try {
      await dbHelper.run(
        'INSERT INTO users (email, password_hash, role) VALUES (?, ?, ?)',
        [seedData.admin.email, hashedPassword, seedData.admin.role]
      );
      console.log(`✅ 管理员用户创建成功: ${seedData.admin.email}`);
    } catch (error: any) {
      if (error.message.includes('UNIQUE constraint failed')) {
        console.log('ℹ️  管理员用户已存在，跳过创建');
      } else {
        throw error;
      }
    }

    // 插入区域数据
    console.log('🗺️  创建区域数据...');
    for (const region of seedData.regions) {
      try {
        await dbHelper.run(
          'INSERT INTO regions (name, bounds, description) VALUES (?, ?, ?)',
          [region.name, region.bounds, region.description]
        );
        console.log(`✅ 区域创建成功: ${region.name}`);
      } catch (error: any) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`ℹ️  区域 ${region.name} 已存在，跳过创建`);
        } else {
          throw error;
        }
      }
    }

    // 插入示例地震事件
    console.log('🌍 创建示例地震事件...');
    for (const earthquake of seedData.earthquakes) {
      try {
        await dbHelper.run(
          'INSERT INTO earthquakes (event_id, occurred_at, latitude, longitude, depth, magnitude, region) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [earthquake.event_id, earthquake.occurred_at, earthquake.latitude, earthquake.longitude, earthquake.depth, earthquake.magnitude, earthquake.region]
        );
        console.log(`✅ 地震事件创建成功: ${earthquake.event_id}`);
      } catch (error: any) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`ℹ️  地震事件 ${earthquake.event_id} 已存在，跳过创建`);
        } else {
          throw error;
        }
      }
    }

    // 插入示例监测台站
    console.log('📡 创建示例监测台站...');
    for (const station of seedData.stations) {
      try {
        await dbHelper.run(
          'INSERT INTO stations (name, latitude, longitude, status, region) VALUES (?, ?, ?, ?, ?)',
          [station.name, station.latitude, station.longitude, station.status, station.region]
        );
        console.log(`✅ 监测台站创建成功: ${station.name}`);
      } catch (error: any) {
        if (error.message.includes('UNIQUE constraint failed')) {
          console.log(`ℹ️  监测台站 ${station.name} 已存在，跳过创建`);
        } else {
          throw error;
        }
      }
    }

    console.log('✅ 数据种子完成');
    
  } catch (error) {
    console.error('❌ 数据种子失败:', error);
    process.exit(1);
  } finally {
    await closeDatabase();
  }
}

// 如果直接运行此文件，执行种子
if (require.main === module) {
  seed();
}

export { seed };
