import { FastifyInstance, FastifyRequest, FastifyReply, FastifyError } from 'fastify';

// 错误处理插件
export async function errorHandlerPlugin(fastify: FastifyInstance) {
  
  // 全局错误处理器
  fastify.setErrorHandler(async (error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
    // 记录错误日志
    fastify.log.error({
      error: error.message,
      stack: error.stack,
      url: request.url,
      method: request.method,
      headers: request.headers,
      body: request.body
    }, '请求处理错误');

    // JWT 认证错误
    if (error.code === 'FST_JWT_NO_AUTHORIZATION_IN_HEADER') {
      return reply.status(401).send({
        success: false,
        error: '缺少授权头',
        code: 'MISSING_AUTH_HEADER'
      });
    }

    if (error.code === 'FST_JWT_AUTHORIZATION_TOKEN_INVALID') {
      return reply.status(401).send({
        success: false,
        error: '无效的访问令牌',
        code: 'INVALID_TOKEN'
      });
    }

    if (error.code === 'FST_JWT_AUTHORIZATION_TOKEN_EXPIRED') {
      return reply.status(401).send({
        success: false,
        error: '访问令牌已过期',
        code: 'TOKEN_EXPIRED'
      });
    }

    // 验证错误
    if (error.validation) {
      return reply.status(400).send({
        success: false,
        error: '请求参数验证失败',
        code: 'VALIDATION_ERROR',
        details: error.validation
      });
    }

    // 数据库错误
    if (error.message.includes('SQLITE_CONSTRAINT')) {
      return reply.status(409).send({
        success: false,
        error: '数据约束冲突',
        code: 'CONSTRAINT_ERROR'
      });
    }

    if (error.message.includes('SQLITE_BUSY')) {
      return reply.status(503).send({
        success: false,
        error: '数据库繁忙，请稍后重试',
        code: 'DATABASE_BUSY'
      });
    }

    // 文件系统错误
    if (error.code === 'ENOENT') {
      return reply.status(404).send({
        success: false,
        error: '文件或资源不存在',
        code: 'FILE_NOT_FOUND'
      });
    }

    if (error.code === 'EACCES') {
      return reply.status(403).send({
        success: false,
        error: '文件访问权限不足',
        code: 'ACCESS_DENIED'
      });
    }

    // 网络错误
    if (error.code === 'ECONNREFUSED') {
      return reply.status(503).send({
        success: false,
        error: '服务连接被拒绝',
        code: 'CONNECTION_REFUSED'
      });
    }

    if (error.code === 'ETIMEDOUT') {
      return reply.status(504).send({
        success: false,
        error: '请求超时',
        code: 'TIMEOUT'
      });
    }

    // 默认服务器错误
    const statusCode = error.statusCode || 500;
    
    // 生产环境不暴露详细错误信息
    const isDevelopment = process.env.NODE_ENV !== 'production';
    
    return reply.status(statusCode).send({
      success: false,
      error: isDevelopment ? error.message : '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR',
      ...(isDevelopment && { stack: error.stack })
    });
  });

  // 404 处理器
  fastify.setNotFoundHandler(async (request: FastifyRequest, reply: FastifyReply) => {
    return reply.status(404).send({
      success: false,
      error: `路由 ${request.method} ${request.url} 不存在`,
      code: 'ROUTE_NOT_FOUND'
    });
  });

  // 请求超时处理
  fastify.addHook('onTimeout', async (request: FastifyRequest, reply: FastifyReply) => {
    fastify.log.warn({
      url: request.url,
      method: request.method,
      headers: request.headers
    }, '请求超时');
  });

  // 响应序列化错误处理
  fastify.setSerializerCompiler(({ schema }) => {
    return (data) => {
      try {
        return JSON.stringify(data);
      } catch (error) {
        fastify.log.error('响应序列化失败:', error);
        return JSON.stringify({
          success: false,
          error: '响应数据序列化失败',
          code: 'SERIALIZATION_ERROR'
        });
      }
    };
  });
}
