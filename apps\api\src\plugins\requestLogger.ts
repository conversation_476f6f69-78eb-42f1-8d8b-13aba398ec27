import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';

// 请求日志插件
export async function requestLoggerPlugin(fastify: FastifyInstance) {

  // 请求开始时记录
  fastify.addHook('onRequest', async (request: FastifyRequest, reply: FastifyReply) => {
    const startTime = Date.now();

    // 将开始时间存储在请求上下文中
    (request as any).startTime = startTime;

    // 记录请求开始
    fastify.log.info({
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      timestamp: new Date().toISOString()
    }, '请求开始');
  });

  // 响应发送前记录
  fastify.addHook('onSend', async (request: FastifyRequest, reply: FastifyReply, payload) => {
    const startTime = (request as any).startTime;
    const duration = Date.now() - startTime;

    // 记录响应信息
    fastify.log.info({
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      duration: `${duration}ms`,
      contentLength: payload ? Buffer.byteLength(payload.toString()) : 0,
      timestamp: new Date().toISOString()
    }, '请求完成');

    return payload;
  });

  // 响应完成后记录
  fastify.addHook('onResponse', async (request: FastifyRequest, reply: FastifyReply) => {
    const startTime = (request as any).startTime;
    const duration = Date.now() - startTime;

    // 根据响应时间和状态码确定日志级别
    let logLevel = 'info';
    if (reply.statusCode >= 500) {
      logLevel = 'error';
    } else if (reply.statusCode >= 400) {
      logLevel = 'warn';
    } else if (duration > 1000) {
      logLevel = 'warn'; // 慢请求
    }

    const logData = {
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      duration: `${duration}ms`,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      timestamp: new Date().toISOString()
    };

    // 记录最终响应
    (fastify.log as any)[logLevel](logData, '响应发送完成');
  });

  // 错误请求记录
  fastify.addHook('onError', async (request: FastifyRequest, reply: FastifyReply, error) => {
    const startTime = (request as any).startTime;
    const duration = Date.now() - startTime;

    fastify.log.error({
      method: request.method,
      url: request.url,
      error: error.message,
      stack: error.stack,
      duration: `${duration}ms`,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
      timestamp: new Date().toISOString()
    }, '请求处理出错');
  });

  // 健康检查和静态资源不记录详细日志
  const skipLogging = ['/health', '/docs', '/favicon.ico'];

  fastify.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
    if (skipLogging.some(path => request.url.startsWith(path))) {
      // 为这些路径设置简化日志标记
      (request as any).skipDetailedLogging = true;
    }
  });
}
