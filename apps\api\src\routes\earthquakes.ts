import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DatabaseHelper } from '../db/database';
import { Earthquake, EarthquakeQuery, ApiResponse } from '../types';

// 地震事件查询参数 schema
const earthquakeQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 10000 },
    sort: { type: 'string', enum: ['occurred_at', 'magnitude', 'depth'], default: 'occurred_at' },
    order: { type: 'string', enum: ['asc', 'desc'], default: 'desc' },
    start_date: { type: 'string' },
    end_date: { type: 'string' },
    min_magnitude: { type: 'number' },
    max_magnitude: { type: 'number' },
    region: { type: 'string' }
  }
};

// 地震事件路由
export async function earthquakeRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 获取地震事件列表
  fastify.get<{
    Querystring: EarthquakeQuery;
    Reply: ApiResponse<{ earthquakes: Earthquake[]; total: number; page: number; limit: number }>;
  }>('/earthquakes', {
    schema: {
      description: '获取地震事件列表',
      tags: ['地震事件'],
      querystring: earthquakeQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                earthquakes: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      event_id: { type: 'string' },
                      occurred_at: { type: 'string' },
                      latitude: { type: 'number' },
                      longitude: { type: 'number' },
                      depth: { type: 'number' },
                      magnitude: { type: 'number' },
                      region: { type: 'string' },
                      created_at: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const {
        page = 1,
        limit,
        sort = 'occurred_at',
        order = 'desc',
        start_date,
        end_date,
        min_magnitude,
        max_magnitude,
        region
      } = request.query;

      // 构建查询条件
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (start_date) {
        whereClause += ' AND occurred_at >= ?';
        params.push(start_date);
      }

      if (end_date) {
        whereClause += ' AND occurred_at <= ?';
        params.push(end_date);
      }

      if (min_magnitude !== undefined) {
        whereClause += ' AND magnitude >= ?';
        params.push(min_magnitude);
      }

      if (max_magnitude !== undefined) {
        whereClause += ' AND magnitude <= ?';
        params.push(max_magnitude);
      }

      if (region) {
        whereClause += ' AND region = ?';
        params.push(region);
      }

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM earthquakes ${whereClause}`;
      const countResult = await dbHelper.get<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 获取数据
      let dataSql: string;
      let dataParams: any[];

      if (limit !== undefined) {
        // 有limit参数，使用分页
        const offset = (page - 1) * limit;
        dataSql = `
          SELECT * FROM earthquakes
          ${whereClause}
          ORDER BY ${sort} ${order.toUpperCase()}
          LIMIT ? OFFSET ?
        `;
        dataParams = [...params, limit, offset];
      } else {
        // 没有limit参数，返回所有数据
        dataSql = `
          SELECT * FROM earthquakes
          ${whereClause}
          ORDER BY ${sort} ${order.toUpperCase()}
        `;
        dataParams = params;
      }

      const earthquakes = await dbHelper.all<Earthquake>(dataSql, dataParams);

      reply.send({
        success: true,
        data: {
          earthquakes,
          total,
          page,
          limit: limit !== undefined ? limit : earthquakes.length
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取地震事件列表失败'
      });
    }
  });

  // 获取单个地震事件详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<Earthquake>;
  }>('/earthquakes/:id', {
    schema: {
      description: '获取地震事件详情',
      tags: ['地震事件'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;

      const earthquake = await dbHelper.get<Earthquake>(
        'SELECT * FROM earthquakes WHERE id = ? OR event_id = ?',
        [id, id]
      );

      if (!earthquake) {
        reply.status(404).send({
          success: false,
          error: '地震事件不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: earthquake
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取地震事件详情失败'
      });
    }
  });

  // 获取地震事件统计信息
  fastify.get('/earthquakes/stats', {
    schema: {
      description: '获取地震事件统计信息',
      tags: ['地震事件']
    }
  }, async (request, reply) => {
    try {
      // 总数统计
      const totalResult = await dbHelper.get<{ total: number }>(
        'SELECT COUNT(*) as total FROM earthquakes'
      );

      // 按震级分组统计
      const magnitudeStats = await dbHelper.all<{ magnitude_range: string; count: number }>(
        `SELECT 
          CASE 
            WHEN magnitude < 3.0 THEN '< 3.0'
            WHEN magnitude < 4.0 THEN '3.0 - 4.0'
            WHEN magnitude < 5.0 THEN '4.0 - 5.0'
            WHEN magnitude < 6.0 THEN '5.0 - 6.0'
            ELSE '>= 6.0'
          END as magnitude_range,
          COUNT(*) as count
        FROM earthquakes 
        GROUP BY magnitude_range
        ORDER BY MIN(magnitude)`
      );

      // 按区域统计
      const regionStats = await dbHelper.all<{ region: string; count: number }>(
        'SELECT region, COUNT(*) as count FROM earthquakes GROUP BY region ORDER BY count DESC'
      );

      // 最近7天统计
      const recentStats = await dbHelper.all<{ date: string; count: number }>(
        `SELECT 
          DATE(occurred_at) as date, 
          COUNT(*) as count 
        FROM earthquakes 
        WHERE occurred_at >= datetime('now', '-7 days')
        GROUP BY DATE(occurred_at) 
        ORDER BY date DESC`
      );

      reply.send({
        success: true,
        data: {
          total: totalResult?.total || 0,
          magnitudeStats,
          regionStats,
          recentStats
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取统计信息失败'
      });
    }
  });
}
