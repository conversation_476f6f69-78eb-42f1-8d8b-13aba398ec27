import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DatabaseHelper } from '../db/database';
import { Fault, PaginationParams, ApiResponse } from '../types';

// 断层查询参数 schema
const faultQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
    level: { type: 'number', enum: [1, 2, 3] },
    region: { type: 'string' }
  }
};

// 断层路由
export async function faultRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 获取断层列表
  fastify.get<{
    Querystring: PaginationParams & { level?: number; region?: string };
    Reply: ApiResponse<{ faults: Fault[]; total: number; page: number; limit: number }>;
  }>('/faults', {
    schema: {
      description: '获取断层列表',
      tags: ['断层数据'],
      querystring: faultQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                faults: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      name: { type: 'string' },
                      level: { type: 'number' },
                      coordinates: { type: 'string' },
                      region: { type: 'string' },
                      created_at: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { page = 1, limit = 20, level, region } = request.query;

      // 构建查询条件
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (level !== undefined) {
        whereClause += ' AND level = ?';
        params.push(level);
      }

      if (region) {
        whereClause += ' AND region = ?';
        params.push(region);
      }

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM faults ${whereClause}`;
      const countResult = await dbHelper.get<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 获取分页数据
      const offset = (page - 1) * limit;
      const dataSql = `
        SELECT * FROM faults 
        ${whereClause} 
        ORDER BY level ASC, name ASC 
        LIMIT ? OFFSET ?
      `;
      const faults = await dbHelper.all<Fault>(dataSql, [...params, limit, offset]);

      reply.send({
        success: true,
        data: {
          faults,
          total,
          page,
          limit
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取断层列表失败'
      });
    }
  });

  // 获取单个断层详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<Fault>;
  }>('/faults/:id', {
    schema: {
      description: '获取断层详情',
      tags: ['断层数据'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      
      const fault = await dbHelper.get<Fault>(
        'SELECT * FROM faults WHERE id = ?',
        [id]
      );

      if (!fault) {
        reply.status(404).send({
          success: false,
          error: '断层不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: fault
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取断层详情失败'
      });
    }
  });

  // 按等级获取断层数据（用于地图图层）
  fastify.get<{
    Params: { level: string };
    Reply: ApiResponse<Fault[]>;
  }>('/faults/level/:level', {
    schema: {
      description: '按等级获取断层数据',
      tags: ['断层数据'],
      params: {
        type: 'object',
        properties: {
          level: { type: 'string', enum: ['1', '2', '3'] }
        },
        required: ['level']
      }
    }
  }, async (request, reply) => {
    try {
      const { level } = request.params;
      
      const faults = await dbHelper.all<Fault>(
        'SELECT * FROM faults WHERE level = ? ORDER BY name ASC',
        [parseInt(level)]
      );

      reply.send({
        success: true,
        data: faults
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取断层数据失败'
      });
    }
  });

  // 获取断层统计信息
  fastify.get('/faults/stats', {
    schema: {
      description: '获取断层统计信息',
      tags: ['断层数据']
    }
  }, async (request, reply) => {
    try {
      // 总数统计
      const totalResult = await dbHelper.get<{ total: number }>(
        'SELECT COUNT(*) as total FROM faults'
      );

      // 按等级统计
      const levelStats = await dbHelper.all<{ level: number; count: number }>(
        'SELECT level, COUNT(*) as count FROM faults GROUP BY level ORDER BY level'
      );

      // 按区域统计
      const regionStats = await dbHelper.all<{ region: string; count: number }>(
        'SELECT region, COUNT(*) as count FROM faults GROUP BY region ORDER BY count DESC'
      );

      reply.send({
        success: true,
        data: {
          total: totalResult?.total || 0,
          levelStats,
          regionStats
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取统计信息失败'
      });
    }
  });
}
