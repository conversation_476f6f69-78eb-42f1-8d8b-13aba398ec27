import { FastifyInstance } from 'fastify';
import { DatabaseHelper } from '../db/database';
import type { ApiResponse, FocalMechanism, FocalMechanismQuery } from '../types';

const dbHelper = new DatabaseHelper();

// 震源机制解查询参数 schema
const focalMechanismQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 1000 },
    sort: { type: 'string', enum: ['date', 'magnitude', 'depth'], default: 'date' },
    order: { type: 'string', enum: ['asc', 'desc'], default: 'desc' },
    start_date: { type: 'string', format: 'date' },
    end_date: { type: 'string', format: 'date' },
    min_magnitude: { type: 'number' },
    max_magnitude: { type: 'number' },
    region: { type: 'string' }
  }
};

export default async function focalMechanismRoutes(fastify: FastifyInstance) {
  // 获取震源机制解列表
  fastify.get<{
    Querystring: FocalMechanismQuery;
    Reply: ApiResponse<{ focalMechanisms: FocalMechanism[]; total: number; page: number; limit: number }>;
  }>('/focal-mechanisms', {
    schema: {
      description: '获取震源机制解列表',
      tags: ['震源机制解'],
      querystring: focalMechanismQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                focalMechanisms: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      date: { type: 'string' },
                      time: { type: 'string' },
                      latitude: { type: 'number' },
                      longitude: { type: 'number' },
                      depth: { type: 'number' },
                      magnitude: { type: 'number' },
                      strike1: { type: 'number' },
                      dip1: { type: 'number' },
                      rake1: { type: 'number' },
                      strike2: { type: 'number' },
                      dip2: { type: 'number' },
                      rake2: { type: 'number' },
                      misfit: { type: 'number' },
                      mxx: { type: 'number' },
                      myy: { type: 'number' },
                      mzz: { type: 'number' },
                      mxy: { type: 'number' },
                      mxz: { type: 'number' },
                      myz: { type: 'number' },
                      region: { type: 'string' },
                      created_at: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const {
        page = 1,
        limit,
        sort = 'date',
        order = 'desc',
        start_date,
        end_date,
        min_magnitude,
        max_magnitude,
        region
      } = request.query;

      // 构建查询条件
      const conditions: string[] = [];
      const params: any[] = [];

      if (start_date) {
        conditions.push('date >= ?');
        params.push(start_date);
      }

      if (end_date) {
        conditions.push('date <= ?');
        params.push(end_date);
      }

      if (min_magnitude !== undefined) {
        conditions.push('magnitude >= ?');
        params.push(min_magnitude);
      }

      if (max_magnitude !== undefined) {
        conditions.push('magnitude <= ?');
        params.push(max_magnitude);
      }

      if (region) {
        conditions.push('region = ?');
        params.push(region);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // 获取总数
      const countQuery = `SELECT COUNT(*) as total FROM focal_mechanisms ${whereClause}`;
      const countResult = await dbHelper.get<{ total: number }>(countQuery, params);
      const total = countResult?.total || 0;

      // 获取数据（分页或全部）
      let dataQuery: string;
      let queryParams: any[];

      if (limit !== undefined) {
        // 使用分页
        const offset = (page - 1) * limit;
        dataQuery = `
          SELECT * FROM focal_mechanisms 
          ${whereClause}
          ORDER BY ${sort} ${order.toUpperCase()}
          LIMIT ? OFFSET ?
        `;
        queryParams = [...params, limit, offset];
      } else {
        // 返回所有数据
        dataQuery = `
          SELECT * FROM focal_mechanisms 
          ${whereClause}
          ORDER BY ${sort} ${order.toUpperCase()}
        `;
        queryParams = params;
      }

      const focalMechanisms = await dbHelper.all<FocalMechanism>(
        dataQuery,
        queryParams
      );

      reply.send({
        success: true,
        data: {
          focalMechanisms,
          total,
          page,
          limit: limit || focalMechanisms.length
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取震源机制解列表失败'
      });
    }
  });

  // 获取单个震源机制解详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<FocalMechanism>;
  }>('/focal-mechanisms/:id', {
    schema: {
      description: '获取震源机制解详情',
      tags: ['震源机制解'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;

      const focalMechanism = await dbHelper.get<FocalMechanism>(
        'SELECT * FROM focal_mechanisms WHERE id = ?',
        [id]
      );

      if (!focalMechanism) {
        reply.status(404).send({
          success: false,
          error: '震源机制解不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: focalMechanism
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取震源机制解详情失败'
      });
    }
  });

  // 获取震源机制解统计信息
  fastify.get<{
    Reply: ApiResponse<{
      total: number;
      byMagnitude: { magnitude_range: string; count: number }[];
      byRegion: { region: string; count: number }[];
    }>;
  }>('/focal-mechanisms/stats', {
    schema: {
      description: '获取震源机制解统计信息',
      tags: ['震源机制解']
    }
  }, async (request, reply) => {
    try {
      // 总数统计
      const totalResult = await dbHelper.get<{ total: number }>(
        'SELECT COUNT(*) as total FROM focal_mechanisms'
      );
      const total = totalResult?.total || 0;

      // 按震级分布统计
      const byMagnitude = await dbHelper.all<{ magnitude_range: string; count: number }>(
        `SELECT 
          CASE 
            WHEN magnitude < 1 THEN '<1.0'
            WHEN magnitude < 2 THEN '1.0-2.0'
            WHEN magnitude < 3 THEN '2.0-3.0'
            WHEN magnitude < 4 THEN '3.0-4.0'
            WHEN magnitude < 5 THEN '4.0-5.0'
            ELSE '≥5.0'
          END as magnitude_range,
          COUNT(*) as count
        FROM focal_mechanisms 
        GROUP BY magnitude_range 
        ORDER BY magnitude_range`
      );

      // 按区域分布统计
      const byRegion = await dbHelper.all<{ region: string; count: number }>(
        'SELECT region, COUNT(*) as count FROM focal_mechanisms GROUP BY region ORDER BY count DESC'
      );

      reply.send({
        success: true,
        data: {
          total,
          byMagnitude,
          byRegion
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取震源机制解统计信息失败'
      });
    }
  });
}
