import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DataImporter } from '../utils/dataImporter';
import { ApiResponse } from '../types';

// 数据导入路由（仅管理员可用）
export async function importRoutes(fastify: FastifyInstance) {

  // 批量导入所有数据
  fastify.post('/import/all', {
    schema: {
      description: '批量导入所有数据（管理员）',
      tags: ['数据导入'],
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const importer = new DataImporter();

      // 执行批量导入
      await importer.importAllData();

      reply.send({
        success: true,
        data: {
          message: '所有数据导入完成'
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '数据导入失败'
      });
    }
  });

  // 导入地震事件数据
  fastify.post<{
    Body: { filePath: string; region?: string };
    Reply: ApiResponse<{ count: number; message: string }>;
  }>('/import/earthquakes', {
    schema: {
      description: '导入地震事件数据（管理员）',
      tags: ['数据导入'],
      body: {
        type: 'object',
        required: ['filePath'],
        properties: {
          filePath: { type: 'string' },
          region: { type: 'string', default: '龙门山断裂带' }
        }
      },
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { filePath, region = '龙门山断裂带' } = request.body;
      const importer = new DataImporter();

      const count = await importer.importEarthquakes(filePath, region);

      reply.send({
        success: true,
        data: {
          count,
          message: `成功导入 ${count} 条地震事件数据`
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '地震事件数据导入失败'
      });
    }
  });

  // 导入断层数据
  fastify.post<{
    Body: { filePath: string; level: number; region?: string };
    Reply: ApiResponse<{ count: number; message: string }>;
  }>('/import/faults', {
    schema: {
      description: '导入断层数据（管理员）',
      tags: ['数据导入'],
      body: {
        type: 'object',
        required: ['filePath', 'level'],
        properties: {
          filePath: { type: 'string' },
          level: { type: 'number', enum: [1, 2, 3] },
          region: { type: 'string', default: '龙门山断裂带' }
        }
      },
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { filePath, level, region = '龙门山断裂带' } = request.body;
      const importer = new DataImporter();

      const count = await importer.importFaults(filePath, level, region);

      reply.send({
        success: true,
        data: {
          count,
          message: `成功导入 ${count} 条断层数据`
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '断层数据导入失败'
      });
    }
  });

  // 导入井轨迹数据
  fastify.post<{
    Body: { coordinatesDir: string; namesFile: string; region?: string };
    Reply: ApiResponse<{ count: number; message: string }>;
  }>('/import/wells', {
    schema: {
      description: '导入井轨迹数据（管理员）',
      tags: ['数据导入'],
      body: {
        type: 'object',
        required: ['coordinatesDir', 'namesFile'],
        properties: {
          coordinatesDir: { type: 'string' },
          namesFile: { type: 'string' },
          region: { type: 'string', default: '龙门山断裂带' }
        }
      },
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { coordinatesDir, namesFile, region = '龙门山断裂带' } = request.body;
      const importer = new DataImporter();

      const count = await importer.importWellTrajectories(coordinatesDir, namesFile, region);

      reply.send({
        success: true,
        data: {
          count,
          message: `成功导入 ${count} 条井轨迹数据`
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '井轨迹数据导入失败'
      });
    }
  });

  // 导入台站数据
  fastify.post<{
    Body: { filePath: string; region?: string };
    Reply: ApiResponse<{ count: number; message: string }>;
  }>('/import/stations', {
    schema: {
      description: '导入台站数据（管理员）',
      tags: ['数据导入'],
      body: {
        type: 'object',
        required: ['filePath'],
        properties: {
          filePath: { type: 'string' },
          region: { type: 'string', default: '龙门山断裂带' }
        }
      },
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { filePath, region = '龙门山断裂带' } = request.body;
      const importer = new DataImporter();

      const count = await importer.importStations(filePath, region);

      reply.send({
        success: true,
        data: {
          count,
          message: `成功导入 ${count} 条台站数据`
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '台站数据导入失败'
      });
    }
  });

  // 导入震源机制解数据
  fastify.post<{
    Body: { filePath: string; region?: string };
    Reply: ApiResponse<{ count: number; message: string }>;
  }>('/import/focal-mechanisms', {
    schema: {
      description: '导入震源机制解数据（管理员）',
      tags: ['数据导入'],
      security: [{ Bearer: [] }],
      body: {
        type: 'object',
        properties: {
          filePath: { type: 'string', description: 'CSV文件路径' },
          region: { type: 'string', description: '区域名称', default: '龙门山断裂带' }
        },
        required: ['filePath']
      }
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { filePath, region = '龙门山断裂带' } = request.body;
      const importer = new DataImporter();

      const count = await importer.importFocalMechanisms(filePath, region);

      reply.send({
        success: true,
        data: {
          count,
          message: `成功导入 ${count} 条震源机制解数据`
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '震源机制解数据导入失败'
      });
    }
  });

  // 获取数据导入状态
  fastify.get('/import/status', {
    schema: {
      description: '获取数据导入状态（管理员）',
      tags: ['数据导入'],
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      // 这里可以添加导入状态检查逻辑
      // 目前返回简单的状态信息

      reply.send({
        success: true,
        data: {
          status: 'ready',
          message: '数据导入服务就绪'
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取导入状态失败'
      });
    }
  });
}
