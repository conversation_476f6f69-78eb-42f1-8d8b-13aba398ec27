import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DatabaseHelper } from '../db/database';
import { WellPlatform, PaginationParams, ApiResponse } from '../types';

// 井平台查询参数 schema
const platformQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 10000, default: 1000 },
    region: { type: 'string' },
    name: { type: 'string' }
  }
};

// 井平台路由
export async function platformRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 获取井平台列表
  fastify.get<{
    Querystring: PaginationParams & { region?: string; name?: string };
    Reply: ApiResponse<{ platforms: WellPlatform[]; total: number; page: number; limit: number }>;
  }>('/platforms', {
    schema: {
      description: '获取井平台列表',
      tags: ['井平台数据'],
      querystring: platformQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                platforms: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      name: { type: 'string' },
                      latitude: { type: 'number' },
                      longitude: { type: 'number' },
                      region: { type: 'string' },
                      created_at: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { page = 1, limit = 1000, region, name } = request.query;

      // 构建查询条件
      const conditions: string[] = [];
      const params: any[] = [];

      if (region) {
        conditions.push('region = ?');
        params.push(region);
      }

      if (name) {
        conditions.push('name LIKE ?');
        params.push(`%${name}%`);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM well_platforms ${whereClause}`;
      const countResult = await dbHelper.get<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 获取分页数据
      const offset = (page - 1) * limit;
      const dataSql = `
        SELECT * FROM well_platforms 
        ${whereClause} 
        ORDER BY name ASC 
        LIMIT ? OFFSET ?
      `;
      const platforms = await dbHelper.all<WellPlatform>(dataSql, [...params, limit, offset]);

      reply.send({
        success: true,
        data: {
          platforms,
          total,
          page,
          limit
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取井平台列表失败'
      });
    }
  });

  // 获取单个井平台详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<WellPlatform>;
  }>('/platforms/:id', {
    schema: {
      description: '获取井平台详情',
      tags: ['井平台数据'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;

      const platform = await dbHelper.get<WellPlatform>(
        'SELECT * FROM well_platforms WHERE id = ?',
        [id]
      );

      if (!platform) {
        reply.status(404).send({
          success: false,
          error: '井平台不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: platform
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取井平台详情失败'
      });
    }
  });

  // 获取井平台关联的井轨迹
  fastify.get<{
    Params: { id: string };
    Querystring: PaginationParams;
    Reply: ApiResponse<{ trajectories: any[]; total: number; page: number; limit: number }>;
  }>('/platforms/:id/trajectories', {
    schema: {
      description: '获取井平台关联的井轨迹',
      tags: ['井平台数据'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'number', minimum: 1, default: 1 },
          limit: { type: 'number', minimum: 1, maximum: 1000, default: 100 }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      const { page = 1, limit = 100 } = request.query;

      // 检查井平台是否存在
      const platform = await dbHelper.get<WellPlatform>(
        'SELECT * FROM well_platforms WHERE id = ?',
        [id]
      );

      if (!platform) {
        reply.status(404).send({
          success: false,
          error: '井平台不存在'
        });
        return;
      }

      // 获取总数
      const countResult = await dbHelper.get<{ total: number }>(
        'SELECT COUNT(*) as total FROM well_trajectories WHERE platform_id = ?',
        [id]
      );
      const total = countResult?.total || 0;

      // 获取分页数据
      const offset = (page - 1) * limit;
      const trajectories = await dbHelper.all(
        `SELECT wt.*, wp.name as platform_name 
         FROM well_trajectories wt 
         JOIN well_platforms wp ON wt.platform_id = wp.id 
         WHERE wt.platform_id = ? 
         ORDER BY wt.name ASC 
         LIMIT ? OFFSET ?`,
        [id, limit, offset]
      );

      reply.send({
        success: true,
        data: {
          trajectories,
          total,
          page,
          limit
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取井平台轨迹失败'
      });
    }
  });
}
