import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DatabaseHelper } from '../db/database';
import { Station, PaginationParams, ApiResponse } from '../types';

// 台站查询参数 schema
const stationQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
    status: { type: 'string', enum: ['active', 'inactive', 'maintenance'] },
    region: { type: 'string' },
    name: { type: 'string' }
  }
};

// 台站路由
export async function stationRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 获取台站列表
  fastify.get<{
    Querystring: PaginationParams & { status?: string; region?: string; name?: string };
    Reply: ApiResponse<{ stations: Station[]; total: number; page: number; limit: number }>;
  }>('/stations', {
    schema: {
      description: '获取监测台站列表',
      tags: ['监测台站'],
      querystring: stationQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                stations: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      name: { type: 'string' },
                      latitude: { type: 'number' },
                      longitude: { type: 'number' },
                      status: { type: 'string' },
                      region: { type: 'string' },
                      created_at: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { page = 1, limit = 20, status, region, name } = request.query;

      // 构建查询条件
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (status) {
        whereClause += ' AND status = ?';
        params.push(status);
      }

      if (region) {
        whereClause += ' AND region = ?';
        params.push(region);
      }

      if (name) {
        whereClause += ' AND name LIKE ?';
        params.push(`%${name}%`);
      }

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM stations ${whereClause}`;
      const countResult = await dbHelper.get<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 获取分页数据
      const offset = (page - 1) * limit;
      const dataSql = `
        SELECT * FROM stations 
        ${whereClause} 
        ORDER BY name ASC 
        LIMIT ? OFFSET ?
      `;
      const stations = await dbHelper.all<Station>(dataSql, [...params, limit, offset]);

      reply.send({
        success: true,
        data: {
          stations,
          total,
          page,
          limit
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取台站列表失败'
      });
    }
  });

  // 获取单个台站详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<Station>;
  }>('/stations/:id', {
    schema: {
      description: '获取台站详情',
      tags: ['监测台站'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      
      const station = await dbHelper.get<Station>(
        'SELECT * FROM stations WHERE id = ?',
        [id]
      );

      if (!station) {
        reply.status(404).send({
          success: false,
          error: '台站不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: station
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取台站详情失败'
      });
    }
  });

  // 获取活跃台站（用于地图显示）
  fastify.get('/stations/active', {
    schema: {
      description: '获取活跃台站列表',
      tags: ['监测台站']
    }
  }, async (request, reply) => {
    try {
      const stations = await dbHelper.all<Station>(
        'SELECT * FROM stations WHERE status = ? ORDER BY name ASC',
        ['active']
      );

      reply.send({
        success: true,
        data: stations
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取活跃台站失败'
      });
    }
  });

  // 获取台站统计信息
  fastify.get('/stations/stats', {
    schema: {
      description: '获取台站统计信息',
      tags: ['监测台站']
    }
  }, async (request, reply) => {
    try {
      // 总数统计
      const totalResult = await dbHelper.get<{ total: number }>(
        'SELECT COUNT(*) as total FROM stations'
      );

      // 按状态统计
      const statusStats = await dbHelper.all<{ status: string; count: number }>(
        'SELECT status, COUNT(*) as count FROM stations GROUP BY status ORDER BY count DESC'
      );

      // 按区域统计
      const regionStats = await dbHelper.all<{ region: string; count: number }>(
        'SELECT region, COUNT(*) as count FROM stations GROUP BY region ORDER BY count DESC'
      );

      reply.send({
        success: true,
        data: {
          total: totalResult?.total || 0,
          statusStats,
          regionStats
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取统计信息失败'
      });
    }
  });
}
