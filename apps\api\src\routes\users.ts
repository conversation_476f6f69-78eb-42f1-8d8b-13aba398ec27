import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import bcrypt from 'bcryptjs';
import { DatabaseHelper } from '../db/database';
import { User, PaginationParams, ApiResponse } from '../types';

// 用户查询参数 schema
const userQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
    role: { type: 'string', enum: ['admin', 'user', 'viewer'] },
    email: { type: 'string' }
  }
};

// 用户更新 schema
const userUpdateSchema = {
  type: 'object',
  properties: {
    email: { type: 'string', format: 'email' },
    role: { type: 'string', enum: ['admin', 'user', 'viewer'] },
    password: { type: 'string', minLength: 6 }
  }
};

// 用户管理路由（需要管理员权限）
export async function userRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 获取用户列表
  fastify.get<{
    Querystring: PaginationParams & { role?: string; email?: string };
    Reply: ApiResponse<{ users: Omit<User, 'password_hash'>[]; total: number; page: number; limit: number }>;
  }>('/users', {
    schema: {
      description: '获取用户列表（管理员）',
      tags: ['用户管理'],
      querystring: userQuerySchema,
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { page = 1, limit = 20, role, email } = request.query;

      // 构建查询条件
      let whereClause = 'WHERE 1=1';
      const params: any[] = [];

      if (role) {
        whereClause += ' AND role = ?';
        params.push(role);
      }

      if (email) {
        whereClause += ' AND email LIKE ?';
        params.push(`%${email}%`);
      }

      // 获取总数
      const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
      const countResult = await dbHelper.get<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 获取分页数据
      const offset = (page - 1) * limit;
      const dataSql = `
        SELECT id, email, role, created_at FROM users 
        ${whereClause} 
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
      `;
      const users = await dbHelper.all<Omit<User, 'password_hash'>>(dataSql, [...params, limit, offset]);

      reply.send({
        success: true,
        data: {
          users,
          total,
          page,
          limit
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取用户列表失败'
      });
    }
  });

  // 获取单个用户详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<Omit<User, 'password_hash'>>;
  }>('/users/:id', {
    schema: {
      description: '获取用户详情（管理员）',
      tags: ['用户管理'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      
      const user = await dbHelper.get<User>(
        'SELECT id, email, role, created_at FROM users WHERE id = ?',
        [id]
      );

      if (!user) {
        reply.status(404).send({
          success: false,
          error: '用户不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: user
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取用户详情失败'
      });
    }
  });

  // 更新用户信息
  fastify.put<{
    Params: { id: string };
    Body: { email?: string; role?: string; password?: string };
    Reply: ApiResponse<Omit<User, 'password_hash'>>;
  }>('/users/:id', {
    schema: {
      description: '更新用户信息（管理员）',
      tags: ['用户管理'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      body: userUpdateSchema,
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { id } = request.params;
      const { email, role, password } = request.body;

      // 检查用户是否存在
      const existingUser = await dbHelper.get<User>(
        'SELECT * FROM users WHERE id = ?',
        [id]
      );

      if (!existingUser) {
        reply.status(404).send({
          success: false,
          error: '用户不存在'
        });
        return;
      }

      // 构建更新语句
      const updates: string[] = [];
      const params: any[] = [];

      if (email) {
        // 检查邮箱是否已被其他用户使用
        const emailExists = await dbHelper.get<User>(
          'SELECT id FROM users WHERE email = ? AND id != ?',
          [email, id]
        );

        if (emailExists) {
          reply.status(409).send({
            success: false,
            error: '该邮箱已被其他用户使用'
          });
          return;
        }

        updates.push('email = ?');
        params.push(email);
      }

      if (role) {
        updates.push('role = ?');
        params.push(role);
      }

      if (password) {
        const hashedPassword = await bcrypt.hash(password, 10);
        updates.push('password_hash = ?');
        params.push(hashedPassword);
      }

      if (updates.length === 0) {
        reply.status(400).send({
          success: false,
          error: '没有提供要更新的字段'
        });
        return;
      }

      // 执行更新
      params.push(id);
      await dbHelper.run(
        `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
        params
      );

      // 获取更新后的用户信息
      const updatedUser = await dbHelper.get<User>(
        'SELECT id, email, role, created_at FROM users WHERE id = ?',
        [id]
      );

      reply.send({
        success: true,
        data: updatedUser!
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '更新用户信息失败'
      });
    }
  });

  // 删除用户
  fastify.delete<{
    Params: { id: string };
    Reply: ApiResponse<{ message: string }>;
  }>('/users/:id', {
    schema: {
      description: '删除用户（管理员）',
      tags: ['用户管理'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      const { id } = request.params;

      // 检查用户是否存在
      const user = await dbHelper.get<User>(
        'SELECT * FROM users WHERE id = ?',
        [id]
      );

      if (!user) {
        reply.status(404).send({
          success: false,
          error: '用户不存在'
        });
        return;
      }

      // 不允许删除管理员账户
      if (user.role === 'admin') {
        reply.status(403).send({
          success: false,
          error: '不能删除管理员账户'
        });
        return;
      }

      // 删除用户
      await dbHelper.run('DELETE FROM users WHERE id = ?', [id]);

      reply.send({
        success: true,
        data: {
          message: '用户删除成功'
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '删除用户失败'
      });
    }
  });

  // 获取用户统计信息
  fastify.get('/users/stats', {
    schema: {
      description: '获取用户统计信息（管理员）',
      tags: ['用户管理'],
      security: [{ Bearer: [] }]
    },
    preHandler: fastify.requireAdmin
  }, async (request, reply) => {
    try {
      // 总数统计
      const totalResult = await dbHelper.get<{ total: number }>(
        'SELECT COUNT(*) as total FROM users'
      );

      // 按角色统计
      const roleStats = await dbHelper.all<{ role: string; count: number }>(
        'SELECT role, COUNT(*) as count FROM users GROUP BY role ORDER BY count DESC'
      );

      // 最近注册统计
      const recentStats = await dbHelper.all<{ date: string; count: number }>(
        `SELECT 
          DATE(created_at) as date, 
          COUNT(*) as count 
        FROM users 
        WHERE created_at >= datetime('now', '-30 days')
        GROUP BY DATE(created_at) 
        ORDER BY date DESC`
      );

      reply.send({
        success: true,
        data: {
          total: totalResult?.total || 0,
          roleStats,
          recentStats
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取统计信息失败'
      });
    }
  });
}
