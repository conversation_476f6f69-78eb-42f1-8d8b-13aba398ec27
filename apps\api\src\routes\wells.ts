import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { DatabaseHelper } from '../db/database';
import { WellTrajectory, WellPlatform, PaginationParams, ApiResponse } from '../types';

// 井轨迹查询参数 schema
const wellQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 10000, default: 1000 },
    region: { type: 'string' },
    platform_id: { type: 'number' },
    name: { type: 'string' }
  }
};

// 井轨迹路由
export async function wellRoutes(fastify: FastifyInstance) {
  const dbHelper = new DatabaseHelper();

  // 获取井轨迹列表
  fastify.get<{
    Querystring: PaginationParams & { region?: string; platform_id?: number; name?: string };
    Reply: ApiResponse<{ wells: WellTrajectory[]; total: number; page: number; limit: number }>;
  }>('/wells', {
    schema: {
      description: '获取井轨迹列表',
      tags: ['井轨迹数据'],
      querystring: wellQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                wells: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'number' },
                      name: { type: 'string' },
                      platform_id: { type: 'number' },
                      coordinates: { type: 'string' },
                      region: { type: 'string' },
                      created_at: { type: 'string' }
                    }
                  }
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { page = 1, limit = 1000, region, platform_id, name } = request.query;

      // 构建查询条件
      const conditions: string[] = [];
      const params: any[] = [];

      if (region) {
        conditions.push('wt.region = ?');
        params.push(region);
      }

      if (platform_id) {
        conditions.push('wt.platform_id = ?');
        params.push(platform_id);
      }

      if (name) {
        conditions.push('wt.name LIKE ?');
        params.push(`%${name}%`);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // 获取总数
      const countSql = `
        SELECT COUNT(*) as total
        FROM well_trajectories wt
        JOIN well_platforms wp ON wt.platform_id = wp.id
        ${whereClause}
      `;
      const countResult = await dbHelper.get<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 获取分页数据，只包含基本信息
      const offset = (page - 1) * limit;
      const dataSql = `
        SELECT wt.*
        FROM well_trajectories wt
        ${whereClause}
        ORDER BY wt.name ASC
        LIMIT ? OFFSET ?
      `;
      const wells = await dbHelper.all(dataSql, [...params, limit, offset]);

      reply.send({
        success: true,
        data: {
          wells,
          total,
          page,
          limit
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取井轨迹列表失败'
      });
    }
  });

  // 获取单个井轨迹详情
  fastify.get<{
    Params: { id: string };
    Reply: ApiResponse<WellTrajectory>;
  }>('/wells/:id', {
    schema: {
      description: '获取井轨迹详情',
      tags: ['井轨迹数据'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request, reply) => {
    try {
      const { id } = request.params;

      const well = await dbHelper.get(
        'SELECT * FROM well_trajectories WHERE id = ?',
        [id]
      );

      if (!well) {
        reply.status(404).send({
          success: false,
          error: '井轨迹不存在'
        });
        return;
      }

      reply.send({
        success: true,
        data: well
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取井轨迹详情失败'
      });
    }
  });

  // 获取井平台名称列表（用于筛选）
  fastify.get('/wells/platforms', {
    schema: {
      description: '获取井平台名称列表',
      tags: ['井轨迹数据']
    }
  }, async (request, reply) => {
    try {
      const platforms = await dbHelper.all<{ platform_name: string }>(
        'SELECT DISTINCT platform_name FROM well_trajectories ORDER BY platform_name ASC'
      );

      reply.send({
        success: true,
        data: platforms.map(p => p.platform_name)
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取井平台列表失败'
      });
    }
  });

  // 获取井轨迹统计信息
  fastify.get('/wells/stats', {
    schema: {
      description: '获取井轨迹统计信息',
      tags: ['井轨迹数据']
    }
  }, async (request, reply) => {
    try {
      // 总数统计
      const totalResult = await dbHelper.get<{ total: number }>(
        'SELECT COUNT(*) as total FROM well_trajectories'
      );

      // 按区域统计
      const regionStats = await dbHelper.all<{ region: string; count: number }>(
        'SELECT region, COUNT(*) as count FROM well_trajectories GROUP BY region ORDER BY count DESC'
      );

      reply.send({
        success: true,
        data: {
          total: totalResult?.total || 0,
          regionStats
        }
      });
    } catch (error) {
      fastify.log.error(error);
      reply.status(500).send({
        success: false,
        error: '获取统计信息失败'
      });
    }
  });
}
