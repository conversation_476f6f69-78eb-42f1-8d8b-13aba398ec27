import { initDatabase, DatabaseHelper, closeDatabase } from '../db/database';
import bcrypt from 'bcryptjs';

async function createTestUser() {
  try {
    await initDatabase();
    const dbHelper = new DatabaseHelper();

    // 检查是否已经有用户
    const existingUsers = await dbHelper.all('SELECT * FROM users');
    console.log('现有用户数量:', existingUsers.length);

    if (existingUsers.length > 0) {
      console.log('现有用户:');
      existingUsers.forEach((user: any) => {
        console.log(`- ID: ${user.id}, Email: ${user.email}, Role: ${user.role}`);
      });
    }

    // 创建测试用户
    const testEmail = '<EMAIL>';
    const testPassword = '123456';

    // 检查测试用户是否已存在
    const existingTestUser = await dbHelper.get(
      'SELECT * FROM users WHERE email = ?',
      [testEmail]
    );

    if (existingTestUser) {
      console.log('测试用户已存在:', testEmail);
    } else {
      // 创建测试用户
      const hashedPassword = await bcrypt.hash(testPassword, 10);
      
      const result = await dbHelper.run(
        'INSERT INTO users (email, password_hash, role) VALUES (?, ?, ?)',
        [testEmail, hashedPassword, 'user']
      );

      console.log('测试用户创建成功:');
      console.log(`- Email: ${testEmail}`);
      console.log(`- Password: ${testPassword}`);
      console.log(`- Role: user`);
      console.log(`- ID: ${result.lastID}`);
    }

    // 创建管理员用户
    const adminEmail = '<EMAIL>';
    const adminPassword = 'admin123';

    const existingAdmin = await dbHelper.get(
      'SELECT * FROM users WHERE email = ?',
      [adminEmail]
    );

    if (existingAdmin) {
      console.log('管理员用户已存在:', adminEmail);
    } else {
      const hashedAdminPassword = await bcrypt.hash(adminPassword, 10);
      
      const adminResult = await dbHelper.run(
        'INSERT INTO users (email, password_hash, role) VALUES (?, ?, ?)',
        [adminEmail, hashedAdminPassword, 'admin']
      );

      console.log('管理员用户创建成功:');
      console.log(`- Email: ${adminEmail}`);
      console.log(`- Password: ${adminPassword}`);
      console.log(`- Role: admin`);
      console.log(`- ID: ${adminResult.lastID}`);
    }

  } catch (error) {
    console.error('创建用户失败:', error);
  } finally {
    await closeDatabase();
  }
}

createTestUser();
