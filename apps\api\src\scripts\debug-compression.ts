#!/usr/bin/env tsx

/**
 * 调试压缩功能 - 详细检查哪些响应被压缩了
 */

import { fetch } from 'undici';

const API_BASE_URL = 'http://localhost:3001';

interface CompressionTestResult {
  endpoint: string;
  status: number;
  contentType: string;
  contentEncoding?: string;
  transferSize?: number;
  actualSize: number;
  compressionRatio?: number;
  shouldBeCompressed: boolean;
  isCompressed: boolean;
}

async function testEndpoint(endpoint: string): Promise<CompressionTestResult> {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept': 'application/json'
    }
  });

  const contentType = response.headers.get('content-type') || '';
  const contentEncoding = response.headers.get('content-encoding');
  const contentLength = response.headers.get('content-length');
  
  const responseText = await response.text();
  const actualSize = Buffer.byteLength(responseText, 'utf8');
  
  const shouldBeCompressed = actualSize > 1024 && (
    contentType.includes('application/json') ||
    contentType.includes('text/') ||
    contentType.includes('application/javascript') ||
    contentType.includes('application/xml')
  );

  const result: CompressionTestResult = {
    endpoint,
    status: response.status,
    contentType,
    contentEncoding: contentEncoding || undefined,
    transferSize: contentLength ? parseInt(contentLength) : undefined,
    actualSize,
    shouldBeCompressed,
    isCompressed: !!contentEncoding
  };

  if (result.transferSize && result.actualSize && result.isCompressed) {
    result.compressionRatio = ((result.actualSize - result.transferSize) / result.actualSize * 100);
  }

  return result;
}

async function debugCompression() {
  console.log('🔍 调试压缩功能...\n');

  const endpoints = [
    '/test-compression',
    '/api',
    '/health',
    '/api/earthquakes?limit=50',
    '/api/faults?limit=20',
    '/api/wells?limit=20',
    '/api/platforms?limit=20',
    '/api/stations?limit=20'
  ];

  const results: CompressionTestResult[] = [];

  for (const endpoint of endpoints) {
    try {
      console.log(`🧪 测试: ${endpoint}`);
      const result = await testEndpoint(endpoint);
      results.push(result);

      console.log(`   状态: ${result.status}`);
      console.log(`   内容类型: ${result.contentType}`);
      console.log(`   实际大小: ${result.actualSize} bytes`);
      console.log(`   传输大小: ${result.transferSize || '未知'} bytes`);
      console.log(`   内容编码: ${result.contentEncoding || '无压缩'}`);
      console.log(`   应该压缩: ${result.shouldBeCompressed ? '是' : '否'}`);
      console.log(`   实际压缩: ${result.isCompressed ? '是' : '否'}`);
      
      if (result.compressionRatio) {
        console.log(`   压缩率: ${result.compressionRatio.toFixed(2)}%`);
      }

      // 检查是否符合预期
      if (result.shouldBeCompressed && !result.isCompressed) {
        console.log('   ❌ 问题：应该压缩但未压缩');
      } else if (!result.shouldBeCompressed && result.isCompressed) {
        console.log('   ⚠️  注意：不应压缩但被压缩了');
      } else if (result.shouldBeCompressed && result.isCompressed) {
        console.log('   ✅ 正常：正确压缩');
      } else {
        console.log('   ✅ 正常：正确未压缩');
      }

      console.log('');
    } catch (error) {
      console.error(`   ❌ 错误: ${error}\n`);
    }
  }

  // 汇总分析
  console.log('📊 压缩分析汇总:');
  console.log('=' .repeat(60));
  
  const shouldCompress = results.filter(r => r.shouldBeCompressed);
  const actuallyCompressed = results.filter(r => r.isCompressed);
  const correctlyCompressed = results.filter(r => r.shouldBeCompressed && r.isCompressed);
  const incorrectlyNotCompressed = results.filter(r => r.shouldBeCompressed && !r.isCompressed);
  const incorrectlyCompressed = results.filter(r => !r.shouldBeCompressed && r.isCompressed);

  console.log(`总测试数: ${results.length}`);
  console.log(`应该压缩: ${shouldCompress.length}`);
  console.log(`实际压缩: ${actuallyCompressed.length}`);
  console.log(`正确压缩: ${correctlyCompressed.length}`);
  console.log(`应压缩未压缩: ${incorrectlyNotCompressed.length}`);
  console.log(`不应压缩但压缩: ${incorrectlyCompressed.length}`);

  if (incorrectlyNotCompressed.length > 0) {
    console.log('\n❌ 应该压缩但未压缩的端点:');
    incorrectlyNotCompressed.forEach(r => {
      console.log(`   ${r.endpoint} (${r.actualSize} bytes, ${r.contentType})`);
    });
  }

  if (incorrectlyCompressed.length > 0) {
    console.log('\n⚠️  不应压缩但被压缩的端点:');
    incorrectlyCompressed.forEach(r => {
      console.log(`   ${r.endpoint} (${r.actualSize} bytes, ${r.contentType})`);
    });
  }

  if (correctlyCompressed.length > 0) {
    const avgCompressionRatio = correctlyCompressed
      .filter(r => r.compressionRatio)
      .reduce((sum, r) => sum + (r.compressionRatio || 0), 0) / correctlyCompressed.length;
    
    console.log(`\n📈 平均压缩率: ${avgCompressionRatio.toFixed(2)}%`);
  }
}

debugCompression().catch(console.error);
