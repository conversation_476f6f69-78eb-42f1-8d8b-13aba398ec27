import { initDatabase, closeDatabase } from '../db/database';
import { DataImporter } from '../utils/dataImporter';
import { reset } from '../db/reset';
import * as path from 'path';
import * as fs from 'fs';

// 支持的数据类型
type DataType = 'earthquakes' | 'faults' | 'wells' | 'stations' | 'all';

interface ImportOptions {
  types: DataType[];
  resetDatabase?: boolean;
  resetTables?: DataType[];
}

// 解析命令行参数
function parseArgs(): ImportOptions {
  const args = process.argv.slice(2);
  const options: ImportOptions = {
    types: ['all'],
    resetDatabase: false,
    resetTables: []
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--reset-db':
        options.resetDatabase = true;
        break;
      case '--reset-tables':
        const tables = args[i + 1]?.split(',') as DataType[];
        if (tables) {
          options.resetTables = tables;
          i++; // 跳过下一个参数
        }
        break;
      case '--types':
        const types = args[i + 1]?.split(',') as DataType[];
        if (types) {
          options.types = types;
          i++; // 跳过下一个参数
        }
        break;
      case '--help':
        printHelp();
        process.exit(0);
        break;
    }
  }

  return options;
}

// 打印帮助信息
function printHelp() {
  console.log(`
数据导入脚本使用说明:

选项:
  --reset-db                    完全重置数据库（删除所有数据和表结构）
  --reset-tables <types>        重置指定表的数据（逗号分隔）
  --types <types>              指定要导入的数据类型（逗号分隔）
  --help                       显示此帮助信息

数据类型:
  earthquakes                  地震事件数据
  faults                       断层数据
  wells                        井轨迹数据
  stations                     监测台站数据
  all                          所有数据类型（默认）

示例:
  npm run import:data                                    # 重置数据库并导入所有数据
  npm run import:data -- --types wells                  # 只导入井轨迹数据
  npm run import:data -- --types wells,stations         # 导入井轨迹和台站数据
  npm run import:data -- --reset-tables wells --types wells  # 重置井轨迹表并重新导入
  npm run import:data -- --types earthquakes,faults     # 导入地震和断层数据
`);
}

// 选择性重置表数据
async function resetTables(types: DataType[], importer: DataImporter) {
  for (const type of types) {
    switch (type) {
      case 'earthquakes':
        console.log('�️ 清空地震事件数据...');
        await importer.resetTableData('earthquakes');
        break;
      case 'faults':
        console.log('🗑️ 清空断层数据...');
        await importer.resetTableData('faults');
        break;
      case 'wells':
        console.log('�️ 清空井轨迹数据...');
        await importer.resetTableData('wells');
        break;
      case 'stations':
        console.log('🗑️ 清空台站数据...');
        await importer.resetTableData('stations');
        break;
    }
  }
}

// 选择性导入数据
async function importSelectedData(types: DataType[], importer: DataImporter) {
  const stats = {
    earthquakes: 0,
    faults: 0,
    wells: 0,
    stations: 0
  };

  const shouldImport = (type: DataType) => types.includes('all') || types.includes(type);

  if (shouldImport('earthquakes')) {
    const earthquakeFile = path.join(importer.dataDirectory, '地震事件点数据', 'all_region平台内事件目录.csv');
    if (fs.existsSync(earthquakeFile)) {
      stats.earthquakes = await importer.importEarthquakes(earthquakeFile);
    }
  }

  if (shouldImport('faults')) {
    for (let level = 1; level <= 3; level++) {
      const faultFile = path.join(importer.dataDirectory, '断层数据', `LX_fault${level}.txt`);
      if (fs.existsSync(faultFile)) {
        stats.faults += await importer.importFaults(faultFile, level);
      }
    }
  }

  if (shouldImport('wells')) {
    const wellTrajectoryFile = path.join(importer.dataDirectory, '井轨迹数据', '井轨迹.txt');
    const wellNamesFile = path.join(importer.dataDirectory, '井轨迹数据', '井平台名称.txt');
    if (fs.existsSync(wellTrajectoryFile) && fs.existsSync(wellNamesFile)) {
      stats.wells = await importer.importWellTrajectories(wellTrajectoryFile, wellNamesFile);
    }
  }

  if (shouldImport('stations')) {
    const stationFile = path.join(importer.dataDirectory, '台站位置', 'station.dat');
    if (fs.existsSync(stationFile)) {
      stats.stations = await importer.importStations(stationFile);
    }
  }

  return stats;
}

// 数据导入脚本
async function importData() {
  try {
    const options = parseArgs();

    if (options.resetDatabase) {
      console.log('🔄 重置数据库...');
      await reset();
    }

    console.log('� 初始化数据库连接...');
    await initDatabase();

    const importer = new DataImporter();

    // 选择性重置表
    if (options.resetTables && options.resetTables.length > 0) {
      await resetTables(options.resetTables, importer);
    }

    console.log('📦 开始数据导入...');
    console.log(`📋 导入类型: ${options.types.join(', ')}`);

    // 选择性导入数据
    const stats = await importSelectedData(options.types, importer);

    console.log('✅ 数据导入完成！');
    console.log('📈 导入统计:');
    if (stats.earthquakes > 0) console.log(`   - 地震事件: ${stats.earthquakes} 条`);
    if (stats.faults > 0) console.log(`   - 断层数据: ${stats.faults} 条`);
    if (stats.wells > 0) console.log(`   - 井轨迹: ${stats.wells} 条`);
    if (stats.stations > 0) console.log(`   - 监测台站: ${stats.stations} 条`);

  } catch (error) {
    console.error('❌ 数据导入失败:', error);
    process.exit(1);
  } finally {
    await closeDatabase();
  }
}

// 如果直接运行此文件，执行导入
if (require.main === module) {
  importData();
}

export { importData };
