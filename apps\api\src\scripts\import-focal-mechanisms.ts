import { initDatabase, closeDatabase } from '../db/database';
import { DataImporter } from '../utils/dataImporter';
import path from 'path';
import fs from 'fs';

async function importFocalMechanisms() {
  try {
    console.log('🌍 开始导入震源机制解数据...');

    // 初始化数据库
    await initDatabase();

    // 创建数据导入器
    const importer = new DataImporter();

    // 震源机制解数据文件路径 - 更新为新的真实数据文件
    const focalMechanismFile = path.join(process.cwd(), '../../data/震源机制解/All_Focal_Mechanism_Results.csv');

    console.log(`📂 数据文件路径: ${focalMechanismFile}`);

    // 检查文件是否存在
    if (!fs.existsSync(focalMechanismFile)) {
      console.error(`❌ 文件不存在: ${focalMechanismFile}`);
      process.exit(1);
    }

    // 导入震源机制解数据
    const count = await importer.importFocalMechanisms(focalMechanismFile, '龙门山断裂带');

    console.log(`✅ 震源机制解数据导入完成，共导入 ${count} 条记录`);

  } catch (error) {
    console.error('❌ 震源机制解数据导入失败:', error);
    process.exit(1);
  } finally {
    // 关闭数据库连接
    await closeDatabase();
  }
}

// 运行导入
importFocalMechanisms();
