import { initDatabase, DatabaseHelper, closeDatabase } from '../db/database';
import { DataImporter } from '../utils/dataImporter';

// 重新导入井轨迹数据脚本
async function reimportWells() {
  console.log('🔄 开始重新导入井轨迹数据...');

  try {
    await initDatabase();
    const dbHelper = new DatabaseHelper();

    // 1. 清空现有数据
    console.log('🗑️ 清空现有井轨迹和井平台数据...');
    // 临时禁用外键约束
    await dbHelper.run('PRAGMA foreign_keys = OFF');
    await dbHelper.run('DELETE FROM well_trajectories');
    await dbHelper.run('DELETE FROM well_platforms');
    // 重新启用外键约束
    await dbHelper.run('PRAGMA foreign_keys = ON');
    console.log('✅ 数据清空完成');

    // 2. 重新导入数据
    console.log('📥 重新导入井轨迹数据...');
    const importer = new DataImporter();

    const trajectoryFile = 'd:\\workspace\\rise-map-fullsatck\\data\\井轨迹数据\\井轨迹.txt';
    const namesFile = 'd:\\workspace\\rise-map-fullsatck\\data\\井轨迹数据\\井平台名称.txt';
    const region = '龙门山断裂带';

    const count = await importer.importWellTrajectories(trajectoryFile, namesFile, region);

    console.log(`✅ 成功重新导入 ${count} 条井轨迹数据`);

    // 3. 检查结果
    const trajectoryCount = await dbHelper.get<{ count: number }>('SELECT COUNT(*) as count FROM well_trajectories');
    const platformCount = await dbHelper.get<{ count: number }>('SELECT COUNT(*) as count FROM well_platforms');

    console.log(`📊 井轨迹数量: ${trajectoryCount?.count || 0}`);
    console.log(`📊 井平台数量: ${platformCount?.count || 0}`);

    // 4. 显示一些示例数据
    console.log('\n📋 井平台示例数据:');
    const platforms = await dbHelper.all('SELECT * FROM well_platforms LIMIT 5');
    platforms?.forEach(platform => {
      console.log(`  - ${platform.name}: (${platform.longitude}, ${platform.latitude})`);
    });

    console.log('\n📋 井轨迹示例数据:');
    const trajectories = await dbHelper.all('SELECT * FROM well_trajectories LIMIT 5');
    trajectories?.forEach(trajectory => {
      console.log(`  - ${trajectory.name}: 平台ID ${trajectory.platform_id}`);
    });

  } catch (error) {
    console.error('❌ 重新导入失败:', error);
    throw error;
  } finally {
    await closeDatabase();
    console.log('✅ 数据库连接已关闭');
  }
}

// 如果直接运行此文件，执行重新导入
if (require.main === module) {
  reimportWells();
}

export { reimportWells };
