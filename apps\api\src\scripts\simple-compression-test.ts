#!/usr/bin/env tsx

/**
 * 简单的压缩测试
 */

import { fetch } from 'undici';

const API_BASE_URL = 'http://localhost:3001';

async function testCompression() {
  console.log('🧪 简单压缩测试...\n');

  // 测试大响应端点
  console.log('1. 测试大响应端点 (/test-compression)');
  try {
    const response = await fetch(`${API_BASE_URL}/test-compression`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'application/json'
      }
    });

    console.log(`   状态码: ${response.status}`);
    console.log(`   内容类型: ${response.headers.get('content-type')}`);
    console.log(`   内容编码: ${response.headers.get('content-encoding') || '无压缩'}`);
    console.log(`   传输大小: ${response.headers.get('content-length') || '未知'} bytes`);
    
    const data = await response.text();
    console.log(`   实际大小: ${Buffer.byteLength(data, 'utf8')} bytes`);
    
    if (response.headers.get('content-encoding')) {
      console.log('   ✅ 大响应被正确压缩');
    } else {
      console.log('   ❌ 大响应未被压缩');
    }
  } catch (error) {
    console.error(`   ❌ 错误: ${error}`);
  }

  console.log('');

  // 测试真实API端点
  console.log('2. 测试地震数据端点 (/api/earthquakes?limit=10)');
  try {
    const response = await fetch(`${API_BASE_URL}/api/earthquakes?limit=10`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'application/json'
      }
    });

    console.log(`   状态码: ${response.status}`);
    console.log(`   内容类型: ${response.headers.get('content-type')}`);
    console.log(`   内容编码: ${response.headers.get('content-encoding') || '无压缩'}`);
    console.log(`   传输大小: ${response.headers.get('content-length') || '未知'} bytes`);
    
    const data = await response.text();
    const actualSize = Buffer.byteLength(data, 'utf8');
    console.log(`   实际大小: ${actualSize} bytes`);
    
    if (actualSize >= 1024) {
      if (response.headers.get('content-encoding')) {
        console.log('   ✅ API响应被正确压缩');
      } else {
        console.log('   ❌ API响应应该被压缩但未被压缩');
      }
    } else {
      console.log('   ℹ️  响应小于1KB，符合不压缩的预期');
    }
  } catch (error) {
    console.error(`   ❌ 错误: ${error}`);
  }

  console.log('');

  // 测试不带压缩头的请求
  console.log('3. 测试不支持压缩的客户端');
  try {
    const response = await fetch(`${API_BASE_URL}/test-compression`, {
      headers: {
        'Accept': 'application/json'
        // 不发送 Accept-Encoding 头
      }
    });

    console.log(`   内容编码: ${response.headers.get('content-encoding') || '无压缩'}`);
    
    if (!response.headers.get('content-encoding')) {
      console.log('   ✅ 正确：不支持压缩的客户端收到未压缩响应');
    } else {
      console.log('   ⚠️  注意：客户端不支持压缩但收到了压缩响应');
    }
  } catch (error) {
    console.error(`   ❌ 错误: ${error}`);
  }

  console.log('\n🎉 压缩测试完成！');
}

testCompression().catch(console.error);
