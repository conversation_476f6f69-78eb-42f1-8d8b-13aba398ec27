#!/usr/bin/env tsx

/**
 * 测试API服务器的压缩功能
 * 检查响应是否正确压缩
 */

import { fetch } from 'undici';

const API_BASE_URL = process.env.API_URL || 'http://localhost:3001';

interface CompressionTestResult {
  endpoint: string;
  status: number;
  contentEncoding?: string;
  contentLength?: number;
  uncompressedSize?: number;
  compressionRatio?: number;
  success: boolean;
  error?: string;
}

async function testEndpointCompression(endpoint: string): Promise<CompressionTestResult> {
  try {
    console.log(`\n🧪 测试端点: ${endpoint}`);
    
    // 发送支持压缩的请求
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'application/json'
      }
    });

    const contentEncoding = response.headers.get('content-encoding');
    const contentLength = response.headers.get('content-length');
    
    // 获取响应内容
    const responseText = await response.text();
    const uncompressedSize = Buffer.byteLength(responseText, 'utf8');
    
    const result: CompressionTestResult = {
      endpoint,
      status: response.status,
      contentEncoding: contentEncoding || undefined,
      contentLength: contentLength ? parseInt(contentLength) : undefined,
      uncompressedSize,
      success: response.ok
    };

    if (result.contentLength && result.uncompressedSize) {
      result.compressionRatio = ((result.uncompressedSize - result.contentLength) / result.uncompressedSize * 100);
    }

    // 输出结果
    console.log(`   状态码: ${result.status}`);
    console.log(`   内容编码: ${result.contentEncoding || '无压缩'}`);
    console.log(`   压缩后大小: ${result.contentLength || '未知'} bytes`);
    console.log(`   原始大小: ${result.uncompressedSize} bytes`);
    
    if (result.compressionRatio) {
      console.log(`   压缩率: ${result.compressionRatio.toFixed(2)}%`);
    }

    return result;

  } catch (error) {
    console.error(`   ❌ 错误: ${error}`);
    return {
      endpoint,
      status: 0,
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

async function testStaticFileCompression(): Promise<void> {
  console.log('\n📁 测试静态文件压缩...');
  
  try {
    // 测试主页
    const response = await fetch(`${API_BASE_URL}/`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br'
      }
    });

    const contentEncoding = response.headers.get('content-encoding');
    const contentType = response.headers.get('content-type');
    
    console.log(`   状态码: ${response.status}`);
    console.log(`   内容类型: ${contentType}`);
    console.log(`   内容编码: ${contentEncoding || '无压缩'}`);
    
  } catch (error) {
    console.error(`   ❌ 静态文件测试失败: ${error}`);
  }
}

async function main() {
  console.log('🚀 开始测试API服务器压缩功能...');
  console.log(`📍 API地址: ${API_BASE_URL}`);

  // 测试的API端点
  const endpoints = [
    '/api',
    '/health',
    '/api/earthquakes?limit=10',
    '/api/faults?limit=5',
    '/api/wells?limit=5',
    '/api/platforms?limit=5',
    '/api/stations?limit=5'
  ];

  const results: CompressionTestResult[] = [];

  // 测试API端点
  for (const endpoint of endpoints) {
    const result = await testEndpointCompression(endpoint);
    results.push(result);
  }

  // 测试静态文件
  await testStaticFileCompression();

  // 汇总结果
  console.log('\n📊 压缩测试汇总:');
  console.log('=' .repeat(80));
  
  const successfulTests = results.filter(r => r.success);
  const compressedResponses = results.filter(r => r.contentEncoding);
  
  console.log(`✅ 成功请求: ${successfulTests.length}/${results.length}`);
  console.log(`🗜️  压缩响应: ${compressedResponses.length}/${results.length}`);
  
  if (compressedResponses.length > 0) {
    const avgCompressionRatio = compressedResponses
      .filter(r => r.compressionRatio)
      .reduce((sum, r) => sum + (r.compressionRatio || 0), 0) / compressedResponses.length;
    
    console.log(`📈 平均压缩率: ${avgCompressionRatio.toFixed(2)}%`);
  }

  // 显示详细结果
  console.log('\n📋 详细结果:');
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const compression = result.contentEncoding ? `[${result.contentEncoding}]` : '[无压缩]';
    const ratio = result.compressionRatio ? ` (${result.compressionRatio.toFixed(1)}%)` : '';
    
    console.log(`${status} ${result.endpoint} ${compression}${ratio}`);
  });

  console.log('\n🎉 压缩测试完成!');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}
