#!/usr/bin/env tsx

/**
 * 测试大响应的压缩功能
 */

import { fetch } from 'undici';

const API_BASE_URL = 'http://localhost:3001';

async function testLargeResponseCompression() {
  console.log('🧪 测试大响应压缩功能...\n');

  try {
    // 测试专门的压缩测试端点
    console.log('📊 测试压缩测试端点 (/test-compression)...');
    const response = await fetch(`${API_BASE_URL}/test-compression`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'application/json'
      }
    });

    const contentEncoding = response.headers.get('content-encoding');
    const contentLength = response.headers.get('content-length');
    const contentType = response.headers.get('content-type');
    
    // 获取响应内容
    const responseText = await response.text();
    const uncompressedSize = Buffer.byteLength(responseText, 'utf8');
    
    console.log(`   状态码: ${response.status}`);
    console.log(`   内容类型: ${contentType}`);
    console.log(`   内容编码: ${contentEncoding || '无压缩'}`);
    console.log(`   传输大小: ${contentLength || '未知'} bytes`);
    console.log(`   实际大小: ${uncompressedSize} bytes`);
    
    if (contentLength && contentEncoding) {
      const compressionRatio = ((uncompressedSize - parseInt(contentLength)) / uncompressedSize * 100);
      console.log(`   压缩率: ${compressionRatio.toFixed(2)}%`);
    }

    if (uncompressedSize > 1024) {
      if (contentEncoding) {
        console.log('✅ 大响应压缩正常工作！');
      } else {
        console.log('❌ 大响应未被压缩！');
        console.log('🔍 可能的原因:');
        console.log('   - 压缩配置问题');
        console.log('   - 内容类型不支持压缩');
        console.log('   - 客户端未发送正确的Accept-Encoding头');
      }
    } else {
      console.log('⚠️  响应大小小于1KB，可能不会被压缩');
    }

    // 测试地震数据端点（通常有较多数据）
    console.log('\n📊 测试地震数据端点 (/api/earthquakes)...');
    const earthquakeResponse = await fetch(`${API_BASE_URL}/api/earthquakes?limit=50`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'application/json'
      }
    });

    const eqContentEncoding = earthquakeResponse.headers.get('content-encoding');
    const eqContentLength = earthquakeResponse.headers.get('content-length');
    const eqResponseText = await earthquakeResponse.text();
    const eqUncompressedSize = Buffer.byteLength(eqResponseText, 'utf8');

    console.log(`   状态码: ${earthquakeResponse.status}`);
    console.log(`   内容编码: ${eqContentEncoding || '无压缩'}`);
    console.log(`   传输大小: ${eqContentLength || '未知'} bytes`);
    console.log(`   实际大小: ${eqUncompressedSize} bytes`);

    if (eqUncompressedSize > 1024) {
      if (eqContentEncoding) {
        console.log('✅ 地震数据压缩正常！');
      } else {
        console.log('❌ 地震数据未被压缩！');
      }
    }

    // 测试不带Accept-Encoding头的请求
    console.log('\n📊 测试不带压缩头的请求...');
    const noCompressionResponse = await fetch(`${API_BASE_URL}/test-compression`, {
      headers: {
        'Accept': 'application/json'
      }
    });

    const noCompContentEncoding = noCompressionResponse.headers.get('content-encoding');
    console.log(`   内容编码: ${noCompContentEncoding || '无压缩'}`);
    
    if (!noCompContentEncoding) {
      console.log('✅ 正确：不支持压缩的客户端不会收到压缩响应');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

async function testCompressionSettings() {
  console.log('\n🔧 测试不同压缩设置...');
  
  const testCases = [
    { encoding: 'gzip', name: 'Gzip压缩' },
    { encoding: 'deflate', name: 'Deflate压缩' },
    { encoding: 'br', name: 'Brotli压缩' },
    { encoding: 'gzip, deflate, br', name: '所有压缩算法' }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`\n   测试 ${testCase.name}...`);
      const response = await fetch(`${API_BASE_URL}/test-compression`, {
        headers: {
          'Accept-Encoding': testCase.encoding,
          'Accept': 'application/json'
        }
      });

      const contentEncoding = response.headers.get('content-encoding');
      console.log(`     请求编码: ${testCase.encoding}`);
      console.log(`     响应编码: ${contentEncoding || '无压缩'}`);
      
      if (contentEncoding) {
        console.log(`     ✅ ${testCase.name} 工作正常`);
      } else {
        console.log(`     ❌ ${testCase.name} 未生效`);
      }
    } catch (error) {
      console.log(`     ❌ ${testCase.name} 测试失败: ${error}`);
    }
  }
}

async function main() {
  console.log('🚀 开始测试大响应压缩功能...');
  console.log(`📍 API地址: ${API_BASE_URL}\n`);

  await testLargeResponseCompression();
  await testCompressionSettings();

  console.log('\n🎉 大响应压缩测试完成！');
}

main().catch(console.error);
