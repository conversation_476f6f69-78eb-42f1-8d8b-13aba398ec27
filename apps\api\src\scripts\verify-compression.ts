#!/usr/bin/env tsx

/**
 * 简单验证压缩功能的脚本
 */

import { fetch } from 'undici';

const API_BASE_URL = 'http://localhost:3001';

async function testCompression() {
  console.log('🧪 验证API压缩功能...\n');

  try {
    // 测试API端点
    const response = await fetch(`${API_BASE_URL}/api/earthquakes?limit=10`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'application/json'
      }
    });

    console.log('📊 响应头信息:');
    console.log(`   状态码: ${response.status}`);
    console.log(`   内容类型: ${response.headers.get('content-type')}`);
    console.log(`   内容编码: ${response.headers.get('content-encoding') || '无压缩'}`);
    console.log(`   内容长度: ${response.headers.get('content-length') || '未知'} bytes`);
    
    const data = await response.text();
    console.log(`   实际内容大小: ${Buffer.byteLength(data, 'utf8')} bytes`);
    
    if (response.headers.get('content-encoding')) {
      console.log('✅ 压缩功能正常工作！');
    } else {
      console.log('⚠️  响应未被压缩（可能因为内容太小）');
    }

    // 测试静态文件
    console.log('\n📁 测试静态文件压缩...');
    const staticResponse = await fetch(`${API_BASE_URL}/`, {
      headers: {
        'Accept-Encoding': 'gzip, deflate, br'
      }
    });

    console.log(`   状态码: ${staticResponse.status}`);
    console.log(`   内容类型: ${staticResponse.headers.get('content-type')}`);
    console.log(`   内容编码: ${staticResponse.headers.get('content-encoding') || '无压缩'}`);
    
    if (staticResponse.headers.get('content-encoding')) {
      console.log('✅ 静态文件压缩正常工作！');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testCompression();
