import Fastify from 'fastify';
import cors from '@fastify/cors';
import jwt from '@fastify/jwt';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import fastifyStatic from '@fastify/static';
import path from 'path';
import { initDatabase } from './db/database';
import { swaggerConfig, swaggerUiConfig } from './config/swagger';

// 创建 Fastify 实例
const fastify = Fastify({
  logger: {
    level: 'info'
  }
});

// 注册插件
async function registerPlugins() {
  // CORS 支持
  const corsOrigin = process.env.CORS_ORIGIN
    ? process.env.CORS_ORIGIN.split(',').map(origin => origin.trim())
    : ['http://localhost:3000', 'https://rise-map.cosfoc.com'];

  await fastify.register(cors, {
    origin: corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
  });

  // JWT 认证
  await fastify.register(jwt, {
    secret: process.env.JWT_SECRET || 'rise-map-secret-key-change-in-production'
  });

  // 错误处理插件
  const { errorHandlerPlugin } = await import('./plugins/errorHandler');
  await fastify.register(errorHandlerPlugin);

  // 请求日志插件
  const { requestLoggerPlugin } = await import('./plugins/requestLogger');
  await fastify.register(requestLoggerPlugin);

  // 认证插件
  const { authPlugin } = await import('./plugins/auth');
  await fastify.register(authPlugin);

  // Swagger 文档
  await fastify.register(swagger, swaggerConfig);
  await fastify.register(swaggerUi, swaggerUiConfig);

  // 静态文件服务 - 托管前端构建文件
  const staticPath = process.env.STATIC_PATH
    ? path.resolve(process.env.STATIC_PATH)
    : path.join(__dirname, '../../web/dist');

  fastify.log.info(`静态文件路径: ${staticPath}`);

  await fastify.register(fastifyStatic, {
    root: staticPath,
    prefix: '/',
    redirect: true
  });
}

// 注册路由
async function registerRoutes() {
  // 导入路由模块
  const { earthquakeRoutes } = await import('./routes/earthquakes');
  const { faultRoutes } = await import('./routes/faults');
  const { wellRoutes } = await import('./routes/wells');
  const { platformRoutes } = await import('./routes/platforms');
  const { stationRoutes } = await import('./routes/stations');
  const { regionRoutes } = await import('./routes/regions');
  const { authRoutes } = await import('./routes/auth');
  const { userRoutes } = await import('./routes/users');
  const { importRoutes } = await import('./routes/import');
  const { default: focalMechanismRoutes } = await import('./routes/focalMechanisms');

  // 健康检查
  fastify.get('/health', async () => {
    return { status: 'ok', timestamp: new Date().toISOString() };
  });

  // API 根路径
  fastify.get('/api', async () => {
    return {
      message: 'RiseMap API 服务运行中',
      version: '1.0.0',
      docs: '/docs'
    };
  });

  // 注册 API 路由
  fastify.register(async function (fastify) {
    // 地震事件路由
    await fastify.register(earthquakeRoutes, { prefix: '/api' });

    // 断层数据路由
    await fastify.register(faultRoutes, { prefix: '/api' });

    // 井轨迹路由
    await fastify.register(wellRoutes, { prefix: '/api' });

    // 井平台路由
    await fastify.register(platformRoutes, { prefix: '/api' });

    // 监测台站路由
    await fastify.register(stationRoutes, { prefix: '/api' });

    // 区域管理路由
    await fastify.register(regionRoutes, { prefix: '/api' });

    // 用户认证路由
    await fastify.register(authRoutes, { prefix: '/api' });

    // 用户管理路由
    await fastify.register(userRoutes, { prefix: '/api' });

    // 数据导入路由
    await fastify.register(importRoutes, { prefix: '/api' });

    // 震源机制解路由
    await fastify.register(focalMechanismRoutes, { prefix: '/api' });
  });

  // 🎉 不需要手动SPA fallback了！
  // 我们的智能Vite插件已经为每个路由生成了对应的HTML文件
  // 例如：/map -> /map/index.html, /login -> /login/index.html
  // 静态文件服务器会自动处理这些路由
}

// 启动服务器
async function start() {
  try {
    // 初始化数据库
    await initDatabase();

    // 注册插件
    await registerPlugins();

    // 注册路由
    await registerRoutes();

    // 启动服务器
    const port = parseInt(process.env.PORT || '3001');
    const host = process.env.HOST || '0.0.0.0';

    await fastify.listen({ port, host });

    console.log(`🚀 RiseMap API 服务已启动`);
    console.log(`📍 服务地址: http://localhost:${port}`);
    console.log(`📚 API 文档: http://localhost:${port}/docs`);

  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n正在关闭服务器...');
  await fastify.close();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n正在关闭服务器...');
  await fastify.close();
  process.exit(0);
});

// 启动应用
start();
