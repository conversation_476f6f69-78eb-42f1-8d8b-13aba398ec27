// 地震事件数据类型
export interface Earthquake {
  id: number;
  event_id: string;
  occurred_at: string;
  latitude: number;
  longitude: number;
  depth: number;
  magnitude: number;
  region: string;
  created_at: string;
}

// 断层数据类型
export interface Fault {
  id: number;
  name: string;
  level: number;
  coordinates: string; // JSON 字符串存储坐标数组
  region: string;
  created_at: string;
}

// 井平台数据类型
export interface WellPlatform {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  region: string;
  created_at: string;
}

// 井轨迹数据类型
export interface WellTrajectory {
  id: number;
  name: string;
  platform_id: number;
  coordinates: string; // JSON 字符串存储坐标数组
  region: string;
  created_at: string;
}

// 监测台站数据类型
export interface Station {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  status: 'active' | 'inactive' | 'maintenance';
  region: string;
  created_at: string;
}

// 震源机制解数据类型
export interface FocalMechanism {
  id: number;
  date: string;
  time: string;
  latitude: number;
  longitude: number;
  depth: number;
  magnitude: number;
  strike1: number;
  dip1: number;
  rake1: number;
  strike2: number;
  dip2: number;
  rake2: number;
  misfit: number;
  region: string;
  created_at: string;
}

// 用户数据类型
export interface User {
  id: number;
  email: string;
  password_hash: string;
  role: 'admin' | 'user' | 'viewer';
  created_at: string;
}

// 区域数据类型
export interface Region {
  id: number;
  name: string;
  bounds: string; // JSON 字符串存储边界坐标
  description: string;
  created_at: string;
}

// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页参数类型
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 地震事件查询参数
export interface EarthquakeQuery extends PaginationParams {
  start_date?: string;
  end_date?: string;
  min_magnitude?: number;
  max_magnitude?: number;
  region?: string;
}

// 震源机制解查询参数
export interface FocalMechanismQuery extends PaginationParams {
  start_date?: string;
  end_date?: string;
  min_magnitude?: number;
  max_magnitude?: number;
  region?: string;
}

// JWT 载荷类型
export interface JwtPayload {
  userId: number;
  email: string;
  role: string;
}

// 登录请求类型
export interface LoginRequest {
  email: string;
  password: string;
}

// 注册请求类型
export interface RegisterRequest {
  email: string;
  password: string;
  role?: 'user' | 'viewer';
}
