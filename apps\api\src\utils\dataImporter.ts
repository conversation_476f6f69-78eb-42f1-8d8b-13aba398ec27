import fs from 'fs';
import path from 'path';
import { DatabaseHelper } from '../db/database';

// 数据导入器类
export class DataImporter {
  private dbHelper: DatabaseHelper;
  private dataDir: string;

  constructor() {
    this.dbHelper = new DatabaseHelper();
    this.dataDir = path.join(process.cwd(), '..', '..', 'data');
  }

  // 获取数据目录路径（公共方法）
  get dataDirectory(): string {
    return this.dataDir;
  }

  // 选择性重置表数据（公共方法）
  async resetTableData(tableType: 'earthquakes' | 'faults' | 'wells' | 'stations'): Promise<void> {
    switch (tableType) {
      case 'earthquakes':
        console.log('🗑️ 清空地震事件数据...');
        await this.dbHelper.run('DELETE FROM earthquakes');
        break;
      case 'faults':
        console.log('🗑️ 清空断层数据...');
        await this.dbHelper.run('DELETE FROM faults');
        break;
      case 'wells':
        console.log('🗑️ 清空井轨迹和井平台数据...');
        await this.dbHelper.run('DELETE FROM well_trajectories');
        await this.dbHelper.run('DELETE FROM well_platforms');
        break;
      case 'stations':
        console.log('🗑️ 清空台站数据...');
        await this.dbHelper.run('DELETE FROM stations');
        break;
    }
  }

  // 导入地震事件数据 (CSV格式)
  async importEarthquakes(filePath: string, region: string = '龙门山断裂带'): Promise<number> {
    try {
      console.log(`📊 开始导入地震事件数据: ${filePath}`);

      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim());
      const totalLines = lines.length - 1; // 减去标题行

      let importedCount = 0;
      let processedCount = 0;

      // 收集所有有效数据用于批量插入
      const batchData: any[][] = [];

      // 跳过标题行
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const parts = line.split(',');

        if (parts.length >= 7) {
          try {
            const [eventId, dateStr, timeStr, longitude, latitude, depth, magnitude] = parts;

            // 解析日期格式并组合完整的日期时间
            const formattedDate = this.parseDate(dateStr);
            const occurredAt = `${formattedDate} ${timeStr}`;

            batchData.push([
              eventId,
              occurredAt,
              parseFloat(latitude),
              parseFloat(longitude),
              parseFloat(depth),
              parseFloat(magnitude),
              region
            ]);
          } catch (error) {
            console.warn(`跳过无效行: ${line}`, error);
          }
        }

        processedCount++;
        // 每处理1000条记录显示一次进度
        if (processedCount % 1000 === 0 || processedCount === totalLines) {
          const progress = ((processedCount / totalLines) * 100).toFixed(1);
          console.log(`   进度: ${progress}% (${processedCount}/${totalLines})`);
        }
      }

      // 批量插入数据
      console.log(`💾 开始批量插入 ${batchData.length} 条地震事件数据...`);
      importedCount = await this.dbHelper.batchInsert(
        `INSERT OR IGNORE INTO earthquakes
         (event_id, occurred_at, latitude, longitude, depth, magnitude, region)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        batchData
      );

      console.log(`✅ 地震事件数据导入完成，共导入 ${importedCount} 条记录`);
      return importedCount;

    } catch (error) {
      console.error('❌ 地震事件数据导入失败:', error);
      throw error;
    }
  }

  // 导入断层数据
  async importFaults(filePath: string, level: number, region: string = '龙门山断裂带'): Promise<number> {
    try {
      console.log(`📊 开始导入断层数据: ${filePath} (等级 ${level})`);

      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim());
      const totalLines = lines.length;

      let importedCount = 0;
      let currentFault: number[][] = [];
      let faultIndex = 1;
      let processedLines = 0;

      for (const line of lines) {
        const trimmedLine = line.trim();

        if (trimmedLine.startsWith('>')) {
          // 保存前一个断层（如果有数据）
          if (currentFault.length > 0) {
            const faultName = `断层_${level}_${faultIndex}`;
            const coordinates = JSON.stringify(currentFault);

            await this.dbHelper.run(
              `INSERT OR IGNORE INTO faults (name, level, coordinates, region) VALUES (?, ?, ?, ?)`,
              [faultName, level, coordinates, region]
            );

            importedCount++;
            faultIndex++;
          }

          // 开始新的断层
          currentFault = [];
        } else if (trimmedLine) {
          // 解析坐标点 (支持逗号分隔)
          const parts = trimmedLine.includes(',') ? trimmedLine.split(',') : trimmedLine.split(/\s+/);
          if (parts.length >= 2) {
            const lon = parseFloat(parts[0]);
            const lat = parseFloat(parts[1]);

            if (!isNaN(lon) && !isNaN(lat)) {
              currentFault.push([lon, lat]);
            }
          }
        }

        processedLines++;
        // 每处理500行显示一次进度
        if (processedLines % 500 === 0 || processedLines === totalLines) {
          const progress = ((processedLines / totalLines) * 100).toFixed(1);
          console.log(`   进度: ${progress}% (${processedLines}/${totalLines})`);
        }
      }

      // 保存最后一个断层
      if (currentFault.length > 0) {
        const faultName = `断层_${level}_${faultIndex}`;
        const coordinates = JSON.stringify(currentFault);

        await this.dbHelper.run(
          `INSERT OR IGNORE INTO faults (name, level, coordinates, region) VALUES (?, ?, ?, ?)`,
          [faultName, level, coordinates, region]
        );

        importedCount++;
      }

      console.log(`✅ 断层数据导入完成，共导入 ${importedCount} 条记录`);
      return importedCount;

    } catch (error) {
      console.error('❌ 断层数据导入失败:', error);
      throw error;
    }
  }

  // 导入井轨迹数据 (新格式 - 分离井平台和井轨迹)
  async importWellTrajectories(trajectoryFile: string, namesFile: string, region: string = '龙门山断裂带'): Promise<number> {
    try {
      console.log(`📊 开始导入井轨迹数据: ${trajectoryFile}`);

      if (!fs.existsSync(trajectoryFile) || !fs.existsSync(namesFile)) {
        throw new Error(`文件不存在: ${trajectoryFile} 或 ${namesFile}`);
      }

      // 清空现有井轨迹和井平台数据
      await this.dbHelper.run('DELETE FROM well_trajectories WHERE region = ?', [region]);
      await this.dbHelper.run('DELETE FROM well_platforms WHERE region = ?', [region]);
      console.log(`🗑️ 已清空区域 "${region}" 的现有井轨迹和井平台数据`);

      // 读取井平台名称和位置
      const namesContent = fs.readFileSync(namesFile, 'utf-8');
      const nameLines = namesContent.split('\n').filter(line => line.trim());

      const platformsData: { [key: string]: { lon: number; lat: number; name: string } } = {};

      for (const line of nameLines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 3) {
          const [lon, lat, name] = parts;
          platformsData[name] = {
            lon: parseFloat(lon),
            lat: parseFloat(lat),
            name: name
          };
        }
      }

      // 创建井平台记录的映射
      const platformIds: { [key: string]: number } = {};

      // 读取井轨迹坐标
      const trajectoryContent = fs.readFileSync(trajectoryFile, 'utf-8');
      const trajectoryLines = trajectoryContent.split('\n').filter(line => line.trim());
      const totalTrajectoryLines = trajectoryLines.length;

      let importedCount = 0;
      let trajectoryIndex = 1;
      let processedTrajectoryLines = 0;

      let currentWellName = '';

      console.log(`   处理井轨迹文件，共 ${totalTrajectoryLines} 行`);

      // 用于跟踪当前井轨迹的所有线段
      let currentWellSegments: number[][][] = []; // 当前井轨迹的所有线段
      let currentSegment: number[][] = []; // 当前线段

      for (const line of trajectoryLines) {
        const trimmedLine = line.trim();

        if (trimmedLine.startsWith('>')) {
          // 如果当前线段有数据，添加到当前井轨迹
          if (currentSegment.length > 0) {
            currentWellSegments.push([...currentSegment]);
            currentSegment = [];
          }

          // 检查是否有井轨迹名称
          const wellNameMatch = trimmedLine.match(/>\s*(.+)/);
          if (wellNameMatch && wellNameMatch[1].trim()) {
            // 有名称，说明开始新的井轨迹
            // 先保存之前的井轨迹（如果有的话）
            if (currentWellSegments.length > 0) {
              const wellName = currentWellName || `无名井轨迹_${trajectoryIndex}`;
              await this.saveWellTrajectorySegments(wellName, currentWellSegments, region, platformsData, platformIds);
              importedCount++;
              trajectoryIndex++;
            }

            // 开始新的井轨迹
            currentWellName = wellNameMatch[1].trim();
            currentWellSegments = [];
          } else {
            // 没有名称，继续当前井轨迹的下一个线段
            // currentSegment 已经被重置，准备接收新线段的坐标
          }
        } else if (trimmedLine) {
          // 解析坐标点
          const parts = trimmedLine.split(',');
          if (parts.length >= 2) {
            const lon = parseFloat(parts[0].trim());
            const lat = parseFloat(parts[1].trim());

            if (!isNaN(lon) && !isNaN(lat)) {
              currentSegment.push([lon, lat]);
            }
          }
        }

        processedTrajectoryLines++;
        // 每处理200行显示一次进度
        if (processedTrajectoryLines % 200 === 0 || processedTrajectoryLines === totalTrajectoryLines) {
          const progress = ((processedTrajectoryLines / totalTrajectoryLines) * 100).toFixed(1);
          console.log(`   进度: ${progress}% (${processedTrajectoryLines}/${totalTrajectoryLines})`);
        }
      }

      // 处理最后的线段和井轨迹
      if (currentSegment.length > 0) {
        currentWellSegments.push([...currentSegment]);
      }

      if (currentWellSegments.length > 0) {
        const wellName = currentWellName || `无名井轨迹_${trajectoryIndex}`;
        await this.saveWellTrajectorySegments(wellName, currentWellSegments, region, platformsData, platformIds);
        importedCount++;
      }

      // 导入井平台位置（即使没有轨迹数据的井平台也要导入）
      let platformCount = 0;
      for (const [, platform] of Object.entries(platformsData)) {
        // 创建或获取井平台
        const platformId = await this.getOrCreatePlatform(
          platform.name,
          platformsData,
          [platform.lon, platform.lat],
          region,
          platformIds
        );
        if (platformId) {
          platformCount++;
        }
      }

      console.log(`✅ 井轨迹数据导入完成，共导入 ${importedCount} 条井轨迹记录`);
      console.log(`📍 其中包含 ${platformCount} 个井平台`);

      // 显示导入统计
      console.log(`📈 导入统计:`);
      console.log(`   - 井轨迹线: ${importedCount} 条`);
      console.log(`   - 井平台点: ${platformCount} 个`);

      return importedCount;

    } catch (error) {
      console.error('❌ 井轨迹数据导入失败:', error);
      throw error;
    }
  }

  // 导入台站数据 (.dat格式)
  async importStations(filePath: string, region: string = '龙门山断裂带'): Promise<number> {
    try {
      console.log(`📊 开始导入台站数据: ${filePath}`);

      if (!fs.existsSync(filePath)) {
        throw new Error(`文件不存在: ${filePath}`);
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim());
      const totalLines = lines.length;

      let importedCount = 0;
      let processedLines = 0;

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);

        if (parts.length >= 3) {
          try {
            const [name, latitude, longitude] = parts;
            const status = 'active'; // 默认状态为活跃

            await this.dbHelper.run(
              `INSERT OR IGNORE INTO stations (name, latitude, longitude, status, region) VALUES (?, ?, ?, ?, ?)`,
              [name, parseFloat(latitude), parseFloat(longitude), status, region]
            );

            importedCount++;
          } catch (error) {
            console.warn(`跳过无效行: ${line}`, error);
          }
        }

        processedLines++;
        // 每处理50行显示一次进度
        if (processedLines % 50 === 0 || processedLines === totalLines) {
          const progress = ((processedLines / totalLines) * 100).toFixed(1);
          console.log(`   进度: ${progress}% (${processedLines}/${totalLines})`);
        }
      }

      console.log(`✅ 台站数据导入完成，共导入 ${importedCount} 条记录`);
      return importedCount;

    } catch (error) {
      console.error('❌ 台站数据导入失败:', error);
      throw error;
    }
  }

  // 批量导入所有数据
  async importAllData(): Promise<void> {
    try {
      console.log('🚀 开始批量导入所有数据...');

      const stats = {
        earthquakes: 0,
        faults: 0,
        wells: 0,
        stations: 0
      };

      // 导入地震事件数据
      const earthquakeFile = path.join(this.dataDir, '地震事件点数据', 'all_region平台内事件目录.csv');
      if (fs.existsSync(earthquakeFile)) {
        stats.earthquakes = await this.importEarthquakes(earthquakeFile);
      }

      // 导入断层数据
      for (let level = 1; level <= 3; level++) {
        const faultFile = path.join(this.dataDir, '断层数据', `LX_fault${level}.txt`);
        if (fs.existsSync(faultFile)) {
          stats.faults += await this.importFaults(faultFile, level);
        }
      }

      // 导入井轨迹数据
      const wellTrajectoryFile = path.join(this.dataDir, '井轨迹数据', '井轨迹.txt');
      const wellNamesFile = path.join(this.dataDir, '井轨迹数据', '井平台名称.txt');
      if (fs.existsSync(wellTrajectoryFile) && fs.existsSync(wellNamesFile)) {
        stats.wells = await this.importWellTrajectories(wellTrajectoryFile, wellNamesFile);
      }

      // 导入台站数据
      const stationFile = path.join(this.dataDir, '台站位置', 'station.dat');
      if (fs.existsSync(stationFile)) {
        stats.stations = await this.importStations(stationFile);
      }

      console.log('✅ 所有数据导入完成！');
      console.log('📈 导入统计:');
      console.log(`   - 地震事件: ${stats.earthquakes} 条`);
      console.log(`   - 断层数据: ${stats.faults} 条`);
      console.log(`   - 井轨迹: ${stats.wells} 条`);
      console.log(`   - 监测台站: ${stats.stations} 条`);

    } catch (error) {
      console.error('❌ 批量导入失败:', error);
      throw error;
    }
  }

  // 保存井轨迹数据的辅助方法 - 处理多个线段，保存为MultiLineString格式
  private async saveWellTrajectorySegments(
    wellName: string,
    segments: number[][][],
    region: string,
    platformsData: { [key: string]: { lon: number; lat: number; name: string } },
    platformIds: { [key: string]: number }
  ): Promise<void> {
    if (segments.length === 0) return;

    // 保存为MultiLineString格式，每个segment是一条LineString
    const multiLineStringCoordinates = segments.filter(segment => segment.length >= 2); // 过滤掉少于2个点的线段

    if (multiLineStringCoordinates.length > 0) {
      // 提取井平台名称
      const platformName = this.extractPlatformName(wellName);

      // 获取或创建井平台
      const platformId = await this.getOrCreatePlatform(
        platformName,
        platformsData,
        multiLineStringCoordinates[0][0], // 使用第一个坐标点
        region,
        platformIds
      );

      if (platformId) {
        // 保存井轨迹
        await this.dbHelper.run(
          'INSERT INTO well_trajectories (name, platform_id, coordinates, region) VALUES (?, ?, ?, ?)',
          [wellName, platformId, JSON.stringify(multiLineStringCoordinates), region]
        );

        const totalPoints = multiLineStringCoordinates.reduce((sum, segment) => sum + segment.length, 0);
        console.log(`💾 保存井轨迹: ${wellName} -> 井平台: ${platformName} (${multiLineStringCoordinates.length}个线段, ${totalPoints}个坐标点)`);
      }
    }
  }

  // 从井轨迹名称提取井平台名称
  private extractPlatformName(trajectoryName: string): string {
    // 匹配 "名称-数字" 格式，提取前缀作为井平台名称
    const match = trajectoryName.match(/^(.+)-\d+$/);
    return match ? match[1] : trajectoryName;
  }

  // 获取或创建井平台
  private async getOrCreatePlatform(
    platformName: string,
    platformsData: { [key: string]: { lon: number; lat: number; name: string } },
    fallbackCoordinate: number[],
    region: string,
    platformIds: { [key: string]: number }
  ): Promise<number | null> {
    // 如果已经创建过，直接返回ID
    if (platformIds[platformName]) {
      return platformIds[platformName];
    }

    // 获取井平台坐标
    let lat: number, lon: number;
    if (platformsData[platformName]) {
      lat = platformsData[platformName].lat;
      lon = platformsData[platformName].lon;
    } else {
      // 使用备用坐标（井轨迹的第一个点）
      lat = fallbackCoordinate[1];
      lon = fallbackCoordinate[0];
    }

    try {
      // 创建井平台
      await this.dbHelper.run(
        'INSERT OR IGNORE INTO well_platforms (name, latitude, longitude, region) VALUES (?, ?, ?, ?)',
        [platformName, lat, lon, region]
      );

      // 获取井平台ID
      const platform = await this.dbHelper.get<{ id: number }>(
        'SELECT id FROM well_platforms WHERE name = ? AND region = ?',
        [platformName, region]
      );

      if (platform) {
        platformIds[platformName] = platform.id;
        console.log(`🏗️ 创建井平台: ${platformName} (${lat}, ${lon})`);
        return platform.id;
      }
    } catch (error) {
      console.error(`❌ 创建井平台失败: ${platformName}`, error);
    }

    return null;
  }

  /**
   * 解析日期格式，将日期转换为 YYYY-MM-DD 格式
   * @param dateStr 日期字符串 (支持 YYYY/MM/DD 或 YYYY-MM-DD 格式)
   * @returns 格式化后的日期字符串
   */
  private parseDate(dateStr: string): string {
    try {
      // 替换斜杠为连字符
      let normalizedDate = dateStr.replace(/\//g, '-');

      // 验证日期格式是否正确 (YYYY-MM-DD)
      const dateMatch = normalizedDate.match(/^(\d{4})-(\d{1,2})-(\d{1,2})$/);
      if (!dateMatch) {
        throw new Error(`无效的日期格式: ${dateStr}`);
      }

      const [, year, month, day] = dateMatch;

      // 补零处理
      const formattedMonth = month.padStart(2, '0');
      const formattedDay = day.padStart(2, '0');

      const formattedDate = `${year}-${formattedMonth}-${formattedDay}`;

      // 验证日期是否有效
      const date = new Date(formattedDate);
      if (isNaN(date.getTime())) {
        throw new Error(`无效的日期: ${dateStr}`);
      }

      return formattedDate;
    } catch (error) {
      console.warn(`日期解析失败，使用原始值: ${dateStr}`, error);
      return dateStr.replace(/\//g, '-');
    }
  }

  /**
   * 解析时间格式，将时间转换为 HH:MM:SS 格式
   * @param timeStr 时间字符串 (支持 H:M:S, HH:MM:SS, H:M 等格式)
   * @returns 格式化后的时间字符串
   */
  private parseTime(timeStr: string): string {
    try {
      const trimmedTime = timeStr.trim();

      // 验证时间格式 (支持 H:M:S, HH:MM:SS, H:M, HH:MM 等格式)
      const timeMatch = trimmedTime.match(/^(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?$/);
      if (!timeMatch) {
        throw new Error(`无效的时间格式: ${timeStr}`);
      }

      const [, hour, minute, second] = timeMatch;

      // 补零处理
      const formattedHour = hour.padStart(2, '0');
      const formattedMinute = minute.padStart(2, '0');
      const formattedSecond = (second || '00').padStart(2, '0');

      const formattedTime = `${formattedHour}:${formattedMinute}:${formattedSecond}`;

      // 验证时间是否有效
      const hourNum = parseInt(formattedHour);
      const minuteNum = parseInt(formattedMinute);
      const secondNum = parseInt(formattedSecond);

      if (hourNum < 0 || hourNum > 23 || minuteNum < 0 || minuteNum > 59 || secondNum < 0 || secondNum > 59) {
        throw new Error(`无效的时间值: ${timeStr}`);
      }

      return formattedTime;
    } catch (error) {
      console.warn(`时间解析失败，使用原始值: ${timeStr}`, error);
      return timeStr.trim();
    }
  }

  /**
   * 导入震源机制解数据
   * @param filePath CSV文件路径
   * @param region 区域名称
   * @returns 导入的记录数
   */
  async importFocalMechanisms(filePath: string, region: string = '龙门山断裂带'): Promise<number> {
    console.log(`📊 开始导入震源机制解数据: ${filePath}`);

    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const lines = content.split('\n').filter(line => line.trim());

      if (lines.length === 0) {
        console.log('⚠️  文件为空');
        return 0;
      }

      let importedCount = 0;

      // 跳过标题行
      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const parts = line.split(',');

        if (parts.length >= 18) {
          try {
            const [
              date, time, strike1, dip1, rake1,
              strike2, dip2, rake2, misfit,
              lat, lon, dep, mag, mxx, myy, mzz, mxy, mxz, myz
            ] = parts;

            // 解析日期格式 (支持 YYYY/MM/DD 或 YYYY-MM-DD 格式)
            const formattedDate = this.parseDate(date.trim());

            await this.dbHelper.run(
              `INSERT OR IGNORE INTO focal_mechanisms
               (date, time, latitude, longitude, depth, magnitude,
                strike1, dip1, rake1, strike2, dip2, rake2, misfit,
                mxx, myy, mzz, mxy, mxz, myz, region)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                formattedDate,
                this.parseTime(time.trim()),
                parseFloat(lat.trim()),
                parseFloat(lon.trim()),
                parseFloat(dep.trim()),
                parseFloat(mag.trim()),
                parseFloat(strike1.trim()),
                parseFloat(dip1.trim()),
                parseFloat(rake1.trim()),
                parseFloat(strike2.trim()),
                parseFloat(dip2.trim()),
                parseFloat(rake2.trim()),
                parseFloat(misfit.trim()),
                parseFloat(mxx.trim()),
                parseFloat(myy.trim()),
                parseFloat(mzz.trim()),
                parseFloat(mxy.trim()),
                parseFloat(mxz.trim()),
                parseFloat(myz.trim()),
                region
              ]
            );

            importedCount++;
          } catch (error) {
            console.warn(`跳过无效行: ${line}`, error);
          }
        }
      }

      console.log(`✅ 震源机制解数据导入完成，共导入 ${importedCount} 条记录`);
      return importedCount;

    } catch (error) {
      console.error('❌ 震源机制解数据导入失败:', error);
      throw error;
    }
  }
}
