# 地震音频转换逻辑说明

## 概述

本文档详细说明了地震事件到音频的转换逻辑，包括震级分类、音频参数映射和播放策略。

## 震级分类标准

基于里氏震级标准，我们将地震分为5个等级：

| 震级范围 | 分类 | 描述 | 音频特征 |
|---------|------|------|----------|
| < 2.0 | 微震 | 人体无感觉，仅仪器能检测到 | 非常轻微的高频低音 |
| 2.0 - 3.9 | 小震 | 人体有轻微感觉 | 轻微的中高频低音 |
| 4.0 - 4.9 | 中震 | 明显感觉，可能造成轻微损害 | 明显的中频低音 |
| 5.0 - 5.9 | 强震 | 造成建筑物损害 | 深沉的低频低音 |
| ≥ 6.0 | 大震 | 造成严重破坏 | 震撼的鼓声 + 余震效果 |

## 音频参数映射

### 基础参数
- **基础频率**: 40Hz (可配置)
- **音量倍数**: 1.5x (可配置)

### 详细映射表

#### 微震 (< 2.0)
- **乐器**: 低音合成器 (MembraneSynth)
- **频率**: 60Hz (基础频率 + 20Hz)
- **音量**: 20% × 音量倍数
- **持续时间**: 32n (三十二分音符，非常短)
- **示例**: 震级1.5 → 60Hz, 0.3音量, 32n持续时间

#### 小震 (2.0 - 3.9)
- **乐器**: 低音合成器 (MembraneSynth)
- **频率**: 50Hz (基础频率 + 10Hz)
- **音量**: 40% × 音量倍数
- **持续时间**: 16n (十六分音符，短)
- **示例**: 震级3.0 → 50Hz, 0.6音量, 16n持续时间

#### 中震 (4.0 - 4.9)
- **乐器**: 低音合成器 (MembraneSynth)
- **频率**: 40Hz (基础频率)
- **音量**: 60% × 音量倍数
- **持续时间**: 8n (八分音符，中等)
- **示例**: 震级4.5 → 40Hz, 0.9音量, 8n持续时间

#### 强震 (5.0 - 5.9)
- **乐器**: 低音合成器 (MembraneSynth)
- **频率**: 30Hz (基础频率 - 10Hz)
- **音量**: 80% × 音量倍数
- **持续时间**: 4n (四分音符，较长)
- **示例**: 震级5.5 → 30Hz, 1.2音量, 4n持续时间

#### 大震 (≥ 6.0)
- **乐器**: 鼓声合成器 (NoiseSynth)
- **噪音类型**: 棕噪音 (更深沉)
- **音量**: 100% × 音量倍数
- **持续时间**: 2n (二分音符，很长)
- **特殊效果**:
  - 主鼓声: 立即播放
  - 余震1: 400ms后播放，音量60%，持续时间4n
  - 余震2: 1000ms后播放，音量30%，持续时间8n

## 播放策略

### 事件筛选
1. **时间窗口**: 使用1000ms的时间窗口筛选当前时间点附近的地震事件
2. **去重机制**: 基于事件ID确保每个地震事件只播放一次
3. **优先级排序**: 按震级从大到小排序，大地震优先播放

### 播放时序
1. **错开播放**: 多个地震事件间隔150ms播放，避免音频冲突
2. **内存管理**: 播放记录超过2000个时，自动清理保留最近1000个

### 音频效果
1. **渐进式设计**: 从微震到强震使用连续的低音频率变化
2. **突出大震**: 大地震使用完全不同的鼓声，营造震撼效果
3. **余震模拟**: 大地震包含多重音效，模拟真实地震的余震

## 技术实现

### 音频合成器配置
```typescript
// 低音合成器 (用于微震到强震)
bassRef.current = new Tone.MembraneSynth({
  pitchDecay: 0.05,
  octaves: 10,
  oscillator: { type: 'sine' },
  envelope: { attack: 0.001, decay: 0.4, sustain: 0.1, release: 0.6 }
});

// 鼓声合成器 (用于大震)
drumRef.current = new Tone.NoiseSynth({
  noise: { type: 'brown' },
  envelope: { attack: 0.001, decay: 1.5, sustain: 0.1, release: 2.0 }
});
```

### 播放函数调用
```typescript
// 播放地震音频
playEarthquakeSound(magnitude: number, volume: number)

// 实时播放
playRealtimeAudio(earthquakes, currentTime, timeWindow, volume)
```

## 用户体验

### 听觉反馈
- **微震**: 几乎听不见的轻微声音
- **小震**: 清晰但不突兀的低音
- **中震**: 明显的低频音，引起注意
- **强震**: 深沉有力的低音，令人印象深刻
- **大震**: 震撼的鼓声，多重效果，非常突出

### 音频连续性
- 地震活动频繁时，形成连续的"地震音乐"
- 不同震级的音频自然过渡，不会突兀
- 大地震的鼓声会明显打断常规音频，突出重要性

## 配置选项

用户可以通过音频配置面板调整：
- **基础频率** (20-80Hz)
- **音量倍数** (0.5-3.0x)
- **音调衰减** (0.01-0.2s)
- **包络衰减** (0.1-1.0s)

这些参数会影响所有震级的音频效果，但相对关系保持不变。
