{"name": "@rise-map/web", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@deck.gl/core": "^9.1.13", "@deck.gl/geo-layers": "^9.1.13", "@deck.gl/layers": "^9.1.13", "@deck.gl/mapbox": "^9.1.13", "@deck.gl/react": "^9.1.13", "@headlessui/react": "^2.2.6", "@react-three/drei": "9.88.13", "@react-three/fiber": "8.15.19", "@tanstack/react-query": "^5.83.0", "@turf/circle": "^7.2.0", "@turf/distance": "^7.2.0", "@turf/turf": "^7.2.0", "@types/three": "0.152.0", "clsx": "^2.1.1", "d3": "^7.9.0", "date-fns": "^2.30.0", "deck.gl": "^9.1.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "lucide-react": "^0.292.0", "maplibre-gl": "^5.6.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.61.1", "react-map-gl": "^8.0.4", "react-router-dom": "^6.30.1", "tailwindcss": "^4.1.11", "terra-draw": "^1.10.0", "terra-draw-maplibre-gl-adapter": "^1.1.1", "three": "0.152.0", "tone": "^15.1.22", "zod": "^3.25.76", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/vite": "^4.1.11", "@types/d3": "^7.4.3", "@types/node": "^20.19.9", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.7.0", "typescript": "^5.8.3", "vite": "^7.0.6", "vite-plugin-svgr": "^4.3.0"}}