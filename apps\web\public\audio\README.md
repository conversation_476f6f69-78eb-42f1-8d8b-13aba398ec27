# 音频文件库

本目录包含了从 Tone.js 官方音频库下载的音频样本文件，用于地震数据的音频化播放。

## 📁 目录结构

```
audio/
├── salamander/          # Salamander 钢琴采样
│   ├── C1.mp3
│   ├── C2.mp3
│   ├── C3.mp3
│   ├── C4.mp3
│   ├── C5.mp3
│   └── C6.mp3
├── casio/              # Casio MT-40 电子音色
│   ├── C2.mp3
│   ├── D2.mp3
│   ├── E2.mp3
│   ├── F2.mp3
│   └── G2.mp3
├── drums/              # 鼓组采样
│   ├── kick.mp3
│   ├── snare.mp3
│   ├── hihat.mp3
│   ├── tom1.mp3
│   └── tom2.mp3
└── berklee/            # Berklee 音频样本
    ├── Analogsynth_octaves_highmid.mp3
    ├── Analogsynth_octaves_lowmid.mp3
    ├── Analogsynth_octaves_low.mp3
    ├── gong_shot1.mp3
    ├── gong_shot2.mp3
    ├── gong_shot3.mp3
    ├── Clap1.mp3
    ├── Clap2.mp3
    ├── guitar_LowEstring1.mp3
    ├── guitar_Gstring.mp3
    ├── guitar_highEstring.mp3
    ├── guitar_Astring.mp3
    └── guitar_Bstring.mp3
```

## 🎵 乐器类型映射

### 1. 钢琴 (Piano)
- **来源**: Salamander Grand Piano
- **文件**: salamander/C1-C6.mp3
- **特点**: 清脆明亮的钢琴音色

### 2. Casio 电子音色
- **来源**: Casio MT-40
- **文件**: casio/C2,D2,E2,F2,G2.mp3
- **特点**: 经典电子音色，复古风格

### 3. 鼓组 (Drums)
- **来源**: Acoustic Kit
- **文件**: drums/kick,snare,hihat,tom1,tom2.mp3
- **特点**: 真实鼓组采样，节奏感强

### 4. 模拟合成器 (Analog)
- **来源**: Berklee Analog Synth
- **文件**: berklee/Analogsynth_octaves_*.mp3
- **特点**: 温暖的模拟合成器音色

### 5. 打击乐器 (Percussion)
- **来源**: Berklee Gongs & Claps
- **文件**: berklee/gong_shot*.mp3, Clap*.mp3
- **特点**: 锣声、拍手等打击乐器

### 6. 弦乐器 (Strings)
- **来源**: Berklee Guitar Strings
- **文件**: berklee/guitar_*.mp3
- **特点**: 吉他弦音，温暖柔和

## 🔄 重新下载文件

如果需要重新下载音频文件，可以运行：

```powershell
powershell -ExecutionPolicy Bypass -File "scripts/download-audio-files.ps1"
```

## 📊 文件大小

总计约 50MB 的音频文件，提供高质量的音频体验。

## 🎯 震级映射

不同震级对应不同的音符和音效：
- **微震 (< 1.0)**: 高音/轻音效
- **中震 (1.0 - 2.5)**: 中音/中等音效  
- **大震 (≥ 2.5)**: 低音/重音效

## 📝 版权信息

所有音频文件来源于 Tone.js 官方音频库：
https://github.com/Tonejs/audio

遵循相应的开源许可证。
