import { useState, useEffect } from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { 
  GRLawAnalysis, 
  DynamicBValueAnalysis, 
  FocalMechanismDisplay, 
  CoulombStressCalculator 
} from './modules';
import { BeachBallIcon } from '../icons';

interface AnalysisPanelProps {
  isVisible: boolean;
  onClose: () => void;
}

export function AnalysisPanel({ isVisible, onClose }: AnalysisPanelProps) {
  const [activeTab, setActiveTab] = useState<'gr-law' | 'dynamic-b' | 'focal-mechanism' | 'coulomb'>('gr-law');
  const setLayerVisibility = useMapStore(state => state.setLayerVisibility);

  // 面板关闭时隐藏震源机制解图层
  useEffect(() => {
    if (!isVisible) {
      setLayerVisibility('focalMechanisms', false);
    }
  }, [isVisible, setLayerVisibility]);

  // 当切换到其他tab时隐藏震源机制解图层
  useEffect(() => {
    if (activeTab !== 'focal-mechanism' && activeTab !== 'coulomb') {
      setLayerVisibility('focalMechanisms', false);
    }
  }, [activeTab, setLayerVisibility]);

  if (!isVisible) return null;

  const tabs = [
    { id: 'gr-law', label: <>GR Law <i>b</i>值</>, plainLabel: 'GR Law b值', icon: '📊' },
    { id: 'dynamic-b', label: <>动态<i>b</i>值</>, plainLabel: '动态b值', icon: '📈' },
    { id: 'focal-mechanism', label: '震源机制解', plainLabel: '震源机制解', icon: <BeachBallIcon /> },
    { id: 'coulomb', label: '库伦应力变化', plainLabel: '库伦应力变化', icon: '⚡' }
  ] as const;

  return (
    <div className="fixed z-[60] right-20 top-[100px] w-[420px] max-h-[65vh] fade-in">
      <div className="layer-panel-container rounded-lg px-3 py-2">
        {/* 顶部标题和关闭按钮 */}
        <div className="flex items-center justify-between mb-3 pb-2 border-b border-slate-200">
          <h3 className="text-sm font-semibold text-slate-700">数据分析工具</h3>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600 transition-colors"
            title="关闭分析面板"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 标签页导航 */}
        <div className="mb-3">
          <div className="flex space-x-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`
                  px-2 py-1.5 text-xs font-medium rounded transition-colors whitespace-nowrap
                  ${activeTab === tab.id
                    ? 'bg-blue-500 text-white'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-slate-100'
                  }
                `}
                title={tab.plainLabel}
              >
                <span className="mr-1">
                  {tab.icon}
                </span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <div className="overflow-y-auto max-h-[calc(65vh-8rem)] pr-2">
          {activeTab === 'gr-law' && <GRLawAnalysis />}
          {activeTab === 'dynamic-b' && <DynamicBValueAnalysis />}
          {activeTab === 'focal-mechanism' && <FocalMechanismDisplay />}
          {activeTab === 'coulomb' && <CoulombStressCalculator />}
        </div>

        {/* 底部信息 */}
        <div className="pt-2 mt-3 border-t border-slate-200">
          <div className="flex items-center justify-between text-xs text-slate-500">
            <span>当前分析: {tabs.find(t => t.id === activeTab)?.plainLabel}</span>
            <span>点击上方按钮关闭</span>
          </div>
        </div>
      </div>
    </div>
  );
} 