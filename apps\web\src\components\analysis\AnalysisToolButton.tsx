import { useState } from 'react';
import { AnalysisPanel } from './AnalysisPanel';
import { AnalysisToolIcon } from '@/components/icons';

export function AnalysisToolButton( {className}: {className?: string} = {}) {
  const [showAnalysisPanel, setShowAnalysisPanel] = useState(false);

  return (
    <>
      {/* 分析工具按钮 - 放在地图控制条下方 */}
      <div className={`absolute z-1 right-4 fade-in sm:right-6 ${className}`}>
        <button
          onClick={() => setShowAnalysisPanel(!showAnalysisPanel)}
          className={`
            w-12 h-12 rounded-lg flex items-center justify-center transition-all duration-200
            shadow-lg hover:shadow-xl hover:scale-105
            ${showAnalysisPanel
              ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white'
              : 'bg-gradient-to-br from-orange-400 to-red-500 text-white hover:from-orange-500 hover:to-red-600'
            }
          `}
          title="数据分析工具"
        >
          <AnalysisToolIcon className="w-5 h-5" />
        </button>
      </div>

      {/* 分析面板 - 在按钮左侧显示，使用与TimelinePlayer一致的样式 */}
      <AnalysisPanel 
        isVisible={showAnalysisPanel}
        onClose={() => setShowAnalysisPanel(false)}
      />
    </>
  );
} 