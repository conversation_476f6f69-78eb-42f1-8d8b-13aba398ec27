
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import ReactECharts from 'echarts-for-react';
import { useFocalMechanismData } from '../../../hooks/useFocalMechanismData';
import type { FocalMechanism } from '../../../types';
import { useMapStore } from '../../../stores/useMapStore';
import { useIsMobileOnly } from '@/hooks/useIsMobile';

// 应力状态参数
interface StressState {
  s1: number;     // 最大主应力
  s2: number;     // 中间主应力  
  s3: number;     // 最小主应力
  p0: number;     // 孔隙压力
  depth: number;  // 深度
  shmax_orientation: number; // 最大水平应力方向
}

// 默认应力参数（基于MATLAB代码）
const DEFAULT_STRESS_STATE: StressState = {
  s1: 99.8,     // MPa
  s2: 93.3,     // MPa  
  s3: 83.2,     // MPa
  p0: 72.0,     // MPa (20 * 3.6km)
  depth: 3.6,   // km
  shmax_orientation: 105  // degrees
};

// 计算断层面上的应力
function calculateStressOnFault(
  strike: number, 
  dip: number, 
  stressState: StressState
): { normalStress: number; shearStress: number; coulombStress: number } {
  const { s1, s2, s3, p0, shmax_orientation } = stressState;
  
  // 转换为有效应力
  const r1 = s1 - p0;
  const r2 = s2 - p0;
  const r3 = s3 - p0;
  
  // 计算断层面法向量
  const beta = strike - shmax_orientation;
  const alpha = dip;
  
  // 法向量分量
  const nx = Math.sin(alpha * Math.PI / 180) * Math.sin(beta * Math.PI / 180);
  const ny = Math.sin(alpha * Math.PI / 180) * Math.cos(beta * Math.PI / 180);
  const nz = Math.cos(alpha * Math.PI / 180);
  
  // 应力张量
  const stressTensor = [
    [r1, 0, 0],
    [0, r2, 0],
    [0, 0, r3]
  ];
  
  // 计算应力向量 T = σ * n
  const Tx = stressTensor[0][0] * nx + stressTensor[0][1] * ny + stressTensor[0][2] * nz;
  const Ty = stressTensor[1][0] * nx + stressTensor[1][1] * ny + stressTensor[1][2] * nz;
  const Tz = stressTensor[2][0] * nx + stressTensor[2][1] * ny + stressTensor[2][2] * nz;
  
  // 法向应力
  const normalStress = Tx * nx + Ty * ny + Tz * nz;
  
  // 剪应力
  const T_magnitude = Math.sqrt(Tx * Tx + Ty * Ty + Tz * Tz);
  const shearStress = Math.sqrt(T_magnitude * T_magnitude - normalStress * normalStress);
  
  // 库伦应力变化 (简化公式: τ - μ * σn, μ = 0.6)
  const mu = 0.6;
  const coulombStress = Math.abs(shearStress - mu * normalStress);
  
  return { normalStress, shearStress, coulombStress };
}

// 生成Mohr圆数据
function generateMohrCircles(stressState: StressState) {
  const { s1, s2, s3, p0 } = stressState;
  const r1 = s1 - p0;
  const r2 = s2 - p0;
  const r3 = s3 - p0;
  
  const circles: Array<{
    name: string;
    type: string;
    data: number[][];
    lineStyle: { color: string; width: number };
    showSymbol: boolean;
    silent: boolean;
  }> = [];
  const numPoints = 36;
  
  // 三个Mohr圆
  const mohrCircles = [
    { center: (r1 + r2) / 2, radius: Math.abs(r1 - r2) / 2 },
    { center: (r2 + r3) / 2, radius: Math.abs(r2 - r3) / 2 },
    { center: (r1 + r3) / 2, radius: Math.abs(r1 - r3) / 2 }
  ];
  
  mohrCircles.forEach((circle, index) => {
    const points = [];
    // 只绘制上半圆 (0 到 π)
    for (let i = 0; i <= numPoints; i++) {
      const angle = (i / numPoints) * Math.PI; // 只画上半圆
      const x = circle.center + circle.radius * Math.cos(angle);
      const y = circle.radius * Math.sin(angle);
      points.push([x, y]);
    }
    circles.push({
      name: `Mohr Circle ${index + 1}`,
      type: 'line',
      data: points,
      lineStyle: { color: '#666', width: 1.5 },
      showSymbol: false,
      silent: true
    });
  });
  
  return circles;
}

// 生成破坏线数据
function generateFailureLines() {
  const lines: Array<{
    name: string;
    type: string;
    data: number[][];
    lineStyle: { color: string; width: number; type?: string };
    showSymbol: boolean;
    silent: boolean;
  }> = [];
  const maxStress = 40;
  // 为MPa文字标签预留30px空间，相当于在35MPa的坐标系中预留约3个单位
  const labelSpaceReserved = 3.3; // 约30px对应的坐标单位
  const lineEndX = 35 - labelSpaceReserved; // 等值线在x=32处结束
  
  // 主破坏线 (τ = 0.6 * σn)
  lines.push({
    name: 'Failure Line',
    type: 'line',
    data: [[0, 0], [lineEndX, lineEndX * 0.6]],
    lineStyle: { color: '#000', width: 2 },
    showSymbol: false,
    silent: true
  });
  
  // 应力等值线
  const mu = 0.6;
  const theta = Math.atan(mu); // radians
  const slope = mu;
  for (let i = 2; i <= 10; i += 2) {
    const dsin = i / Math.sin(theta);
    const dcos = i / Math.cos(theta);
    const x1 = dsin;
    const y1 = 0;
    // 计算在预留空间内的终点
    const x2 = Math.min(lineEndX, maxStress);
    const y2 = slope * x2 - dcos;

    // 只有当线条在可见范围内时才添加
    if (x1 <= lineEndX && y2 >= 0 && y2 <= 25) {
      lines.push({
        name: `${i} MPa`,
        type: 'line',
        data: [[x1, y1], [x2, y2]],
        lineStyle: { color: '#aaa', width: 1, type: 'dashed' },
        showSymbol: false,
        silent: true
      });
    }
  }
  
  return lines;
}

export function CoulombStressCalculator() {
  const { focalMechanisms, loading, error } = useFocalMechanismData();
  const [stressState, setStressState] = useState<StressState>(DEFAULT_STRESS_STATE);
  const { setLayerVisibility, mapInstance } = useMapStore();
  const isMobile = useIsMobileOnly();

  // 显示图层的 useEffect
  useEffect(() => {
    setLayerVisibility('focalMechanisms', true);

    // 清理函数：组件卸载时隐藏图层
    return () => {
      setLayerVisibility('focalMechanisms', false);
    };
  }, [setLayerVisibility]);

  // 缩放到全局功能
  const handleZoomToGlobal = useCallback(() => {
    if (!mapInstance || !focalMechanisms || focalMechanisms.length === 0) return;

    const bounds = focalMechanisms.reduce((acc, mechanism) => {
      return {
        minLng: Math.min(acc.minLng, mechanism.longitude),
        maxLng: Math.max(acc.maxLng, mechanism.longitude),
        minLat: Math.min(acc.minLat, mechanism.latitude),
        maxLat: Math.max(acc.maxLat, mechanism.latitude)
      };
    }, {
      minLng: focalMechanisms[0].longitude,
      maxLng: focalMechanisms[0].longitude,
      minLat: focalMechanisms[0].latitude,
      maxLat: focalMechanisms[0].latitude
    });

    // 添加边距
    const padding = 0.005;
    mapInstance.fitBounds([
      [bounds.minLng - padding, bounds.minLat - padding],
      [bounds.maxLng + padding, bounds.maxLat + padding]
    ], {
      padding: 50,
      duration: 1000
    });
  }, [mapInstance, focalMechanisms]);

  // 计算所有震源机制解的应力数据
  const stressData = useMemo(() => {
    if (!focalMechanisms.length) return [];
    
    return focalMechanisms.map(fm => {
      // 计算两个节面的应力
      const plane1 = calculateStressOnFault(fm.strike1, fm.dip1, stressState);
      const plane2 = calculateStressOnFault(fm.strike2, fm.dip2, stressState);
      
      // 选择库伦应力较小的节面
      const selectedPlane = plane1.coulombStress <= plane2.coulombStress ? plane1 : plane2;
      
      // 时间转换为数值用于颜色映射
      const dateTime = new Date(`${fm.date}T${fm.time}`);
      const timeValue = dateTime.getTime();
      
      return {
        id: fm.id,
        normalStress: selectedPlane.normalStress,
        shearStress: selectedPlane.shearStress,
        coulombStress: selectedPlane.coulombStress,
        magnitude: fm.magnitude,
        time: timeValue,
        timeString: `${fm.date} ${fm.time}`,
        location: `${fm.latitude.toFixed(3)}°N, ${fm.longitude.toFixed(3)}°E`,
        depth: fm.depth
      };
    });
  }, [focalMechanisms, stressState]);

  // 时间范围用于颜色映射
  const timeRange = useMemo(() => {
    if (!stressData.length) return { min: 0, max: 0 };
    
    const times = stressData.map(d => d.time);
    return {
      min: Math.min(...times),
      max: Math.max(...times)
    };
  }, [stressData]);

  // ECharts配置
  const chartOption = useMemo(() => {
    if (!stressData.length) return null;

    const mohrCircles = generateMohrCircles(stressState);
    const failureLines = generateFailureLines();

    return {
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 0.98)',
        borderColor: 'transparent',
        borderWidth: 0,
        borderRadius: 12,
        textStyle: {
          color: '#1f2937',
          fontSize: 13
        },
        appendToBody: true,
        extraCssText: 'z-index: 9999; box-shadow: 0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(0,0,0,0.08); backdrop-filter: blur(8px);',
        formatter: (params: any) => {
          if (params.seriesName === 'Focal Mechanisms') {
            const data = params.data;
            return `
              <div style="padding: 10px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; min-width: 200px;">
                <div style="font-weight: 600; color: #1e293b; font-size: 13px; margin-bottom: 8px; border-bottom: 1px solid #e2e8f0; padding-bottom: 4px;">
                  震源机制解
                </div>

                <div style="display: grid; gap: 4px; font-size: 11px;">
                  <div style="display: flex; justify-content: space-between;">
                    <span style="color: #64748b;">时间</span>
                    <span style="color: #1e293b; font-weight: 500;">${data.timeString}</span>
                  </div>

                  <div style="display: flex; justify-content: space-between;">
                    <span style="color: #64748b;">位置</span>
                    <span style="color: #1e293b; font-weight: 500;">${data.location}</span>
                  </div>

                  <div style="display: flex; justify-content: space-between;">
                    <span style="color: #64748b;">深度</span>
                    <span style="color: #1e293b; font-weight: 500;">${data.depth.toFixed(1)} km</span>
                  </div>

                  <div style="display: flex; justify-content: space-between;">
                    <span style="color: #64748b;">震级</span>
                    <span style="color: #dc2626; font-weight: 600;">M${data.magnitude.toFixed(1)}</span>
                  </div>

                  <div style="margin-top: 6px; padding-top: 6px; border-top: 1px solid #f1f5f9;">
                    <div style="display: flex; justify-content: space-between;">
                      <span style="color: #64748b;">法向应力</span>
                      <span style="color: #059669; font-weight: 500;">${data.normalStress.toFixed(1)} MPa</span>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-top: 2px;">
                      <span style="color: #64748b;">剪应力</span>
                      <span style="color: #0284c7; font-weight: 500;">${data.shearStress.toFixed(1)} MPa</span>
                    </div>

                    <div style="display: flex; justify-content: space-between; margin-top: 2px;">
                      <span style="color: #64748b;">库伦应力</span>
                      <span style="color: #dc2626; font-weight: 600;">${data.coulombStress.toFixed(1)} MPa</span>
                    </div>
                  </div>
                </div>
              </div>
            `;
          }
          return '';
        }
      },
      legend: {
        show: false
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: '8%',
        top: isMobile ? '25%' : '20%',
        containLabel: true
      },
      graphic: [
        // 震级图例
        {
          type: 'group',
          left: '20%',
          top: '25%',
          children: [
            // 标题
            {
              type: 'text',
              style: {
                text: '震级',
                fontSize: 12,
                fontWeight: 'bold',
                fill: '#374151'
              },
              top: 5,
              left: 10
            },
            // M 3.5
            {
              type: 'polygon',
              shape: {
                points: [[0, -6.125], [6.125, 0], [0, 6.125], [-6.125, 0]]
              },
              style: {
                fill: '#f9fafb',
                stroke: '#374151',
                lineWidth: 1
              },
              top: 25,
              left: 8
            },
            {
              type: 'text',
              style: {
                text: 'M 3.5',
                fontSize: 10,
                fill: '#374151'
              },
              top: 20,
              left: 20
            },
            // M 3.0
            {
              type: 'polygon',
              shape: {
                points: [[0, -4.5], [4.5, 0], [0, 4.5], [-4.5, 0]]
              },
              style: {
                fill: '#f9fafb',
                stroke: '#374151',
                lineWidth: 1
              },
              top: 45,
              left: 9
            },
            {
              type: 'text',
              style: {
                text: 'M 3.0',
                fontSize: 10,
                fill: '#374151'
              },
              top: 40,
              left: 20
            },
            // M 2.5
            {
              type: 'polygon',
              shape: {
                points: [[0, -3.125], [3.125, 0], [0, 3.125], [-3.125, 0]]
              },
              style: {
                fill: '#f9fafb',
                stroke: '#374151',
                lineWidth: 1
              },
              top: 65,
              left: 10
            },
            {
              type: 'text',
              style: {
                text: 'M 2.5',
                fontSize: 10,
                fill: '#374151'
              },
              top: 60,
              left: 20
            },
            // M 2.0
            {
              type: 'polygon',
              shape: {
                points: [[0, -2], [2, 0], [0, 2], [-2, 0]]
              },
              style: {
                fill: '#f9fafb',
                stroke: '#374151',
                lineWidth: 1
              },
              top: 85,
              left: 11
            },
            {
              type: 'text',
              style: {
                text: 'M 2.0',
                fontSize: 10,
                fill: '#374151'
              },
              top: 80,
              left: 20
            },
            // M 1.5
            {
              type: 'polygon',
              shape: {
                points: [[0, -1.125], [1.125, 0], [0, 1.125], [-1.125, 0]]
              },
              style: {
                fill: '#f9fafb',
                stroke: '#374151',
                lineWidth: 1
              },
              top: 105,
              left: 12
            },
            {
              type: 'text',
              style: {
                text: 'M 1.5',
                fontSize: 10,
                fill: '#374151'
              },
              top: 100,
              left: 20
            }
          ]
        },
        // MPa标签
        {
          type: 'group',
          right: '0%',
          top: '40%',
          children: [
            {
              type: 'text',
              style: {
                text: '2MPa',
                fontSize: 11,
                fill: '#666',
                fontWeight: 'normal'
              },
              top: 0
            },
            {
              type: 'text',
              style: {
                text: '4MPa',
                fontSize: 11,
                fill: '#666',
                fontWeight: 'normal'
              },
              top: 22
            },
            {
              type: 'text',
              style: {
                text: '6MPa',
                fontSize: 11,
                fill: '#666',
                fontWeight: 'normal'
              },
              top: 44
            },
            {
              type: 'text',
              style: {
                text: '8MPa',
                fontSize: 11,
                fill: '#666',
                fontWeight: 'normal'
              },
              top: 65
            },
            {
              type: 'text',
              style: {
                text: '10MPa',
                fontSize: 11,
                fill: '#666',
                fontWeight: 'normal'
              },
              top: 88
            }
          ]
        }
      ],
      xAxis: {
        type: 'value',
        name: '法向应力 σₙ (MPa)',
        nameLocation: 'middle',
        nameGap: 30,
        min: 0,
        max: 35,
        axisLine: { lineStyle: { color: '#666' } },
        axisTick: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#666' },
        splitLine: { show: false }
      },
      yAxis: {
        type: 'value',
        name: '剪应力 τ (MPa)',
        nameLocation: 'end',
        nameGap: 10,
        nameTextStyle: {
          align: 'left',
          padding: [0, 0, 0, -20]
        },
        min: 0,
        max: 25,
        axisLine: { lineStyle: { color: '#666' } },
        axisTick: { lineStyle: { color: '#666' } },
        axisLabel: { color: '#666' },
        splitLine: { show: false }
      },
      visualMap: {
        type: 'continuous',
        dimension: 2,
        min: timeRange.min,
        max: timeRange.max,
        text: ['最新', '最早'],
        realtime: true,
        calculable: true,
        inRange: {
          color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
        },
        orient: 'horizontal',
        left: 'center',
        top: 0,
        itemWidth: isMobile ? 10 : 15,
        itemHeight: isMobile ? 150 : 200,
        textStyle: {
          color: '#4b5563',
          fontSize: 11
        },
        formatter: (value: number) => {
          return new Date(value).toISOString().split('T')[0];
        },
        seriesIndex: [mohrCircles.length + failureLines.length] // 只应用到散点图系列
      },
      series: [
        ...mohrCircles,
        ...failureLines,
        {
          name: 'Focal Mechanisms',
          type: 'scatter',
          symbol: 'diamond',
          data: stressData.map(d => ({
            value: [d.normalStress, d.shearStress, d.time, d.magnitude],
            itemStyle: {
              borderColor: '#000',
              borderWidth: 1
            },
            ...d
          })),
          symbolSize: function(val: any[]) {
            const mag = val[3] as number;
            return Math.pow(mag, 2) * 2; // 较小尺寸
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0,0,0,0.5)'
            }
          }
        }
      ]
    };
  }, [stressData, stressState, timeRange, isMobile]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="bg-gray-50 p-6 rounded-lg text-center border border-gray-200">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-3"></div>
          <p className="text-gray-600">正在加载震源机制解数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="bg-red-50 p-6 rounded-lg text-center border border-red-200">
          <div className="text-red-500 mb-3">⚠️</div>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    );
  }

  if (!focalMechanisms.length) {
    return (
      <div className="space-y-4">
        <div className="bg-yellow-50 p-6 rounded-lg text-center border border-yellow-200">
          <div className="text-yellow-500 mb-3">📊</div>
          <p className="text-yellow-700">暂无震源机制解数据用于分析</p>
          <p className="text-sm text-yellow-600 mt-2">请尝试调整时间范围或筛选条件</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* 主标题 - 吸顶，移动端优化 */}
      <div className="sticky top-0 z-20 bg-white border-b border-slate-200 px-2 md:px-4 py-2 md:py-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm md:text-lg font-semibold text-slate-800">库伦应力分析</h3>
          <div className="flex items-center gap-1 md:gap-2">
            <span className="text-xs md:text-sm text-slate-500">
              {stressData.length}/{stressData.length}
            </span>
            {loading && <div className="animate-spin w-3 h-3 md:w-4 md:h-4 border border-blue-500 border-t-transparent rounded-full"></div>}
            <button
              onClick={handleZoomToGlobal}
              className="p-1 text-slate-400 hover:text-blue-500 transition-colors"
              title="缩放到全局"
              disabled={stressData.length === 0}
            >
              <svg className="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 图表说明 */}
      <div className="bg-slate-50 p-2 md:p-3 rounded mx-1.5 md:mx-3">
        <p className="text-xs text-slate-600 mb-2">
          基于震源机制解的应力状态分析，显示断层面上的应力分布
        </p>
        <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 p-1 rounded border-l-2 md:border-l-3 border-orange-400">
          <span className="text-orange-500">💡</span>
          <p className="flex-1">
            菱形点大小代表震级，颜色代表时间；灰色半圆为Mohr圆；黑线为破坏线；虚线为等值线
          </p>
        </div>
      </div>

      {/* 图表区域 */}
      {chartOption && (
        <div className="bg-white p-2 md:p-3 rounded border border-slate-200 mx-1.5 md:mx-3">
          <ReactECharts
            option={chartOption}
            style={{ height: isMobile ? '240px' : '350px', width: '100%' }}
            opts={{ renderer: 'canvas' }}
          />
        </div>
      )}

      {/* 背景应力场参数设置 - 放到底部，直接展开 */}
      <div className="bg-white border border-slate-200 rounded p-2 md:p-3 mx-1.5 md:mx-3">
        <h4 className="font-semibold mb-2 md:mb-3 text-xs md:text-sm flex items-center gap-2">
          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
          背景应力场参数设置
          <span className="text-xs bg-orange-100 text-orange-700 px-1.5 md:px-2 py-0.5 md:py-1 rounded-full">
            可调节
          </span>
        </h4>

        <div className="grid grid-cols-2 md:grid-cols-3 gap-2 md:gap-3">
          <div>
            <label className="block text-xs font-medium text-slate-700 mb-0.5 md:mb-1">
              最大主应力 σ₁ (MPa)
            </label>
            <input
              type="number"
              value={stressState.s1}
              onChange={(e) => setStressState(prev => ({ ...prev, s1: Number(e.target.value) }))}
              className="w-full px-1.5 md:px-2 py-0.5 md:py-1 border border-slate-300 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-slate-700 mb-0.5 md:mb-1">
              中间主应力 σ₂ (MPa)
            </label>
            <input
              type="number"
              value={stressState.s2}
              onChange={(e) => setStressState(prev => ({ ...prev, s2: Number(e.target.value) }))}
              className="w-full px-1.5 md:px-2 py-0.5 md:py-1 border border-slate-300 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-slate-700 mb-0.5 md:mb-1">
              最小主应力 σ₃ (MPa)
            </label>
            <input
              type="number"
              value={stressState.s3}
              onChange={(e) => setStressState(prev => ({ ...prev, s3: Number(e.target.value) }))}
              className="w-full px-1.5 md:px-2 py-0.5 md:py-1 border border-slate-300 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-slate-700 mb-0.5 md:mb-1">
              孔隙压力 pp (MPa)
            </label>
            <input
              type="number"
              value={stressState.p0}
              onChange={(e) => setStressState(prev => ({ ...prev, p0: Number(e.target.value) }))}
              className="w-full px-1.5 md:px-2 py-0.5 md:py-1 border border-slate-300 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-slate-700 mb-0.5 md:mb-1">
              深度 (km)
            </label>
            <input
              type="number"
              value={stressState.depth}
              onChange={(e) => setStressState(prev => ({ ...prev, depth: Number(e.target.value) }))}
              className="w-full px-1.5 md:px-2 py-0.5 md:py-1 border border-slate-300 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              step="0.1"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-slate-700 mb-0.5 md:mb-1">
              SHmax方向 (°)
            </label>
            <input
              type="number"
              value={stressState.shmax_orientation}
              onChange={(e) => setStressState(prev => ({ ...prev, shmax_orientation: Number(e.target.value) }))}
              className="w-full px-1.5 md:px-2 py-0.5 md:py-1 border border-slate-300 rounded text-xs focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              step="1"
              min="0"
              max="360"
            />
          </div>
        </div>

        <div className="flex items-center justify-between pt-3 mt-3 border-t border-slate-200">
          <div className="flex items-center gap-3">
            <button
              onClick={() => setStressState(DEFAULT_STRESS_STATE)}
              className="px-3 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 border border-blue-200 rounded transition-colors font-medium"
            >
              恢复默认值
            </button>
            <span className="text-xs text-slate-600 bg-slate-50 px-2 py-1 rounded">
              数据点: {stressData.length} 个震源机制解
            </span>
          </div>
          <div className="text-xs text-slate-500">
            <div>有效应力 = 总应力 - 孔隙压力</div>
            <div>SHmax: 最大水平应力方向</div>
          </div>
        </div>
      </div>
    </div>
  );
} 