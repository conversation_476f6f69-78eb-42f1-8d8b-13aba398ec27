import { useState, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
// helper interfaces and functions for the new dynamic b-value computation
interface BSeriesPoint {
  datetime: Date;    // 保持完整的时间精度
  b: number;         // b value
  bInv: number;      // 1 / b
  unc: number;       // Shibolt uncertainty
}

/** 
 * 计算震级-频度分布 (FMD)
 * 对应Python的fmd函数
 */
function calculateFMD(magnitudes: number[], mbin = 0.1) {
  if (magnitudes.length === 0) return { bins: [], counts: [], cumCounts: [] };
  
  const minMag = Math.floor(Math.min(...magnitudes) / mbin) * mbin;
  const maxMag = Math.ceil(Math.max(...magnitudes) / mbin) * mbin;
  const bins: number[] = [];
  const counts: number[] = [];
  const cumCounts: number[] = [];
  
  for (let m = minMag; m <= maxMag + mbin; m += mbin) {
    const rounded = Math.round(m * 10) / 10;
    bins.push(rounded);
    // 计算累积事件数（大于当前震级的事件数）
    const cumCount = magnitudes.filter(mag => mag > rounded - mbin/2).length;
    cumCounts.push(cumCount);
  }
  
  // 计算每个区间的事件数
  for (let i = 0; i < cumCounts.length - 1; i++) {
    counts.push(cumCounts[i] - cumCounts[i + 1]);
  }
  counts.push(cumCounts[cumCounts.length - 1]); // 最后一个区间
  
  return { bins, counts, cumCounts };
}

/** 
 * 使用简化的MBS方法估计Mc
 * 参考Python的get_mbs函数，但做了简化
 */
function estimateCompletenessMagnitude(magnitudes: number[]): number {
  if (magnitudes.length === 0) return 0.5;
  
  const mbin = 0.1;
  const { bins, counts } = calculateFMD(magnitudes, mbin);
  
  // 找到事件数最多的震级区间 (MAXC)
  const maxCount = Math.max(...counts);
  const maxcIndex = counts.indexOf(maxCount);
  const maxc = bins[maxcIndex];
  
  // 简化的MBS：计算每个震级的b值，找第一个稳定的点
  let stableMc = maxc;
  const minMc = -3;
  
  for (let i = maxcIndex; i < bins.length - 1; i++) {
    const testMc = bins[i];
    if (testMc < minMc) continue;
    
    // 计算当前震级的b值
    const { b: currentB } = calculateBAndUnc(magnitudes, testMc, mbin);
    if (!currentB || !isFinite(currentB)) continue;
    
    // 计算后续0.4震级范围的平均b值
    const nextMcs = bins.slice(i, Math.min(i + 4, bins.length)); // 0.4震级范围约4个0.1区间
    if (nextMcs.length < 2) break;
    
    const bValues = nextMcs.map(mc => calculateBAndUnc(magnitudes, mc, mbin).b)
      .filter(b => b && isFinite(b)) as number[];
    
    if (bValues.length >= 2) {
      const avgB = bValues.reduce((s, b) => s + b, 0) / bValues.length;
      const bStd = Math.sqrt(bValues.reduce((s, b) => s + (b - avgB) ** 2, 0) / bValues.length);
      
      // 检查稳定性：当前b值与平均值的差异小于不确定性
      const { unc } = calculateBAndUnc(magnitudes, testMc, mbin);
      if (Math.abs(currentB - avgB) <= unc) {
        stableMc = testMc;
        break;
      }
    }
  }
  
  return Math.round(stableMc * 10) / 10;
}

/** Calculate b value and Shibolt uncertainty for a set of magnitudes */
function calculateBAndUnc(
  magnitudes: number[],
  mc: number,
  mbin = 0.1,
): { b: number | null; unc: number } {
  // 修正：使用严格大于，与Python一致
  const filtered = magnitudes.filter(m => m > Math.round(mc * 10) / 10 - mbin / 2);
  const n = filtered.length;
  if (n < 2) return { b: null, unc: 0 };
  const meanMag = filtered.reduce((s, m) => s + m, 0) / n;
  const b = Math.LOG10E / (meanMag - (mc - mbin / 2));
  const variance =
    filtered.reduce((s, m) => s + (m - meanMag) ** 2, 0) / (n * (n - 1));
  const unc = 2.3 * b * b * Math.sqrt(variance);
  return { b, unc };
}

/** 
 * Compute sliding-window dynamic b-value series (following calc_timeBV2 algorithm) 
 * Window size logic from Python:
 * - len(mag) < 200: win_num = 60
 * - len(mag) >= 200 and len(mag) < 500: win_num = 80  
 * - len(mag) >= 500: win_num = 120
 */
function computeSlidingWindowBSeries(
  events: { datetime: Date; magnitude: number }[],
  mc: number,
): BSeriesPoint[] {
  const total = events.length;
  let windowSize = 120;
  if (total < 200) {
    windowSize = 60;
  } else if (total >= 200 && total < 500) {
    windowSize = 80;
  } else {
    windowSize = 120;
  }



  const result: BSeriesPoint[] = [];
  for (let i = windowSize - 1; i < total; i++) {
    const win = events.slice(i - windowSize + 1, i + 1);
    const mags = win.map(e => e.magnitude);
    const { b, unc } = calculateBAndUnc(mags, mc);
    if (b && isFinite(b)) {
      result.push({ 
        datetime: events[i].datetime, 
        b, 
        bInv: 1 / b, 
        unc 
      });
    }
  }
  return result;
}

/** 
 * Compute b-positive series based on magnitude differences (following b_positive algorithm)
 * 算法逻辑：
 * 1. 计算所有事件的震级差
 * 2. 筛选震级差 >= DIFF 的事件索引
 * 3. 在计算b值时检查当前事件震级是否 >= Mc
 * Window size logic from Python:
 * - evt_num <= 200: Np = 45
 * - evt_num > 200 and evt_num <= 300: Np = 60
 * - evt_num > 300 and evt_num <= 400: Np = 70
 * - evt_num > 400 and evt_num <= 500: Np = 80
 * - evt_num > 500 and evt_num <= 600: Np = 90
 * - evt_num > 600: Np = 95
 */
function computeBPositiveSeries(
  events: { datetime: Date; magnitude: number }[],
  mc: number,
  diff = 0.02,
): BSeriesPoint[] {
  if (events.length === 0) return [];
  
  // Calculate magnitude differences
  const magDiffs = [0]; // First event has no difference
  for (let i = 1; i < events.length; i++) {
    magDiffs.push(events[i].magnitude - events[i-1].magnitude);
  }
  
  // Filter events with magnitude difference >= DIFF (不筛选震级，所有事件都参与)
  const filteredIndices: number[] = [];
  for (let i = 0; i < events.length; i++) {
    if (magDiffs[i] >= diff) {
      filteredIndices.push(i);
    }
  }
  
  if (filteredIndices.length === 0) return [];
  
  // Determine window size Np based on filtered event count (following Python logic)
  const evtNum = filteredIndices.length;
  let Np = 45;
  if (evtNum > 200 && evtNum <= 300) Np = 60;
  else if (evtNum > 300 && evtNum <= 400) Np = 70;
  else if (evtNum > 400 && evtNum <= 500) Np = 80;
  else if (evtNum > 500 && evtNum <= 600) Np = 90;
  else if (evtNum > 600) Np = 95;
  

  
  const result: BSeriesPoint[] = [];
  
  // Calculate b-positive values
  for (let i = Np; i < filteredIndices.length; i++) {
    // 检查当前事件震级是否大于Mc (与Python代码一致)
    const currentEventIndex = filteredIndices[i];
    if (events[currentEventIndex].magnitude < mc) {
      continue;
    }
    
    const windowIndices = filteredIndices.slice(i - Np + 1, i + 1);
    const windowMagDiffs = windowIndices.map(idx => magDiffs[idx]);
    
    const meanMagDiff = windowMagDiffs.reduce((s, m) => s + m, 0) / windowMagDiffs.length;
    const b = 1 / (meanMagDiff - diff) / Math.LN10;
    
    // Calculate uncertainty using bootstrap (与Python代码一致)
    let bTmp: number[] = [];
    const bootstrapSamples = Np; // Python使用Np次循环，不是固定50次
    for (let j = 0; j < bootstrapSamples; j++) {
      const randomIndices = [];
      const sampleSize = Math.floor(Np - 10); // Python: int(Np-10)
      
      // 生成随机排列的索引 (相当于Python的np.random.permutation)
      const allIndices = Array.from({length: Np}, (_, i) => i);
      for (let k = 0; k < sampleSize; k++) {
        const randomIndex = Math.floor(Math.random() * allIndices.length);
        randomIndices.push(allIndices.splice(randomIndex, 1)[0]);
      }
      
      const randomMags = randomIndices.map(idx => windowMagDiffs[idx]);
      const randomMean = randomMags.reduce((s, m) => s + m, 0) / randomMags.length;
      const bootstrapB = 1 / (randomMean - diff) / Math.LN10;
      bTmp.push(bootstrapB);
    }
    
    // Python使用标准差：np.std(b_tmp)
    const mean = bTmp.reduce((s, v) => s + v, 0) / bTmp.length;
    const variance = bTmp.reduce((s, v) => s + (v - mean) ** 2, 0) / bTmp.length;
    const unc = Math.sqrt(variance);
    
    if (isFinite(b) && b > 0) {
      result.push({
        datetime: events[filteredIndices[i]].datetime,
        b,
        bInv: 1 / b,
        unc
      });
    }
  }
  
  return result;
}

/** Get risk events (magnitude 2.5 and above) */
function getRiskEvents(
  events: { datetime: Date; magnitude: number }[],
  minMagnitude = 2.5
): { datetime: Date; magnitude: number }[] {
  if (events.length === 0) return [];
  
  return events.filter(e => e.magnitude >= minMagnitude);
}
import { useEarthquakeData } from '../../../hooks/useEarthquakeData';
import ReactECharts from 'echarts-for-react';



export function DynamicBValueAnalysis() {
  const { earthquakes } = useEarthquakeData();
  const [chartOption, setChartOption] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isEnlarged, setIsEnlarged] = useState(false);
  // dynamic b-value series produced by the new algorithm
  const [bSeries, setBSeries] = useState<BSeriesPoint[]>([]);
  // b-positive series
  const [bPlusSeries, setBPlusSeries] = useState<BSeriesPoint[]>([]);
  // risk events (top 2% magnitude)
  const [riskEvents, setRiskEvents] = useState<{ datetime: Date; magnitude: number }[]>([]);
  
    // 节流计算函数
  const throttledCalculate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        calculateDynamicBValue();
      }, 100);
    };
  }, [earthquakes]);

  // 数据变化时自动计算
  useEffect(() => {
    if (earthquakes && earthquakes.length > 0) {
      throttledCalculate();
    } else {
      setChartOption(null);
        setBSeries([]);
        setBPlusSeries([]);
        setRiskEvents([]);
    }
  }, [earthquakes, throttledCalculate]);

  // ESC键关闭模态框
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isEnlarged) {
        setIsEnlarged(false);
      }
    };

    if (isEnlarged) {
      document.addEventListener('keydown', handleEscape);
      // 禁止body滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isEnlarged]);

    

  const calculateDynamicBValue = useCallback(() => {
    if (!earthquakes || earthquakes.length === 0) return;

    setIsCalculating(true);
    // 清理旧数据，确保图表重新渲染
    setChartOption(null);
    setBSeries([]);
    setBPlusSeries([]);
    setRiskEvents([]);
    
    try {
      // Prepare sorted events (datetime & magnitude) - 保持完整时间精度
      const events = earthquakes
        .filter(eq => eq.occurred_at && eq.magnitude != null && !isNaN(eq.magnitude))
        .map(eq => {
          const date = new Date(eq.occurred_at);
          return { datetime: date, magnitude: eq.magnitude as number };
        })
        .sort((a, b) => (a.datetime < b.datetime ? -1 : 1));

             if (events.length === 0) {
         setChartOption(null);
         setBSeries([]);
         setBPlusSeries([]);
         setRiskEvents([]);
         return;
       }

      // Completeness magnitude Mc
      const mc = estimateCompletenessMagnitude(events.map(e => e.magnitude));

      // 筛选Mc以上的事件用于动态b值计算（与Python代码一致）
      const eventsAboveMc = events.filter(e => e.magnitude >= mc);

      // Dynamic b-value series - 只使用Mc以上的事件
      const seriesData = computeSlidingWindowBSeries(eventsAboveMc, mc);
      setBSeries(seriesData);

      // b-positive series - 使用所有事件（在计算过程中才检查Mc条件）
      const bPlusData = computeBPositiveSeries(events, mc);
      setBPlusSeries(bPlusData);
        
      // Risk events (magnitude 2.5 and above)
      const riskEventsData = getRiskEvents(events, 2.5);
      setRiskEvents(riskEventsData);

             // Seismicity scatter data (regular events in gray) - 使用数值时间戳
       const regularEvents = events.filter(e => !riskEventsData.some(r => r.datetime.getTime() === e.datetime.getTime() && r.magnitude === e.magnitude));
       const scatterData: [number, number][] = regularEvents.map(e => [e.datetime.getTime(), e.magnitude]);
       
       // Risk events scatter data (magnitude 2.5+ in blue) - 使用数值时间戳
       const riskScatterData: [number, number][] = riskEventsData.map(e => [e.datetime.getTime(), e.magnitude]);

       // 构建时间戳映射，供 tooltip 使用
       const dynamicBMap: Record<number, { b: number; unc: number }> = {};
       seriesData.forEach(p => {
         dynamicBMap[p.datetime.getTime()] = { b: p.b, unc: p.unc };
       });

       const bPlusBMap: Record<number, { b: number; unc: number }> = {};
       bPlusData.forEach(p => {
         bPlusBMap[p.datetime.getTime()] = { b: p.b, unc: p.unc };
       });

       // Dynamic b-value line data - 转换为 [timestamp, value] 格式
       const dynamicBLineData: [number, number][] = seriesData.map(p => [p.datetime.getTime(), p.bInv]);
       
       // B-plus line data - 转换为 [timestamp, value] 格式  
       const bPlusLineData: [number, number][] = bPlusData.map(p => [p.datetime.getTime(), p.bInv]);

               // Uncertainty bands data - 重新构建为正确的ECharts堆叠格式
        const dynamicLowerBand: [number, number][] = seriesData.map(p => [p.datetime.getTime(), 1 / (p.b + p.unc)]);
        const dynamicDiffBand: [number, number][] = seriesData.map(p => {
          const lower = 1 / (p.b + p.unc);
          const upper = 1 / (p.b - p.unc);
          return [p.datetime.getTime(), upper - lower];
        });
        
        const bPlusLowerBand: [number, number][] = bPlusData.map(p => [p.datetime.getTime(), 1 / (p.b + p.unc)]);
        const bPlusDiffBand: [number, number][] = bPlusData.map(p => {
          const lower = 1 / (p.b + p.unc);
          const upper = 1 / (p.b - p.unc);
          return [p.datetime.getTime(), upper - lower];
        });

       // Mc和b=1基线数据
       const timeRange = events.length > 0 ? [events[0].datetime.getTime(), events[events.length - 1].datetime.getTime()] : [Date.now(), Date.now()];
       const mcLineData: [number, number][] = [[timeRange[0], mc], [timeRange[1], mc]];
       const bOneLineData: [number, number][] = [[timeRange[0], 1], [timeRange[1], 1]];

       // 计算动态坐标轴范围
       const magnitudes = events.map(e => e.magnitude);
       const magMin = magnitudes.length > 0 ? Math.min(...magnitudes) : 0;
       const magMax = magnitudes.length > 0 ? Math.max(...magnitudes) : 4;
       const magPadding = (magMax - magMin) * 0.1;
       const yAxisMagnitudeMin = magMin;
       const yAxisMagnitudeMax = magMax + magPadding;

       const bInvValues = [...dynamicBLineData, ...bPlusLineData].map(p => p[1]);
       const bInvMin = bInvValues.length > 0 ? Math.min(...bInvValues) : 0;
       const bInvMax = bInvValues.length > 0 ? Math.max(...bInvValues) : 2;
       const bInvPadding = (bInvMax - bInvMin) * 0.1;
       const yAxisBInvMin = Math.max(0, bInvMin - bInvPadding);

      const option = {
        backgroundColor: 'white',
         grid: { left: '4%', right: '2%', bottom: '8%', top: '80px', containLabel: true },
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'cross' },
            backgroundColor: 'rgba(255,255,255,0.95)',
            borderColor: '#ccc',
            borderWidth: 1,
            textStyle: { color: '#000', fontSize: 11 },
            padding: 8,
            confine: false,
            appendToBody: true,
            formatter: (params: any[]) => {
              if (!params || params.length === 0) return '';
              const timestamp = params[0].value?.[0] || params[0].axisValue;
              const d = new Date(timestamp);
              const date = d.getFullYear() + '-' + 
                          (d.getMonth() + 1).toString().padStart(2, '0') + '-' + 
                          d.getDate().toString().padStart(2, '0');
              const time = d.toLocaleTimeString('zh-CN', { hour12: false });
              const lines: string[] = [`<span style="color:#000;">日期: ${date}</span>`];
              
              // 如果不是整点，显示具体时间
              if (d.getHours() !== 0 || d.getMinutes() !== 0 || d.getSeconds() !== 0) {
                lines.push(`<span style="color:#666;font-size:10px;">时间: ${time}</span>`);
              }

              // 获取趋势判断函数（注意：显示的是1/b，所以1/b上升意味着b下降，风险上升）
              const getTrend = (currentData: BSeriesPoint[], timestamp: number) => {
                const currentIndex = currentData.findIndex(p => p.datetime.getTime() === timestamp);
                if (currentIndex <= 0) return '';
                
                const current = currentData[currentIndex];
                const previous = currentData[currentIndex - 1];
                
                if (current.bInv > previous.bInv) {
                  return '<span style="color:#e74c3c;">↗ 上升 (风险↑)</span>';
                } else if (current.bInv < previous.bInv) {
                  return '<span style="color:#27ae60;">↘ 下降 (风险↓)</span>';
                } else {
                  return '<span style="color:#95a5a6;">→ 平稳</span>';
                }
              };

              // 动态 1/b
              const dyn = dynamicBMap[timestamp];
              if (dyn) {
                const { b, unc } = dyn;
                const trend = getTrend(seriesData, timestamp);
                
                // 添加地震学意义解释（基于实际b值，不是1/b值）
                let interpretation = '';
                if (b > 1.2) {
                  interpretation = '<span style="color:#27ae60;font-size:10px;">✓ b值较高，地震活动相对稳定，风险较低</span>';
                } else if (b < 0.8) {
                  interpretation = '<span style="color:#e74c3c;font-size:10px;">⚠ b值较低，需要关注，可能预示较高风险</span>';
                } else {
                  interpretation = '<span style="color:#f39c12;font-size:10px;">~ b值中等，正常地震活动水平</span>';
                }
                
                lines.push(`<span style="color:#e03535;">动态<i>b</i>值: ${b.toFixed(3)} (区间 ${(b-unc).toFixed(3)} ~ ${(b+unc).toFixed(3)})</span>`);
                lines.push(`<span style="color:#e03535;">1/<i>b</i>值: ${(1/b).toFixed(3)} ${trend}</span>`);
                lines.push(`<span style="margin-left:10px;">${interpretation}</span>`);
              }

              // b-plus
              const bp = bPlusBMap[timestamp];
              if (bp) {
                const { b, unc } = bp;
                const trend = getTrend(bPlusData, timestamp);
                
                // 添加b-plus特殊意义解释（基于实际b值，不是1/b值）
                let bPlusInterpretation = '';
                if (bp.b < 0.7) {
                  bPlusInterpretation = '<span style="color:#e74c3c;font-size:10px;">🚨 b-plus值较低，突发性地震活动增强，高度关注</span>';
                } else if (bp.b > 1.5) {
                  bPlusInterpretation = '<span style="color:#27ae60;font-size:10px;">📉 b-plus值较高，震级跳跃活动减弱，风险降低</span>';
                } else {
                  bPlusInterpretation = '<span style="color:#f39c12;font-size:10px;">📊 b-plus值中等，震级跳跃活动正常</span>';
                }
                
                lines.push(`<span style="color:#F26100;"><i>b</i>-plus值: ${b.toFixed(3)} (区间 ${(b-unc).toFixed(3)} ~ ${(b+unc).toFixed(3)})</span>`);
                lines.push(`<span style="color:#F26100;">1/<i>b</i>-plus值: ${(1/b).toFixed(3)} ${trend}</span>`);
                lines.push(`<span style="margin-left:10px;">${bPlusInterpretation}</span>`);
              }

              // 添加背离提示
              if (dyn && bp) {
                const divergence = Math.abs(dyn.b - bp.b);
                if (divergence > 0.3) {
                  lines.push('<span style="color:#9b59b6;font-size:10px;font-weight:bold;">📈 动态b值与b-plus出现显著背离，建议重点关注</span>');
                }
              }

              // 添加一般性提示
              lines.push('<span style="color:#7f8c8d;font-size:9px;margin-top:5px;">💡 图表显示1/b值，1/b↑表示b↓风险↑；b<1预示高风险，b>1相对稳定</span>');

               return lines.join('<br/>');
            },
          },
        legend: [
          {
            data: [
              { name: `地震事件 (${regularEvents.length})`, itemStyle: { color: '#d9d9d9' } },
              { name: `风险事件 (${riskEventsData.length})`, itemStyle: { color: '#1E90FF' } }
            ],
            top: 5,
            left: 'center',
            orient: 'horizontal',
            itemWidth: 14,
            itemHeight: 8,
            itemGap: 15,
            textStyle: { fontSize: 11, color: '#000' }
          },
          {
            data: [
              { name: '动态b值', itemStyle: { color: '#e03535' } },
              { name: 'b-plus', itemStyle: { color: '#F26100' } }
            ],
            top: 25,
            left: 'center',
            orient: 'horizontal',
            itemWidth: 14,
            itemHeight: 8,
            itemGap: 15,
            textStyle: { fontSize: 11, color: '#000' }
          }
        ],
        xAxis: {
          type: 'time', // 保持时间轴，但调整显示格式
          name: '日期',
          nameLocation: 'middle',
          nameGap: 35,
           nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
           axisLine: { lineStyle: { color: '#000', width: 1.5 } },
          axisLabel: {
            fontSize: 10,
            color: '#000',
            interval: 'auto', // 自动间隔，减少标签密度
            maxInterval: 7 * 24 * 3600 * 1000, // 最大间隔7天
             formatter: (value: number) => {
               const d = new Date(value);
               const month = (d.getMonth() + 1).toString().padStart(2, '0');
               const day = d.getDate().toString().padStart(2, '0');
               return `${month}-${day}`; // 只显示日期，类似Python的%Y-%m-%d格式
            },
          },
           axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 },
          minInterval: 24 * 3600 * 1000, // 最小间隔1天，避免过度密集
        },
        yAxis: [
          {
            type: 'value',
            name: '震级 (ML)',
             min: yAxisMagnitudeMin, 
            max: yAxisMagnitudeMax,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000', formatter: (value: number) => value.toFixed(1) },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 },
             splitLine: { show: true, lineStyle: { color: '#f0f0f0', width: 1 } }
          },
          {
            type: 'value',
            name: '1/b',
             min: 0, 
             max: 2.5,
             nameTextStyle: { fontSize: 12, fontWeight: 'bold', color: '#000' },
             axisLine: { lineStyle: { color: '#000', width: 1.5 } },
             axisLabel: { fontSize: 10, color: '#000', formatter: (value: number) => value.toFixed(1) },
             axisTick: { lineStyle: { color: '#000', width: 1 }, length: 5 }
           },
        ],
        series: [
           // Regular seismicity (gray dots)
          {
             name: `地震事件 (${regularEvents.length})`,
            type: 'scatter',
            yAxisIndex: 0,
             data: scatterData,
            symbolSize: (val: any) => Math.max(3, val[1] * 4),
             itemStyle: { color: '#d9d9d9', borderColor: '#bfbfbf', borderWidth: 0.5 },
             emphasis: { disabled: true },
             tooltip: { show: false },
            z: 0  // 设为0，让事件点显示在刻度轴下方
          },
           // Risk events vertical lines (gray lines from Mc to event magnitude)
           ...riskEventsData.map((e, index) => ({
             name: `风险事件连线${index}`,
             type: 'line',
             yAxisIndex: 0,
             data: [
               [e.datetime.getTime(), mc],
               [e.datetime.getTime(), e.magnitude]
             ],
             lineStyle: { color: '#999999', width: 1, type: 'solid' },
             symbol: 'none',
             showSymbol: false,
             silent: true,
             tooltip: { show: false },
             z: -1,  // 设为-1，让连线显示在事件点下方
             legendHoverLink: false
           })),
           // Risk events (blue dots - magnitude 2.5+)
           {
             name: `风险事件 (${riskEventsData.length})`,
             type: 'scatter',
             yAxisIndex: 0,
             data: riskScatterData,
             symbolSize: (val: any) => Math.max(8, val[1] * 5),
             itemStyle: { color: '#1E90FF', borderColor: '#0066CC', borderWidth: 1 },
             emphasis: { scale: true },
             tooltip: { show: false },
             z: 0  // 设为0，让风险事件点也显示在刻度轴下方
           },
           // Mc baseline using markLine for persistent label
           {
             name: 'Mc线',
             type: 'line',
             yAxisIndex: 0,
             data: [],
                           markLine: {
                symbol: 'none',
                lineStyle: { color: '#000', type: 'dashed', width: 1.5 },
                label: {
                  show: true,
                  formatter: `Mc=${mc.toFixed(1)}`,
                  position: 'insideStartBottom',
                  color: '#000',
                  fontSize: 10,
                  backgroundColor: 'rgba(255,255,255,0.8)',
                  padding: [2, 4],
                  borderRadius: 2,
                },
               data: [
                 { yAxis: mc }
               ],
               z: 2,
             },
             silent: true,
           },
           // Dynamic 1/b uncertainty band (red) - linked to legend
           {
            name: '动态b值',
            type: 'line',
            yAxisIndex: 1,
            stack: 'dynamic_uncert',
            data: dynamicLowerBand,
            lineStyle: { opacity: 0 },
            showSymbol: false,
            areaStyle: { color: 'transparent' },
            emphasis: { disabled: true },
            silent: true,
            z: 1,
            zlevel: 1,
          },
           {
             name: '动态b值',
             type: 'line',
             yAxisIndex: 1,
             stack: 'dynamic_uncert',
             data: dynamicDiffBand,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(255,0,0,0.25)' },
             emphasis: { disabled: true },
             silent: true,
             z: 2
           },
           // B-plus uncertainty band (orange) - linked to legend
           {
             name: 'b-plus',
             type: 'line',
             yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: bPlusLowerBand,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'transparent' },
             emphasis: { disabled: true },
             silent: true,
             z: 1,
             zlevel: 2,
           },
          {
             name: 'b-plus',
            type: 'line',
            yAxisIndex: 1,
             stack: 'bplus_uncert',
             data: bPlusDiffBand,
             lineStyle: { opacity: 0 },
             showSymbol: false,
             areaStyle: { color: 'rgba(218, 90, 5, 0.55)' },
             emphasis: { disabled: true },
             silent: true,
             z: 4,
             zlevel: 3
           },
           // Dynamic 1/b line (连续红色线条)
           {
             name: '动态b值',
             type: 'line',
             yAxisIndex: 1,
             data: dynamicBLineData,
             lineStyle: { 
               color: '#e03535',
               width: 2 
             },
             symbol: 'none',
             connectNulls: false,
             showSymbol: false,
             z: 4,
           },
           // b-plus line (连续橙色线条)
           {
             name: 'b-plus',
             type: 'line',
             yAxisIndex: 1,
             data: bPlusLineData,
             lineStyle: { 
               color: '#F26100',
               width: 2 
             },
             symbol: 'none',
             connectNulls: false,
             showSymbol: false,
             z: 4,
           },
           // b=1 baseline
           {
             name: 'b=1',
             type: 'line',
             yAxisIndex: 1,
             data: bOneLineData,
             lineStyle: { color: '#000', width: 1.5 },
             symbol: 'none',
             silent: true,
            z: 2
           },
         ],
      };

      setChartOption(option);
    } finally {
      setIsCalculating(false);
    }
  }, [earthquakes]);

  // 统计信息
  const avgBValue = bSeries.length > 0 ? bSeries.reduce((s, v) => s + v.bInv, 0) / bSeries.length : null;
  const totalEarthquakes = earthquakes ? earthquakes.length : 0;
  
  // 计算时间跨度时保持时间精度
  let analysisSpanDays = 0;
  if (earthquakes && earthquakes.length > 0) {
    const dates = earthquakes
      .filter(eq => eq.occurred_at)
      .map(eq => new Date(eq.occurred_at))
      .sort((a, b) => a.getTime() - b.getTime());
    const first = dates[0];
    const last = dates[dates.length - 1];
    analysisSpanDays = Math.ceil((last.getTime() - first.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  }



  return (
    <>
      <div className="space-y-3">
        <div className="bg-slate-50 p-3 rounded">
          <h4 className="font-semibold mb-2 text-sm">动态<i>b</i>值时间序列分析</h4>
          <p className="text-xs text-slate-600 mb-2">
            基于累积历史数据分析地震活动的<i>b</i>值随时间变化
          </p>
          <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-50 p-1 rounded border-l-3 border-orange-400">
            <span className="text-orange-500">💡</span>
            <p className="flex-1">
              选择的日期越长，<i>b</i>值计算越稳定可靠
            </p>
          </div>
          
          {isCalculating && (
            <div className="text-xs text-blue-600 text-center py-1">
              正在分析数据...
            </div>
          )}
          
          {!isCalculating && (!earthquakes || earthquakes.length === 0) && (
            <div className="text-xs text-slate-500 text-center py-1">
              等待地震数据...
            </div>
          )}

        </div>

        {chartOption && (
          <div className="space-y-2">
            <div className="grid grid-cols-4 gap-2">
              <div className="bg-blue-50 p-2 rounded text-center">
                <div className="text-xs text-slate-600">地震总数</div>
                <div className="text-lg font-semibold text-blue-600">{totalEarthquakes}</div>
              </div>
              
              <div className="bg-green-50 p-2 rounded text-center">
                <div className="text-xs text-slate-600">时间跨度</div>
                <div className="text-lg font-semibold text-green-600">{analysisSpanDays}天</div>
              </div>
              
              <div className="bg-red-50 p-2 rounded text-center">
                <div className="text-xs text-slate-600">动态<i>b</i>值</div>
                <div className="text-lg font-semibold text-red-600">
                  {avgBValue ? avgBValue.toFixed(3) : 'N/A'}
                </div>
              </div>

              <div className="bg-orange-50 p-2 rounded text-center">
                <div className="text-xs text-slate-600">风险事件</div>
                <div className="text-lg font-semibold text-orange-600">{riskEvents.length}</div>
              </div>
            </div>

            <div className="bg-white p-3 rounded border border-slate-200 relative">
              <button
                onClick={() => setIsEnlarged(!isEnlarged)}
                className="absolute top-2 right-2 z-10 bg-white hover:bg-gray-50 border border-gray-300 rounded-md p-1.5 shadow-sm transition-colors"
                title={isEnlarged ? "缩小图表" : "放大图表"}
              >
                {isEnlarged ? (
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                )}
              </button>
              <ReactECharts 
                option={chartOption} 
                style={{ height: '350px', width: '100%', overflow: 'hidden' }}
                opts={{ renderer: 'canvas' }}
                notMerge={true}
              />
            </div>
          </div>
        )}
      </div>

      {/* 放大模式的全屏覆盖层 - 使用Portal渲染到body */}
      {isEnlarged && chartOption && createPortal(
        <div 
          className="fixed inset-0 flex items-center justify-center z-[9999] p-4 animate-in fade-in duration-300"
          style={{
            background: `
              radial-gradient(ellipse at top, rgba(147, 197, 253, 0.15) 0%, transparent 50%),
              radial-gradient(ellipse at bottom, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
              linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)
            `,
            backdropFilter: 'blur(16px) saturate(180%)',
            WebkitBackdropFilter: 'blur(16px) saturate(180%)',
          }}
          onClick={(e) => {
            // 点击背景关闭模态框
            if (e.target === e.currentTarget) {
              setIsEnlarged(false);
            }
          }}
        >
          <div 
            className="w-full max-w-7xl h-full max-h-[90vh] flex flex-col rounded-3xl overflow-hidden border animate-in zoom-in-95 duration-300"
            style={{
              background: `
                linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%),
                radial-gradient(circle at 20% 50%, rgba(147, 197, 253, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.08) 0%, transparent 50%)
              `,
              backdropFilter: 'blur(24px) saturate(200%)',
              WebkitBackdropFilter: 'blur(24px) saturate(200%)',
              border: `1px solid rgba(255, 255, 255, 0.3)`,
              boxShadow: `
                0 32px 64px -12px rgba(0, 0, 0, 0.15),
                0 20px 25px -5px rgba(0, 0, 0, 0.1),
                0 8px 10px -5px rgba(0, 0, 0, 0.04),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.4),
                inset 0 -1px 0 0 rgba(0, 0, 0, 0.05)
              `,
            }}
          >
            {/* 顶部反光效果 */}
            <div 
              className="absolute top-0 left-0 right-0 h-px"
              style={{
                background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%)',
              }}
            />
            
            <div 
              className="flex items-center justify-between p-6 relative"
              style={{
                background: `
                  linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.8) 100%),
                  radial-gradient(circle at 30% 40%, rgba(147, 197, 253, 0.08) 0%, transparent 70%)
                `,
                backdropFilter: 'blur(12px) saturate(150%)',
                WebkitBackdropFilter: 'blur(12px) saturate(150%)',
                borderBottom: '1px solid rgba(148, 163, 184, 0.2)',
              }}
            >
              {/* 标题栏装饰性反光 */}
              <div 
                className="absolute top-0 left-0 right-0 h-px"
                style={{
                  background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.5) 20%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.5) 80%, transparent 100%)',
                }}
              />
              
              <h3 className="text-xl font-bold bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 bg-clip-text text-transparent">
                动态<i>b</i>值时间序列分析 - 放大视图
              </h3>
              
              <div className="flex items-center gap-3">
                <span className="text-xs text-gray-600/90 bg-white/40 px-2 py-1 rounded-full backdrop-blur-sm">
                  按ESC或点击背景关闭
                </span>
                <button
                  onClick={() => setIsEnlarged(false)}
                  className="group relative rounded-2xl p-3 transition-all duration-300 hover:scale-110 active:scale-95"
                  style={{
                    background: `
                      linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.8) 100%),
                      radial-gradient(circle at 50% 50%, rgba(147, 197, 253, 0.1) 0%, transparent 70%)
                    `,
                    backdropFilter: 'blur(10px) saturate(150%)',
                    WebkitBackdropFilter: 'blur(10px) saturate(150%)',
                    border: '1px solid rgba(255, 255, 255, 0.4)',
                    boxShadow: `
                      0 4px 12px rgba(0, 0, 0, 0.08),
                      0 2px 4px rgba(0, 0, 0, 0.05),
                      inset 0 1px 0 rgba(255, 255, 255, 0.6)
                    `,
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = `
                      linear-gradient(135deg, rgba(239, 246, 255, 0.95) 0%, rgba(233, 243, 255, 0.9) 100%),
                      radial-gradient(circle at 50% 50%, rgba(147, 197, 253, 0.15) 0%, transparent 70%)
                    `;
                    e.currentTarget.style.transform = 'scale(1.1) translateY(-1px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = `
                      linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.8) 100%),
                      radial-gradient(circle at 50% 50%, rgba(147, 197, 253, 0.1) 0%, transparent 70%)
                    `;
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                  title="关闭放大视图"
                >
                  <svg className="w-5 h-5 text-gray-700 group-hover:text-gray-800 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                  
                  {/* 按钮内部反光效果 */}
                  <div 
                    className="absolute top-0 left-0 right-0 h-px rounded-t-2xl"
                    style={{
                      background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%)',
                    }}
                  />
                </button>
              </div>
            </div>
            
            <div 
              className="flex-1 p-6 relative overflow-hidden"
              style={{
                background: `
                  radial-gradient(ellipse at top left, rgba(239, 246, 255, 0.3) 0%, transparent 50%),
                  radial-gradient(ellipse at bottom right, rgba(245, 243, 255, 0.3) 0%, transparent 50%)
                `,
              }}
            >
              {/* 内容区域装饰性光晕 */}
              <div 
                className="absolute top-0 left-0 w-32 h-32 rounded-full opacity-30"
                style={{
                  background: 'radial-gradient(circle, rgba(147, 197, 253, 0.4) 0%, transparent 70%)',
                  filter: 'blur(20px)',
                }}
              />
              <div 
                className="absolute bottom-0 right-0 w-24 h-24 rounded-full opacity-20"
                style={{
                  background: 'radial-gradient(circle, rgba(168, 85, 247, 0.4) 0%, transparent 70%)',
                  filter: 'blur(15px)',
                }}
              />
              
              <div className="relative z-10 h-full">
                <ReactECharts 
                  option={chartOption} 
                  style={{ height: '100%', width: '100%' }}
                  opts={{ renderer: 'canvas' }}
                  notMerge={true}
                />
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </>
  );
} 