import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useMapStore } from '@/stores/useMapStore';
import { useFocalMechanismData } from '@/hooks/useFocalMechanismData';
import { getOrCreatePNG, getOrCreatePNGFromMT, momentTensorToPrincipalAxes, principalAxesToNodalPlanes, classifyMomentTensor } from '@/utils/beachball';
import type { FocalMechanism } from '@/types';
import type { MomentTensor } from '@/utils/beachball';

export function FocalMechanismDisplay() {
  const {
    setLayerVisibility,
    selectedFocalMechanism,
    setSelectedFocalMechanism,
    setHoveredFocalMechanism,
    mapInstance,
    focalMechanismDisplayMode,
    setFocalMechanismDisplayMode
  } = useMapStore();
  
  // 使用新的context获取过滤后的震源机制解数据
  const { focalMechanisms, loading, error, refetch } = useFocalMechanismData();

  // 震源机制解显示模式使用全局状态
  const displayMode = focalMechanismDisplayMode;

  // 获取断层类型描述
  const getFaultType = useCallback((rake: number): string => {
    const absRake = Math.abs(rake);
    if (absRake < 30 || absRake > 150) {
      return '走滑断层';
    } else if (rake > 30 && rake < 150) {
      return '逆断层';
    } else {
      return '正断层';
    }
  }, []);

  // 获取机制解的矩阵张量数据
  const getMechanismMomentTensor = useCallback((mechanism: FocalMechanism): MomentTensor => {
    // 直接使用真实的矩阵张量数据
    return {
      mxx: mechanism.mxx,
      myy: mechanism.myy,
      mzz: mechanism.mzz,
      mxy: mechanism.mxy,
      mxz: mechanism.mxz,
      myz: mechanism.myz
    };
  }, []);

  // 从矩阵张量计算节面参数
  const getNodalPlanesFromMT = useCallback((mechanism: FocalMechanism) => {
    try {
      const mt = getMechanismMomentTensor(mechanism);
      const axes = momentTensorToPrincipalAxes(mt);
      const planes = principalAxesToNodalPlanes(axes);
      return planes;
    } catch (error) {
      console.error('从矩阵张量计算节面参数失败:', error);
      return null;
    }
  }, [getMechanismMomentTensor]);

  // 显示图层的 useEffect
  useEffect(() => {
    setLayerVisibility('focalMechanisms', true);
    
    // 清理函数：组件卸载时隐藏图层
    return () => {
      setLayerVisibility('focalMechanisms', false);
    };
  }, [setLayerVisibility]);

  const [localSelectedMechanism, setLocalSelectedMechanism] = useState<FocalMechanism | null>(null);

  // 详情面板高度控制
  const [detailPanelHeight, setDetailPanelHeight] = useState<number>(50); // 百分比
  const [isDetailPanelExpanded, setIsDetailPanelExpanded] = useState<boolean>(false);

  // 同步展开状态
  useEffect(() => {
    setIsDetailPanelExpanded(detailPanelHeight > 50);
  }, [detailPanelHeight]);

  // 同步地图选中状态和本地选中状态
  useEffect(() => {
    if (selectedFocalMechanism) {
      const mechanism = focalMechanisms.find(m => String(m.id) === selectedFocalMechanism);
      setLocalSelectedMechanism(mechanism || null);
    } else {
      setLocalSelectedMechanism(null);
    }
  }, [selectedFocalMechanism, focalMechanisms]);

  // 选择机制解的回调函数 - 支持切换选中状态
  const handleSelectMechanism = useCallback((mechanism: FocalMechanism) => {
    const mechanismId = String(mechanism.id);
    
    // 如果点击的是已选中的机制解，则取消选中
    if (selectedFocalMechanism === mechanismId) {
      setLocalSelectedMechanism(null);
      setSelectedFocalMechanism(null);
    } else {
      setLocalSelectedMechanism(mechanism);
      setSelectedFocalMechanism(mechanismId);
    }
  }, [selectedFocalMechanism, setSelectedFocalMechanism]);

  // 悬停机制解的回调函数
  const handleHoverMechanism = useCallback((mechanism: FocalMechanism | null) => {
    setHoveredFocalMechanism(mechanism ? String(mechanism.id) : null);
  }, [setHoveredFocalMechanism]);

  // 缩放到全局功能
  const handleZoomToGlobal = useCallback(() => {
    if (!mapInstance || focalMechanisms.length === 0) return;

    const bounds = focalMechanisms.reduce((acc, mechanism) => {
      return {
        minLng: Math.min(acc.minLng, mechanism.longitude),
        maxLng: Math.max(acc.maxLng, mechanism.longitude),
        minLat: Math.min(acc.minLat, mechanism.latitude),
        maxLat: Math.max(acc.maxLat, mechanism.latitude)
      };
    }, {
      minLng: Infinity,
      maxLng: -Infinity,
      minLat: Infinity,
      maxLat: -Infinity
    });

    // 添加边距
    const padding = 0.005;
    mapInstance.fitBounds([
      [bounds.minLng - padding, bounds.minLat - padding],
      [bounds.maxLng + padding, bounds.maxLat + padding]
    ], {
      padding: 50,
      duration: 1000
    });
  }, [mapInstance, focalMechanisms]);

  // 缩放到单个机制解功能
  const handleZoomToMechanism = useCallback((mechanism: FocalMechanism) => {
    if (!mapInstance) return;

    mapInstance.flyTo({
      center: [mechanism.longitude, mechanism.latitude],
      zoom: 18,
      duration: 1000
    });
  }, [mapInstance]);

  // 数据处理（按时间排序）
  const sortedMechanisms = useMemo(() => {
    // 默认按时间排序（最新的在前）
    return focalMechanisms.sort((a, b) => {
      return new Date(b.date + ' ' + b.time).getTime() - new Date(a.date + ' ' + a.time).getTime();
    });
  }, [focalMechanisms]);

  // 使用 useMemo 优化统计信息计算
  const statisticsData = useMemo(() => {
    return {
      totalCount: focalMechanisms.length,
      filteredCount: sortedMechanisms.length
    };
  }, [focalMechanisms.length, sortedMechanisms.length]);

  // 选中机制解的PNG状态
  const [selectedMechanismPNG, setSelectedMechanismPNG] = useState<string | null>(null);

  // 当选中的机制解改变时，获取对应的PNG
  useEffect(() => {
    if (localSelectedMechanism) {
      if (displayMode === 'traditional') {
        // 三参数模式
        getOrCreatePNG(
          localSelectedMechanism.strike1,
          localSelectedMechanism.dip1,
          localSelectedMechanism.rake1,
          80
        ).then(setSelectedMechanismPNG);
      } else {
        // 矩阵张量模式
        try {
          const mt = getMechanismMomentTensor(localSelectedMechanism);
          getOrCreatePNGFromMT(mt, 80).then(setSelectedMechanismPNG);
        } catch (error) {
          console.error('矩阵张量计算错误:', error);
          // 降级到传统模式
          getOrCreatePNG(
            localSelectedMechanism.strike1,
            localSelectedMechanism.dip1,
            localSelectedMechanism.rake1,
            80
          ).then(setSelectedMechanismPNG);
        }
      }
    } else {
      setSelectedMechanismPNG(null);
    }
  }, [localSelectedMechanism?.id, localSelectedMechanism?.strike1, localSelectedMechanism?.dip1, localSelectedMechanism?.rake1, displayMode, getMechanismMomentTensor]);

  // 机制解列表项组件 - 完全独立的组件，避免悬停状态影响
  const MechanismItem = React.memo(({ 
    mechanism, 
    isSelected, 
    onSelect, 
    onHover,
    onHoverLeave,
    onZoom,
    getFaultType,
    displayMode,
    getMechanismMomentTensor,
    getNodalPlanesFromMT
  }: { 
    mechanism: FocalMechanism; 
    isSelected: boolean;
    onSelect: (mechanism: FocalMechanism) => void;
    onHover: (mechanism: FocalMechanism) => void;
    onHoverLeave: () => void;
    onZoom: (mechanism: FocalMechanism) => void;
    getFaultType: (rake: number) => string;
    displayMode: 'traditional' | 'tensor';
    getMechanismMomentTensor: (mechanism: FocalMechanism) => MomentTensor;
    getNodalPlanesFromMT: (mechanism: FocalMechanism) => any;
  }) => {
    const [beachBallPNG, setBeachBallPNG] = useState<string | null>(null);
    const [tensorPNG, setTensorPNG] = useState<string | null>(null);
    const [isInternalHovered, setIsInternalHovered] = useState(false);

    // 稳定的矩阵张量数据 - 避免重复计算
    const stableMT = useMemo(() => {
      if (displayMode === 'tensor') {
        return getMechanismMomentTensor(mechanism);
      }
      return null;
    }, [displayMode, mechanism.id, mechanism.mxx, mechanism.myy, mechanism.mzz, mechanism.mxy, mechanism.mxz, mechanism.myz, getMechanismMomentTensor]);

    // 计算矩阵张量模式下的断层类型
    const mtFaultType = useMemo(() => {
      if (displayMode === 'tensor') {
        const planes = getNodalPlanesFromMT(mechanism);
        if (planes) {
          return getFaultType(planes.rake1);
        }
      }
      return null;
    }, [displayMode, mechanism.id, getNodalPlanesFromMT, getFaultType]);

    // 根据显示模式加载不同的沙滩球图形
    useEffect(() => {
      if (displayMode === 'traditional') {
        // 传统三参数模式
        getOrCreatePNG(
          mechanism.strike1,
          mechanism.dip1,
          mechanism.rake1,
          32
        ).then(setBeachBallPNG);
        setTensorPNG(null);
      } else if (displayMode === 'tensor' && stableMT) {
        // 矩阵张量模式
        try {
          getOrCreatePNGFromMT(stableMT, 32).then(setTensorPNG);
        } catch (error) {
          console.error('矩阵张量计算错误:', error);
          // 降级到传统模式
          getOrCreatePNG(
            mechanism.strike1,
            mechanism.dip1,
            mechanism.rake1,
            32
          ).then(setBeachBallPNG);
        }
        setBeachBallPNG(null);
      }
    }, [mechanism.strike1, mechanism.dip1, mechanism.rake1, displayMode, stableMT]);

    const handleClick = useCallback(() => {
      onSelect(mechanism);
    }, [mechanism, onSelect]);

    const handleMouseEnter = useCallback(() => {
      setIsInternalHovered(true);
      onHover(mechanism);
    }, [mechanism, onHover]);

    const handleMouseLeave = useCallback(() => {
      setIsInternalHovered(false);
      onHoverLeave();
    }, [onHoverLeave]);

    const handleZoomClick = useCallback((e: React.MouseEvent) => {
      e.stopPropagation();
      onZoom(mechanism);
    }, [mechanism, onZoom]);

    // 使用内部悬停状态，避免外部状态影响
    const itemClassName = useMemo(() => {
      return `p-3 cursor-pointer transition-colors ${
        isSelected ? 'bg-blue-50 border-l-4 border-blue-500' : 
        isInternalHovered ? 'bg-slate-100' : 'hover:bg-slate-50'
      }`;
    }, [isSelected, isInternalHovered]);

    return (
      <div
        className={itemClassName}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {/* 移动端超紧凑布局 */}
        <div className="md:hidden">
          <div className="flex items-center gap-2 py-1">
            {/* 沙滩球图标 */}
            <div className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
              {(() => {
                const currentPNG = displayMode === 'traditional' ? beachBallPNG : tensorPNG;
                return currentPNG ? (
                  <img
                    src={currentPNG}
                    alt="沙滩球"
                    className="w-5 h-5 object-contain"
                    style={{
                      imageRendering: 'auto',
                      filter: 'contrast(1.1) brightness(1.05)'
                    }}
                  />
                ) : (
                  <div className="w-5 h-5 bg-slate-200 rounded-full animate-pulse"></div>
                );
              })()}
            </div>

            {/* 主要信息 - 水平布局 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-slate-700">
                    M{mechanism.magnitude.toFixed(1)}
                  </span>
                  <span className="text-xs text-slate-500">
                    {mechanism.date.slice(5)} {mechanism.time.slice(0, 5)}
                  </span>
                  <span className="text-xs text-slate-600">
                    {mechanism.depth.toFixed(1)}km
                  </span>
                  <span className="text-xs text-slate-600">
                    {displayMode === 'traditional' ? getFaultType(mechanism.rake1) : (mtFaultType || '计算中...')}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <button
                    onClick={handleZoomClick}
                    className="p-0.5 text-slate-400 hover:text-blue-500 transition-colors"
                    title="缩放到此位置"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                    </svg>
                  </button>
                  {displayMode === 'traditional' && (
                    <span className="text-xs text-slate-500">
                      {mechanism.misfit.toFixed(1)}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 桌面端布局 */}
        <div className="hidden md:flex items-start gap-3">
          {/* 沙滩球图标 */}
          <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center">
            {(() => {
              const currentPNG = displayMode === 'traditional' ? beachBallPNG : tensorPNG;
              return currentPNG ? (
                <img
                  src={currentPNG}
                  alt="沙滩球"
                  className="w-8 h-8 object-contain"
                  style={{
                    imageRendering: 'auto',
                    filter: 'contrast(1.1) brightness(1.05)'
                  }}
                />
              ) : (
                <div className="w-8 h-8 bg-slate-200 rounded-full animate-pulse"></div>
              );
            })()}
          </div>

          {/* 基本信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium text-slate-700">
                M{mechanism.magnitude.toFixed(1)}
              </span>
              <span className="text-xs text-slate-500">
                {mechanism.date} {mechanism.time}
              </span>
            </div>

            <div className="text-xs text-slate-600 space-y-0.5">
              <div>位置: {mechanism.latitude.toFixed(3)}°N, {mechanism.longitude.toFixed(3)}°E</div>
              <div>深度: {mechanism.depth.toFixed(1)}km | 类型: {
                displayMode === 'traditional' ? getFaultType(mechanism.rake1) : (mtFaultType || '计算中...')
              }</div>
            </div>
          </div>

          {/* 操作按钮和拟合度 */}
          <div className="flex flex-row items-center gap-1">
            <button
              onClick={handleZoomClick}
              className="p-1 text-slate-400 hover:text-blue-500 transition-colors"
              title="缩放到此位置"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
              </svg>
            </button>

            <div className="text-xs text-slate-500 min-w-0">
              {displayMode === 'traditional' ? `拟合: ${mechanism.misfit.toFixed(1)}` : `M${mechanism.magnitude.toFixed(1)}`}
            </div>
          </div>
        </div>
      </div>
    );
  }, (prevProps, nextProps) => {
    // 自定义比较函数，避免不必要的重新渲染
    return (
      prevProps.mechanism.id === nextProps.mechanism.id &&
      prevProps.isSelected === nextProps.isSelected &&
      prevProps.displayMode === nextProps.displayMode &&
      prevProps.mechanism.mxx === nextProps.mechanism.mxx &&
      prevProps.mechanism.myy === nextProps.mechanism.myy &&
      prevProps.mechanism.mzz === nextProps.mechanism.mzz &&
      prevProps.mechanism.mxy === nextProps.mechanism.mxy &&
      prevProps.mechanism.mxz === nextProps.mechanism.mxz &&
      prevProps.mechanism.myz === nextProps.mechanism.myz
    );
  });

  // 详细信息面板组件 - 简化版本
  const DetailedMechanismPanel = ({ 
    mechanism, 
    displayMode, 
    selectedMechanismPNG
  }: {
    mechanism: FocalMechanism;
    displayMode: 'traditional' | 'tensor';
    selectedMechanismPNG: string | null;
  }) => {
    // 简单的断层类型判断函数
    const getFaultType = (rake: number): string => {
      const absRake = Math.abs(rake);
      if (absRake < 30 || absRake > 150) {
        return '走滑断层';
      } else if (rake > 30 && rake < 150) {
        return '逆断层';
      } else {
        return '正断层';
      }
    };

    // 计算矩阵张量模式下的数据
    let nodalPlanes = null;
    let mtClassification = null;
    
    if (displayMode === 'tensor') {
      try {
        const mt: MomentTensor = {
          mxx: mechanism.mxx,
          myy: mechanism.myy,
          mzz: mechanism.mzz,
          mxy: mechanism.mxy,
          mxz: mechanism.mxz,
          myz: mechanism.myz
        };
        
        const axes = momentTensorToPrincipalAxes(mt);
        nodalPlanes = principalAxesToNodalPlanes(axes);
        mtClassification = classifyMomentTensor(mt);
      } catch (error) {
        console.error('矩阵张量计算错误:', error);
      }
    }

    if (displayMode === 'traditional') {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 左侧：沙滩球和基本信息 */}
          <div className="space-y-3">
            <div className="flex items-center justify-center p-4 bg-slate-50 rounded">
              {selectedMechanismPNG ? (
                <img
                  src={selectedMechanismPNG}
                  alt="震源机制解"
                  className="w-16 h-16 md:w-20 md:h-20 object-contain"
                  style={{
                    imageRendering: 'auto',
                    filter: 'contrast(1.1) brightness(1.05)'
                  }}
                />
              ) : (
                <div className="w-16 h-16 md:w-20 md:h-20 bg-slate-200 rounded-full animate-pulse"></div>
              )}
            </div>

            <div className="text-xs md:text-sm space-y-1">
              <div><strong>时间:</strong> {mechanism.date} {mechanism.time}</div>
              <div><strong>震级:</strong> M{mechanism.magnitude.toFixed(1)}</div>
              <div><strong>深度:</strong> {mechanism.depth.toFixed(1)}km</div>
              <div><strong>位置:</strong> {mechanism.latitude.toFixed(4)}°N, {mechanism.longitude.toFixed(4)}°E</div>
              <div><strong>拟合度:</strong> {mechanism.misfit.toFixed(1)}</div>
            </div>
          </div>

          {/* 右侧：断层参数 */}
          <div className="space-y-3">
            <div>
              <h6 className="font-medium text-slate-600 mb-2 text-xs md:text-sm">节面1参数</h6>
              <div className="text-xs md:text-sm space-y-1 bg-slate-50 p-2 rounded">
                <div>走向: {mechanism.strike1.toFixed(0)}°</div>
                <div>倾角: {mechanism.dip1.toFixed(0)}°</div>
                <div>滑动角: {mechanism.rake1.toFixed(0)}°</div>
                <div className="text-xs text-slate-500 mt-1">
                  类型: {getFaultType(mechanism.rake1)}
                </div>
              </div>
            </div>

            <div>
              <h6 className="font-medium text-slate-600 mb-2 text-xs md:text-sm">节面2参数</h6>
              <div className="text-xs md:text-sm space-y-1 bg-slate-50 p-2 rounded">
                <div>走向: {mechanism.strike2.toFixed(0)}°</div>
                <div>倾角: {mechanism.dip2.toFixed(0)}°</div>
                <div>滑动角: {mechanism.rake2.toFixed(0)}°</div>
                <div className="text-xs text-slate-500 mt-1">
                  类型: {getFaultType(mechanism.rake2)}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // 矩阵张量模式 - 显示计算出的节面参数
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* 左侧：沙滩球和基本信息 */}
          <div className="space-y-3">
            <div className="flex items-center justify-center p-4 bg-slate-50 rounded">
              {selectedMechanismPNG ? (
                <img
                  src={selectedMechanismPNG}
                  alt="震源机制解"
                  className="w-16 h-16 md:w-20 md:h-20 object-contain"
                  style={{
                    imageRendering: 'auto',
                    filter: 'contrast(1.1) brightness(1.05)'
                  }}
                />
              ) : (
                <div className="w-16 h-16 md:w-20 md:h-20 bg-slate-200 rounded-full animate-pulse"></div>
              )}
            </div>

            <div className="text-xs md:text-sm space-y-1">
              <div><strong>时间:</strong> {mechanism.date} {mechanism.time}</div>
              <div><strong>震级:</strong> M{mechanism.magnitude.toFixed(1)}</div>
              <div><strong>深度:</strong> {mechanism.depth.toFixed(1)}km</div>
              <div><strong>位置:</strong> {mechanism.latitude.toFixed(4)}°N, {mechanism.longitude.toFixed(4)}°E</div>
              {mtClassification && (
                <div><strong>震源类型:</strong> {mtClassification.description}</div>
              )}
            </div>
          </div>

          {/* 右侧：从矩阵张量计算的节面参数 */}
          <div className="space-y-3">
            {nodalPlanes ? (
              <>
                <div>
                  <h6 className="font-medium text-slate-600 mb-2 text-xs md:text-sm">节面1参数</h6>
                  <div className="text-xs md:text-sm space-y-1 bg-slate-50 p-2 rounded">
                    <div>走向: {nodalPlanes.strike1.toFixed(0)}°</div>
                    <div>倾角: {nodalPlanes.dip1.toFixed(0)}°</div>
                    <div>滑动角: {nodalPlanes.rake1.toFixed(0)}°</div>
                    <div className="text-xs text-slate-500 mt-1">
                      类型: {getFaultType(nodalPlanes.rake1)}
                    </div>
                  </div>
                </div>

                <div>
                  <h6 className="font-medium text-slate-600 mb-2 text-xs md:text-sm">节面2参数</h6>
                  <div className="text-xs md:text-sm space-y-1 bg-slate-50 p-2 rounded">
                    <div>走向: {nodalPlanes.strike2.toFixed(0)}°</div>
                    <div>倾角: {nodalPlanes.dip2.toFixed(0)}°</div>
                    <div>滑动角: {nodalPlanes.rake2.toFixed(0)}°</div>
                    <div className="text-xs text-slate-500 mt-1">
                      类型: {getFaultType(nodalPlanes.rake2)}
                    </div>
                  </div>
                </div>

                {mtClassification && (
                  <div>
                    <h6 className="font-medium text-slate-600 mb-2 text-xs md:text-sm">震源分类</h6>
                    <div className="text-xs md:text-sm space-y-1 bg-slate-50 p-2 rounded">
                      <div>DC: {mtClassification.dcPercentage.toFixed(1)}%</div>
                      <div>CLVD: {mtClassification.clvdPercentage.toFixed(1)}%</div>
                      <div>ISO: {mtClassification.isoPercentage.toFixed(1)}%</div>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-xs md:text-sm text-slate-500 italic">
                正在计算节面参数...
              </div>
            )}
          </div>
        </div>
      );
    }
  };

  // 稳定的事件处理器，避免每次渲染都重新创建
  const stableHandlers = useMemo(() => ({
    onSelect: handleSelectMechanism,
    onHover: handleHoverMechanism,
    onHoverLeave: () => handleHoverMechanism(null),
    onZoom: handleZoomToMechanism,
    getFaultType: getFaultType,
    getMechanismMomentTensor: getMechanismMomentTensor,
    getNodalPlanesFromMT: getNodalPlanesFromMT
  }), [handleSelectMechanism, handleHoverMechanism, handleZoomToMechanism, getFaultType, getMechanismMomentTensor, getNodalPlanesFromMT]);

  // 使用 useMemo 优化机制解列表渲染 - 移除悬停状态依赖
  const mechanismListContent = useMemo(() => {
    if (sortedMechanisms.length === 0) {
      return (
        <div className="p-4 text-center text-slate-500">
          暂无震源机制解数据（请尝试其它时空范围）
        </div>
      );
    }

    return (
      <div className="divide-y divide-slate-100">
        {sortedMechanisms.map((mechanism: FocalMechanism) => (
          <MechanismItem 
            key={mechanism.id} 
            mechanism={mechanism} 
            isSelected={localSelectedMechanism?.id === mechanism.id}
            onSelect={stableHandlers.onSelect}
            onHover={stableHandlers.onHover}
            onHoverLeave={stableHandlers.onHoverLeave}
            onZoom={stableHandlers.onZoom}
            getFaultType={stableHandlers.getFaultType}
            displayMode={displayMode}
            getMechanismMomentTensor={stableHandlers.getMechanismMomentTensor}
            getNodalPlanesFromMT={stableHandlers.getNodalPlanesFromMT}
          />
        ))}
      </div>
    );
  }, [
    sortedMechanisms, 
    localSelectedMechanism?.id, 
    stableHandlers,
    displayMode
  ]);

  if (loading && focalMechanisms.length === 0) {
    return (
      <div className="space-y-3">
        {/* 主标题 - 吸顶 */}
        <div className="sticky top-0 z-20 bg-white border-b border-slate-200 px-4 py-3">
          <h3 className="text-lg font-semibold text-slate-800">震源机制解</h3>
        </div>
        
        <div className="bg-slate-50 p-4 rounded text-center mx-3">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
          <p className="text-slate-500">正在加载震源机制解数据...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-3">
        {/* 主标题 - 吸顶 */}
        <div className="sticky top-0 z-20 bg-white border-b border-slate-200 px-4 py-3">
          <h3 className="text-lg font-semibold text-slate-800">震源机制解</h3>
        </div>
        
        <div className="bg-red-50 p-4 rounded text-center mx-3">
          <div className="text-red-500 mb-2">⚠️</div>
          <p className="text-red-600 text-sm">{error}</p>
          <button
            onClick={() => refetch()}
            className="mt-2 px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* 主标题 - 吸顶 */}
      <div className="sticky top-0 z-20 bg-white border-b border-slate-200 px-2 md:px-4 py-2 md:py-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm md:text-lg font-semibold text-slate-800">震源机制解</h3>
          <div className="flex items-center gap-1 md:gap-2">
            {/* 模式切换按钮 */}
            <div className="flex items-center bg-slate-50 rounded-md border border-slate-200 overflow-hidden">
              <button
                onClick={() => setFocalMechanismDisplayMode('traditional')}
                className={`px-1.5 md:px-2 py-1 text-xs font-medium transition-colors ${
                  displayMode === 'traditional'
                    ? 'bg-blue-500 text-white'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
                title="三参数模式"
              >
                三参数
              </button>
              <button
                onClick={() => setFocalMechanismDisplayMode('tensor')}
                className={`px-1.5 md:px-2 py-1 text-xs font-medium transition-colors ${
                  displayMode === 'tensor'
                    ? 'bg-blue-500 text-white'
                    : 'text-slate-600 hover:bg-slate-100'
                }`}
                title="矩阵张量模式"
              >
                矩阵张量
              </button>
            </div>

            <span className="text-xs md:text-sm text-slate-500">
              {statisticsData.filteredCount}/{statisticsData.totalCount}
            </span>
            {loading && <div className="animate-spin w-3 h-3 md:w-4 md:h-4 border border-blue-500 border-t-transparent rounded-full"></div>}
            <button
              onClick={handleZoomToGlobal}
              className="p-1 text-slate-400 hover:text-blue-500 transition-colors"
              title="缩放到全局"
              disabled={focalMechanisms.length === 0}
            >
              <svg className="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 主内容区域 - 垂直分布 */}
      <div className="flex flex-col mx-1.5 md:mx-3" style={{ height: localSelectedMechanism ? '400px' : '320px' }}>
        {/* 机制解列表 */}
        <div
          className="bg-white rounded-lg border border-slate-200 overflow-hidden transition-all duration-300 ease-out"
          style={{
            height: localSelectedMechanism
              ? `${100 - detailPanelHeight}%`
              : '100%'
          }}
        >
          {/* 列表内容 - 可滚动区域 */}
          <div className="h-full overflow-y-auto">
            {mechanismListContent}
          </div>
        </div>

        {/* 详细信息面板 - 从底部弹出 */}
        {localSelectedMechanism && (
          <div
            className="bg-white rounded-lg border border-slate-200 mt-2 transition-all duration-300 ease-out overflow-hidden"
            style={{ height: `${detailPanelHeight}%` }}
          >
            {/* 拖拽调整条 */}
            <div
              className="w-full h-2 bg-slate-100 hover:bg-slate-200 cursor-row-resize flex items-center justify-center transition-colors"
              onMouseDown={(e) => {
                e.preventDefault();
                const startY = e.clientY;
                const startHeight = detailPanelHeight;

                const handleMouseMove = (e: MouseEvent) => {
                  const deltaY = startY - e.clientY;
                  const containerHeight = 400;
                  const deltaPercent = (deltaY / containerHeight) * 100;
                  const newHeight = Math.max(20, Math.min(80, startHeight + deltaPercent));
                  setDetailPanelHeight(newHeight);
                };

                const handleMouseUp = () => {
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);
                };

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
              }}
            >
              <div className="w-8 h-1 bg-slate-400 rounded-full"></div>
            </div>

            {/* 详情面板头部 */}
            <div className="flex items-center justify-between p-2 md:p-3 border-b border-slate-200">
              <h5 className="text-sm md:text-base font-semibold text-slate-700">
                机制解详情
                <span className="ml-1 md:ml-2 text-xs px-1.5 md:px-2 py-1 rounded-full bg-slate-100 text-slate-600">
                  {displayMode === 'traditional' ? '三参数模式' : '矩阵张量模式'}
                </span>
              </h5>
              <div className="flex items-center gap-1 md:gap-2">
                <button
                  onClick={() => setDetailPanelHeight(isDetailPanelExpanded ? 35 : 75)}
                  className="p-1 text-slate-400 hover:text-blue-500 transition-colors"
                  title={isDetailPanelExpanded ? "收起" : "展开"}
                >
                  <svg className="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={!isDetailPanelExpanded ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} />
                  </svg>
                </button>
                <button
                  onClick={() => handleZoomToMechanism(localSelectedMechanism)}
                  className="p-1 text-slate-400 hover:text-blue-500 transition-colors"
                  title="缩放到此位置"
                >
                  <svg className="w-3 h-3 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  </svg>
                </button>
                <button
                  onClick={() => {
                    setLocalSelectedMechanism(null);
                    setSelectedFocalMechanism(null);
                  }}
                  className="text-slate-400 hover:text-slate-600 text-sm md:text-base"
                >
                  ✕
                </button>
              </div>
            </div>

            {/* 详情面板内容 */}
            <div className="p-2 md:p-3 overflow-y-auto" style={{ height: 'calc(100% - 50px)' }}>
              <DetailedMechanismPanel
                mechanism={localSelectedMechanism}
                displayMode={displayMode}
                selectedMechanismPNG={selectedMechanismPNG}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}