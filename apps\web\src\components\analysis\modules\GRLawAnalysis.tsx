import { useState, useEffect, useCallback, useMemo } from 'react';
import { useEarthquakeData } from '../../../hooks/useEarthquakeData';
import ReactECharts from 'echarts-for-react';

export function GRLawAnalysis() {
  const { earthquakes } = useEarthquakeData();
  const [analysisResult, setAnalysisResult] = useState<{
    bValue: number;
    bValueError: number;
    completeMagnitude: number;
    earthquakeCount: number;
    aValue: number;
    chartOption: any;
  } | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  // 节流计算函数
  const throttledCalculate = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        calculateGRLaw();
      }, 100);
    };
  }, [earthquakes]);

  // 数据变化时自动计算
  useEffect(() => {
    if (earthquakes && earthquakes.length > 0) {
      throttledCalculate();
    } else {
      setAnalysisResult(null);
    }
  }, [earthquakes, throttledCalculate]);

  const calculateGRLaw = useCallback(() => {
    if (!earthquakes || earthquakes.length === 0) {
      return;
    }

    setIsCalculating(true);

    try {
      // 提取有效震级数据
      const magnitudes = earthquakes
        .map(e => e.magnitude)
        .filter(m => m != null && !isNaN(m))
        .sort((a, b) => a - b);

      if (magnitudes.length < 20) {
        setAnalysisResult(null);
        return;
      }

      const minMag = Math.floor(magnitudes[0] * 10) / 10;
      const maxMag = Math.ceil(magnitudes[magnitudes.length - 1] * 10) / 10;

      // 创建完整的震级范围作为x轴
      const xAxisMagnitudes: number[] = [];
      for (let mag = minMag; mag <= maxMag; mag += 0.1) {
        xAxisMagnitudes.push(Math.round(mag * 10) / 10);
      }

      // 计算每个震级的数据
      const binCounts: number[] = [];
      const cumulativeCounts: number[] = [];

      for (const magnitude of xAxisMagnitudes) {
        // 频度分布：该震级区间的地震数量
        const binCount = magnitudes.filter(m => 
          m >= magnitude && m < magnitude + 0.1
        ).length;
        binCounts.push(binCount);

        // 累积数量：≥该震级的地震总数
        const cumCount = magnitudes.filter(m => m >= magnitude).length;
        cumulativeCounts.push(cumCount);
      }

      // 估算完备震级（频度分布的峰值对应的震级）
      const maxBinIndex = binCounts.indexOf(Math.max(...binCounts));
      const completeMagnitude = xAxisMagnitudes[maxBinIndex] + 0.2;

      // 计算b值（使用完备震级以上的数据）
      const completeMagnitudes = magnitudes.filter(m => m >= completeMagnitude);
      
      if (completeMagnitudes.length < 10) {
        setAnalysisResult(null);
        return;
      }

      // 最大似然估计b值
      const meanMag = completeMagnitudes.reduce((sum, m) => sum + m, 0) / completeMagnitudes.length;
      const bValue = 1 / (Math.log(10) * (meanMag - completeMagnitude + 0.05));
      const bValueError = bValue / Math.sqrt(completeMagnitudes.length);

      // 计算a值（在完备震级处的累积数量）
      const N_Mc = magnitudes.filter(m => m >= completeMagnitude).length;
      const aValue = Math.log10(N_Mc) + bValue * completeMagnitude;

      // 准备拟合线数据 - 使用稀疏数组，小于1的值不包含
      const fitLineData: (number | '-')[] = [];
      for (const magnitude of xAxisMagnitudes) {
        const fitCount = Math.pow(10, aValue - bValue * magnitude);
        fitLineData.push(fitCount >= 1 ? fitCount : '-'); // 小于1的值设为'-'，ECharts会跳过
      }

      // 配置ECharts选项
      const chartOption = {
        backgroundColor: 'white',
        title: null, // 确保没有标题
        grid: {
          left: '15%',
          right: '8%',
          bottom: '15%',
          top: '8%', // 减少顶部空间，因为没有标题
          containLabel: false
        },
        xAxis: {
          type: 'category',
          data: xAxisMagnitudes.map(m => m.toFixed(1)),
          name: '震级 (ML)',
          nameLocation: 'middle',
          nameGap: 35,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold',
            color: '#000'
          },
          axisLine: {
            lineStyle: { color: '#000', width: 1.5 }
          },
          axisLabel: {
            fontSize: 12,
            color: '#000',
            interval: 4 // 每5个刻度显示一个标签
          },
          axisTick: {
            lineStyle: { color: '#000', width: 1 },
            length: 5
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f0f0f0',
              width: 1
            }
          }
        },
        yAxis: {
          type: 'log',
          name: '地震数量',
          nameLocation: 'middle',
          nameGap: 55,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'bold',
            color: '#000'
          },
          min: 1,
          max: Math.max(10000, Math.max(...cumulativeCounts) * 2),
          axisLine: {
            lineStyle: { color: '#000', width: 1.5 }
          },
          axisLabel: {
            fontSize: 12,
            color: '#000',
            formatter: function(value: number) {
              if (value >= 1000) return '10⁴';
              if (value >= 100) return '10³';
              if (value >= 10) return '10²';
              if (value >= 1) return '10¹';
              return value.toString();
            }
          },
          axisTick: {
            lineStyle: { color: '#000', width: 1 },
            length: 5
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#f0f0f0',
              width: 1
            }
          }
        },
        series: [
          {
            name: '地震数量',
            type: 'bar',
            data: binCounts,
            itemStyle: {
              color: '#90EE90',
              borderColor: '#4CAF50',
              borderWidth: 0.5
            },
            barWidth: '60%',
            z: 1
          },
          {
            name: '累积数量',
            type: 'line',
            data: cumulativeCounts,
            itemStyle: {
              color: '#FF4444'
            },
            lineStyle: {
              color: '#FF4444',
              width: 0
            },
            symbol: 'circle',
            symbolSize: 6,
            showSymbol: true,
            showAllSymbol: true,
            z: 3
          },
          {
            name: 'GR拟合线',
            type: 'line',
            data: fitLineData,
            lineStyle: {
              color: '#000000',
              width: 2,
              type: 'dashed'
            },
            symbol: 'none',
            smooth: false,
            connectNulls: false,
            z: 2
          }
        ],
        legend: {
          data: ['累积数量', '地震数量', 'GR拟合线'],
          top: 5,
          right: 20,
          textStyle: {
            fontSize: 11,
            color: '#000'
          },
          itemWidth: 15,
          itemHeight: 10
        },
        graphic: [
          {
            type: 'text',
            left: '60%',
            top: '25%',
            style: {
              text: `log N = ${aValue.toFixed(2)} - ${bValue.toFixed(2)}M`,
              fontSize: 12,
              fontWeight: 'normal',
              fill: '#000000',
              backgroundColor: 'rgba(255,255,255,0.9)',
              border: 'none',
              padding: [6, 10]
            },
            z: 100
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            lineStyle: { color: '#666', width: 1, type: 'dashed' }
          },
          backgroundColor: 'rgba(255,255,255,0.95)',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: { color: '#000', fontSize: 11 },
          formatter: function(params: any) {
            let result = `震级: ${params[0].axisValue}<br/>`;
            params.forEach((param: any) => {
              if (param.seriesName === '地震数量' && param.value > 0) {
                result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
              } else if (param.seriesName === '累积数量' && param.value > 0) {
                result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
              } else if (param.seriesName === 'GR拟合线' && param.value && param.value >= 1) {
                result += `${param.marker}${param.seriesName}: ${param.value.toFixed(0)}<br/>`;
              }
            });
            return result;
          }
        }
      };

      setAnalysisResult({
        bValue,
        bValueError,
        completeMagnitude,
        earthquakeCount: magnitudes.length,
        aValue,
        chartOption
      });

    } finally {
      setIsCalculating(false);
    }
  }, [earthquakes]);

  return (
    <div className="space-y-3">
      <div className="bg-slate-50 p-3 rounded">
        <h4 className="font-semibold mb-2 text-sm">GR定律分析</h4>
        <p className="text-xs text-slate-600 mb-2">
          古登堡-里克特震级-频度关系分析
        </p>
        
        {isCalculating && (
          <div className="text-xs text-blue-600 text-center py-1">
            正在分析数据...
          </div>
        )}
        
        {!isCalculating && (!earthquakes || earthquakes.length === 0) && (
          <div className="text-xs text-slate-500 text-center py-1">
            等待地震数据...
          </div>
        )}

        {!isCalculating && earthquakes && earthquakes.length > 0 && earthquakes.length < 20 && (
          <div className="text-xs text-orange-600 text-center py-1">
            数据量不足，需要至少20个地震事件
          </div>
        )}
      </div>

      {analysisResult && (
        <div className="space-y-2">
          <div className="grid grid-cols-3 gap-2">
            <div className="bg-blue-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">地震数量</div>
              <div className="text-lg font-semibold text-blue-600">
                {analysisResult.earthquakeCount}
              </div>
            </div>
            
            <div className="bg-green-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600">完备震级</div>
              <div className="text-lg font-semibold text-green-600">
                {analysisResult.completeMagnitude.toFixed(1)}
              </div>
            </div>
            
            <div className="bg-purple-50 p-2 rounded text-center">
              <div className="text-xs text-slate-600"><i>b</i>值</div>
              <div className="text-lg font-semibold text-purple-600">
                {analysisResult.bValue.toFixed(2)} ± {analysisResult.bValueError.toFixed(2)}
              </div>
            </div>
          </div>

          <div className="bg-white p-3 rounded border border-slate-200">
            <ReactECharts 
              option={analysisResult.chartOption} 
              style={{ height: '350px', width: '100%' }}
              opts={{ renderer: 'canvas' }}
            />
          </div>
        </div>
      )}
    </div>
  );
} 