import { useRef, useEffect, useState } from 'react';
import { AudioSynthConfig, audioPresets, InstrumentType } from '../../types/audio';

interface AudioConfigPanelProps {
  config: AudioSynthConfig;
  onConfigChange: (config: AudioSynthConfig) => void;
  onClose: () => void;
  onPreviewInstrument?: (instrumentType: InstrumentType) => Promise<void>;
}

// 乐器选项 - 弦乐器、鼓、钢琴排前三
const instrumentOptions = {
  strings: {
    name: '弦乐器',
    description: '吉他弦音，温暖柔和',
    icon: '🎸',
    instrument: 'strings',
    source: 'Guitar Strings'
  },
  drums: {
    name: '鼓组',
    description: '真实鼓组采样，节奏感强',
    icon: '🥁',
    instrument: 'drums',
    source: 'Acoustic Kit'
  },
  piano: {
    name: '钢琴',
    description: 'Salamander 钢琴采样，清脆明亮',
    icon: '🎹',
    instrument: 'piano',
    source: 'Salamander Grand Piano'
  },
  casio: {
    name: 'Casio 电子音色',
    description: '经典电子音色，复古风格',
    icon: '🎛️',
    instrument: 'casio',
    source: 'Casio MT-40'
  },
  analog: {
    name: '模拟合成器',
    description: '温暖的模拟合成器音色',
    icon: '🎚️',
    instrument: 'analog',
    source: 'Berklee Analog Synth'
  },
  percussion: {
    name: '打击乐器',
    description: '锣声、拍手等打击乐器',
    icon: '🔔',
    instrument: 'percussion',
    source: 'Gongs & Claps'
  }
};

export function AudioConfigPanel({ config, onConfigChange, onClose, onPreviewInstrument }: AudioConfigPanelProps) {
  const panelRef = useRef<HTMLDivElement>(null);
  const [previewingInstrument, setPreviewingInstrument] = useState<string | null>(null);

  // 点击外侧关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const handleInstrumentSelect = (instrumentName: string) => {
    // 使用对应的预设配置
    const preset = audioPresets[instrumentName] || audioPresets.default;
    onConfigChange(preset);
  };

  const handlePreviewInstrument = async (instrumentType: string) => {
    if (!onPreviewInstrument) return;

    setPreviewingInstrument(instrumentType);
    try {
      await onPreviewInstrument(instrumentType as InstrumentType);
    } catch (error) {
      console.error('Preview failed:', error);
    } finally {
      // 延迟清除预览状态，等待地震序列播放完成（5个音符 * 0.6秒间隔 = 3秒）
      setTimeout(() => {
        setPreviewingInstrument(null);
      }, 3500);
    }
  };

  return (
    <div
      ref={panelRef}
      className="floating-panel-unified rounded-lg w-72 shadow-lg border border-slate-200/50 backdrop-blur-sm"
    >
      {/* 紧凑标题栏 */}
      <div className="flex items-center justify-between px-3 py-2 border-b border-slate-200/50 bg-gradient-to-r from-slate-50 to-white">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
          <h3 className="text-sm font-medium text-slate-800">音频乐器</h3>
        </div>
        <button
          onClick={onClose}
          className="text-slate-400 hover:text-slate-600 p-1 rounded-md hover:bg-slate-100/80 transition-all duration-200"
        >
          <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 紧凑内容区域 */}
      <div className="p-3">
        {/* 乐器网格 */}
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(instrumentOptions).map(([key, instrument]) => (
            <div key={key} className="relative group">
              <button
                onClick={() => handleInstrumentSelect(key)}
                className={`w-full p-3 rounded-lg border text-center transition-all duration-200 ${
                  config.selectedInstrument === key
                    ? 'border-orange-400 bg-gradient-to-br from-orange-50 to-orange-100 text-orange-900 shadow-md ring-2 ring-orange-200'
                    : 'border-slate-200 hover:border-slate-300 hover:bg-slate-50 hover:shadow-sm'
                }`}
              >
                <div className="text-xl mb-1.5">{instrument.icon}</div>
                <div className="text-xs font-medium text-slate-700">{instrument.name}</div>
                {config.selectedInstrument === key && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-orange-400 rounded-full flex items-center justify-center">
                    <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </div>
                )}
              </button>

              {/* 试听按钮 - 始终可见 */}
              {onPreviewInstrument && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePreviewInstrument(key);
                  }}
                  disabled={previewingInstrument === key}
                  className={`absolute bottom-1 right-1 w-6 h-6 rounded-full flex items-center justify-center text-xs transition-all duration-200 ${
                    previewingInstrument === key
                      ? 'bg-orange-500 text-white shadow-md'
                      : 'bg-white/90 hover:bg-white text-slate-600 hover:text-slate-700 shadow-sm hover:shadow-md border border-slate-200'
                  }`}
                  title={`试听 ${instrument.name}`}
                >
                  {previewingInstrument === key ? (
                    <svg className="w-3 h-3 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.617 14H2a1 1 0 01-1-1V7a1 1 0 011-1h2.617l3.766-2.793a1 1 0 011 0z" clipRule="evenodd" />
                      <path d="M12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" />
                    </svg>
                  ) : (
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.793L4.617 14H2a1 1 0 01-1-1V7a1 1 0 011-1h2.617l3.766-2.793a1 1 0 011 0z" clipRule="evenodd" />
                      <path d="M12 6.5a1 1 0 011.5-.866A6.002 6.002 0 0116.5 10a6.002 6.002 0 01-3 5.366 1 1 0 11-1-1.732A4.002 4.002 0 0014.5 10a4.002 4.002 0 00-2-3.464A1 1 0 0112 6.5z" />
                    </svg>
                  )}
                </button>
              )}
            </div>
          ))}
        </div>
      </div>


    </div>
  );
}
