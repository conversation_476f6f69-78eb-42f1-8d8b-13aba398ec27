import { useState, useMemo, useCallback } from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useFaultData, useWellData, useStationData } from '../../hooks/useLayerData';
import { LoadingSpinner, ErrorMessage } from '../ui/LoadingSpinner';
import type { Fault, WellTrajectory, Station } from '../../types';

interface LayerTableProps {
  isVisible: boolean;
  onClose: () => void;
  layerType: 'earthquakes' | 'faults' | 'wells' | 'stations';
}

type SortField = 'name' | 'level' | 'region' | 'status' | 'platform_name' | 'created_at';
type SortDirection = 'asc' | 'desc';

export function LayerTable({ isVisible, onClose, layerType }: LayerTableProps) {
  const {
    mapInstance,
    setSelectedFault,
    setSelectedWell,
    setSelectedStation
  } = useMapStore();
  
  // 根据图层类型获取数据
  const { faults, loading: faultsLoading, error: faultsError, refetch: refetchFaults } = useFaultData();
  const { wells, loading: wellsLoading, error: wellsError, refetch: refetchWells } = useWellData();
  const { stations, loading: stationsLoading, error: stationsError, refetch: refetchStations } = useStationData();

  const [sortField, setSortField] = useState<SortField>('created_at');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  // 获取当前图层的数据、加载状态和错误
  const { data, loading, error, refetch } = useMemo(() => {
    switch (layerType) {
      case 'faults':
        return { data: faults, loading: faultsLoading, error: faultsError, refetch: refetchFaults };
      case 'wells':
        return { data: wells, loading: wellsLoading, error: wellsError, refetch: refetchWells };
      case 'stations':
        return { data: stations, loading: stationsLoading, error: stationsError, refetch: refetchStations };
      default:
        return { data: [], loading: false, error: null, refetch: () => {} };
    }
  }, [layerType, faults, faultsLoading, faultsError, refetchFaults, wells, wellsLoading, wellsError, refetchWells, stations, stationsLoading, stationsError, refetchStations]);

  // 排序数据
  const sortedData = useMemo(() => {
    if (!data) return [];
    
    return [...data].sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortField) {
        case 'name':
          aValue = (a as any).name || (a as any).platform_name || '';
          bValue = (b as any).name || (b as any).platform_name || '';
          break;
        case 'level':
          aValue = (a as Fault).level || 0;
          bValue = (b as Fault).level || 0;
          break;
        case 'status':
          aValue = (a as Station).status || '';
          bValue = (b as Station).status || '';
          break;
        case 'region':
          aValue = a.region || '';
          bValue = b.region || '';
          break;
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        default:
          return 0;
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  }, [data, sortField, sortDirection]);

  // 分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return sortedData.slice(startIndex, startIndex + pageSize);
  }, [sortedData, currentPage, pageSize]);

  const totalPages = Math.ceil(sortedData.length / pageSize);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
    setCurrentPage(1);
  };

  const handleRowClick = useCallback((item: any) => {
    if (!mapInstance) {
      console.warn('地图实例未准备好');
      return;
    }

    // 计算偏移量，让定位点显示在可见区域的中心
    // LayerTable表格占据底部28%（移动端）、25%（平板端）、22%（桌面端）的高度
    // 相比UnifiedDataTable较小，所以偏移量也相应减少
    const getOffsetCenter = (lng: number, lat: number) => {
      const bounds = mapInstance.getBounds();
      const latRange = bounds.getNorth() - bounds.getSouth();

      // 根据屏幕尺寸计算不同的偏移量（比UnifiedDataTable小一些）
      let offsetRatio = 0.08; // 默认偏移比例

      if (window.innerWidth < 640) {
        // 移动端：表格占28%，向上偏移
        offsetRatio = 0.12;
      } else if (window.innerWidth < 1024) {
        // 平板端：表格占25%
        offsetRatio = 0.1;
      } else {
        // 桌面端：表格占22%
        offsetRatio = 0.08;
      }

      const latOffset = latRange * offsetRatio;
      return [lng, lat + latOffset];
    };

    if (layerType === 'stations') {
      const station = item as Station;
      // 设置选中状态
      setSelectedStation(String(station.id || station.name));
      // 缩放到要素
      const [offsetLng, offsetLat] = getOffsetCenter(station.longitude, station.latitude);
      mapInstance.flyTo({
        center: [offsetLng, offsetLat],
        zoom: Math.max(mapInstance.getZoom(), 14),
        duration: 1000
      });
    } else if (layerType === 'faults') {
      const fault = item as Fault;
      // 设置选中状态
      setSelectedFault(String(fault.id || fault.name));
      // 对于断层，解析坐标并移动到第一个点
      try {
        const coordinates = JSON.parse(fault.coordinates);
        if (coordinates && coordinates.length > 0) {
          const [lng, lat] = coordinates[0];
          const [offsetLng, offsetLat] = getOffsetCenter(lng, lat);
          mapInstance.flyTo({
            center: [offsetLng, offsetLat],
            zoom: Math.max(mapInstance.getZoom(), 12),
            duration: 1000
          });
        }
      } catch (e) {
        console.error('解析断层坐标失败:', e);
      }
    } else if (layerType === 'wellTrajectories' || layerType === 'wellPlatforms') {
      // 设置选中状态
      setSelectedWell(String(item.id || item.platform_name));
      // 对于井轨迹，解析坐标并移动到第一个点
      try {
        const coordinates = JSON.parse(item.coordinates);
        if (coordinates && coordinates.length > 0) {
          const [lng, lat] = coordinates[0];
          const [offsetLng, offsetLat] = getOffsetCenter(lng, lat);
          mapInstance.flyTo({
            center: [offsetLng, offsetLat],
            zoom: Math.max(mapInstance.getZoom(), 12),
            duration: 1000
          });
        }
      } catch (e) {
        console.error('解析井轨迹坐标失败:', e);
      }
    }
  }, [mapInstance, layerType, setSelectedStation, setSelectedFault, setSelectedWell]);

  const getLayerTitle = () => {
    switch (layerType) {
      case 'faults': return '断层数据';
      case 'wellTrajectories': return '井轨迹数据';
      case 'wellPlatforms': return '井平台数据';
      case 'stations': return '监测台站';
      default: return '数据表格';
    }
  };

  const SortIcon = ({ field }: { field: SortField }) => {
    if (sortField !== field) {
      return (
        <svg className="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
        </svg>
      );
    }
    
    return sortDirection === 'asc' ? (
      <svg className="w-4 h-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7"/>
      </svg>
    ) : (
      <svg className="w-4 h-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"/>
      </svg>
    );
  };

  if (!isVisible) return null;

  return (
    <div className="fixed z-40 fade-in
      inset-x-2 bottom-2 h-[28vh] max-h-[280px]
      sm:inset-x-4 sm:bottom-4 sm:h-[25vh] sm:max-h-[250px]
      lg:left-6 lg:right-6 lg:bottom-6 lg:h-[22vh] lg:max-h-[220px]
      max-w-5xl mx-auto">
      <div className="floating-panel-unified rounded-lg h-full flex flex-col shadow-xl">
        {/* Header */}
        <div className="px-3 py-2 border-b border-slate-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-base font-semibold text-slate-900">{getLayerTitle()}</h3>
              <p className="text-xs text-slate-600">
                显示 {sortedData.length} 条记录
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-md transition-colors"
              title="关闭表格"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>

        {/* Table */}
        <div className="flex-1 overflow-auto">
          {error ? (
            <div className="p-4">
              <ErrorMessage error={error} onRetry={refetch} />
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex flex-col items-center space-y-3">
                <LoadingSpinner size="lg" />
                <div className="text-slate-500">加载{getLayerTitle()}...</div>
              </div>
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-slate-50 sticky top-0">
                <tr>
                  <th
                    className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{layerType === 'wells' ? '平台名称' : '名称'}</span>
                      <SortIcon field="name" />
                    </div>
                  </th>
                  
                  {layerType === 'faults' && (
                    <th
                      className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                      onClick={() => handleSort('level')}
                    >
                      <div className="flex items-center space-x-1">
                        <span>等级</span>
                        <SortIcon field="level" />
                      </div>
                    </th>
                  )}
                  
                  {layerType === 'stations' && (
                    <>
                      <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                        坐标
                      </th>
                      <th
                        className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                        onClick={() => handleSort('status')}
                      >
                        <div className="flex items-center space-x-1">
                          <span>状态</span>
                          <SortIcon field="status" />
                        </div>
                      </th>
                    </>
                  )}

                  <th
                    className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                    onClick={() => handleSort('region')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>区域</span>
                      <SortIcon field="region" />
                    </div>
                  </th>

                  <th
                    className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider cursor-pointer hover:bg-slate-100"
                    onClick={() => handleSort('created_at')}
                  >
                    <div className="flex items-center space-x-1">
                      <span>创建时间</span>
                      <SortIcon field="created_at" />
                    </div>
                  </th>
                  
                  <th className="px-3 py-2 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {paginatedData.length === 0 ? (
                  <tr>
                    <td colSpan={layerType === 'stations' ? 6 : 5} className="px-3 py-4 text-center text-slate-500">
                      暂无{getLayerTitle()}
                    </td>
                  </tr>
                ) : (
                  paginatedData.map((item, index) => (
                    <tr 
                      key={item.id || index}
                      className="hover:bg-slate-50 cursor-pointer"
                      onClick={() => handleRowClick(item)}
                    >
                      <td className="px-3 py-2 text-sm text-slate-900">
                        {(item as any).name || (item as any).platform_name}
                      </td>

                      {layerType === 'faults' && (
                        <td className="px-3 py-2 text-sm text-slate-900">
                          {(item as Fault).level}级
                        </td>
                      )}

                      {layerType === 'stations' && (
                        <>
                          <td className="px-3 py-2 text-sm text-slate-900">
                            {(item as Station).latitude.toFixed(4)}, {(item as Station).longitude.toFixed(4)}
                          </td>
                          <td className="px-3 py-2 text-sm">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              (item as Station).status === 'active' ? 'bg-orange-100 text-orange-800' :
                              (item as Station).status === 'inactive' ? 'bg-gray-100 text-gray-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {(item as Station).status === 'active' ? '在线' :
                               (item as Station).status === 'inactive' ? '离线' : '维护中'}
                            </span>
                          </td>
                        </>
                      )}
                      
                      <td className="px-3 py-2 text-sm text-slate-900">
                        {item.region}
                      </td>

                      <td className="px-3 py-2 text-sm text-slate-900">
                        {new Date(item.created_at).toLocaleDateString('zh-CN')}
                      </td>

                      <td className="px-3 py-2 text-sm">
                        <button 
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRowClick(item);
                          }}
                          className="text-slate-600 hover:text-slate-900"
                          title="在地图上查看"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                          </svg>
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination */}
        <div className="px-3 py-2 border-t border-slate-200 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-slate-600">每页显示:</span>
            <select 
              value={pageSize} 
              onChange={(e) => {
                setPageSize(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="border border-slate-300 rounded px-2 py-1 text-sm"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 border border-slate-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-50"
            >
              上一页
            </button>
            <span className="text-sm text-slate-600">
              第 {currentPage} 页，共 {totalPages} 页
            </span>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 border border-slate-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-slate-50"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
