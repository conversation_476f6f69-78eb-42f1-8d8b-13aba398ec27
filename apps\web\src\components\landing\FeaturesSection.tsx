import React from 'react';

const features = [
  {
    icon: (
      <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
      </svg>
    ),
    title: '实时数据可视化',
    description: '高性能地震事件可视化，包含震级、深度和时间分析。数据每分钟自动更新，支持多种渲染模式，针对大规模数据集进行优化。'
  },
  {
    icon: (
      <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
    ),
    title: '多维数据分析',
    description: '整合断层系统、井轨迹和监测站的综合分析。支持多区域数据扩展，可关联地质特征与地震活动模式，提供跨区域对比分析。'
  },
  {
    icon: (
      <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
    ),
    title: '时间序列分析',
    description: '地震序列演化的时间序列回放和分析工具。支持可变播放速度和时间过滤，可追踪不同区域的地震活动历史趋势。'
  },
  {
    icon: (
      <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
      </svg>
    ),
    title: '交互式研究工具',
    description: '先进的交互功能，地图与表格同步显示。提供数据导出工具和过滤功能，支持学术研究工作流程，可扩展至新的监测区域。'
  }
];

export function FeaturesSection() {
  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-4">核心功能</h2>
          <p className="text-base sm:text-lg text-slate-600 max-w-3xl mx-auto px-4">
            为全面的地震研究和地质数据分析设计的先进分析工具，支持实时数据流和多区域监测
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-slate-50 rounded border border-slate-200 p-6 sm:p-8 hover:border-slate-300 transition-colors">
              <div className="flex items-start space-x-4">
                <div className="w-10 h-10 bg-slate-200 rounded flex items-center justify-center flex-shrink-0 mt-1">
                  {feature.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg sm:text-xl font-semibold text-slate-900 mb-3">{feature.title}</h3>
                  <p className="text-sm sm:text-base text-slate-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
