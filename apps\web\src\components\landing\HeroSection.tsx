import React from 'react';
import { useNavigate } from 'react-router-dom';

export function HeroSection() {
  const navigate = useNavigate();

  return (
    <section className="relative py-16 sm:py-24 lg:py-32" style={{
      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)',
      backgroundImage: `
        linear-gradient(rgba(71, 85, 105, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(71, 85, 105, 0.03) 1px, transparent 1px)
      `,
      backgroundSize: '20px 20px'
    }}>
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center">
          <div className="flex justify-center mb-8 sm:mb-12">
            <img
              src="/full-logo.svg"
              alt="ZJU-RISEMAP"
              className="h-32 sm:h-40 lg:h-48 w-auto max-w-full"
            />
          </div>

          <div className="mb-8 sm:mb-12">
            <p className="text-lg sm:text-xl lg:text-2xl text-slate-600 italic max-w-3xl mx-auto leading-relaxed px-4">
              全天候守护，智能响应每一次震动，洞察地壳的下一步。
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
            <button
              onClick={() => navigate('/map')}
              className="bg-slate-800 text-white px-6 sm:px-8 py-3 rounded text-sm sm:text-base font-medium hover:bg-slate-900 transition-colors inline-flex items-center justify-center min-h-[44px] touch-manipulation"
            >
              进入研究平台
              <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
              </svg>
            </button>
            <button className="border border-slate-300 text-slate-700 px-6 sm:px-8 py-3 rounded text-sm sm:text-base font-medium hover:bg-slate-50 transition-colors min-h-[44px] touch-manipulation">
              查看文档
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
