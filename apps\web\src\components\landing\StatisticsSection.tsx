import React from 'react';

export function StatisticsSection() {
  return (
    <section className="py-12 sm:py-16 bg-slate-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl font-bold text-slate-900 mb-4">数据规模</h2>
          <p className="text-sm sm:text-base text-slate-600 px-4">当前数据集统计和覆盖范围信息，支持实时更新和多区域扩展</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12">
          <div className="bg-white rounded border border-slate-200 p-4 sm:p-6 text-center">
            <div className="text-2xl sm:text-3xl font-bold text-slate-800 mb-2">6,237</div>
            <div className="text-sm sm:text-base font-medium text-slate-600">地震事件</div>
            <div className="text-xs sm:text-sm text-slate-500 mt-2">震级范围: -1.1 到 2.2</div>
          </div>

          <div className="bg-white rounded border border-slate-200 p-4 sm:p-6 text-center">
            <div className="text-2xl sm:text-3xl font-bold text-slate-800 mb-2">实时</div>
            <div className="text-sm sm:text-base font-medium text-slate-600">数据更新</div>
            <div className="text-xs sm:text-sm text-slate-500 mt-2">每分钟自动更新</div>
          </div>

          <div className="bg-white rounded border border-slate-200 p-4 sm:p-6 text-center sm:col-span-2 lg:col-span-1">
            <div className="text-2xl sm:text-3xl font-bold text-slate-800 mb-2">可扩展</div>
            <div className="text-sm sm:text-base font-medium text-slate-600">监测区域</div>
            <div className="text-xs sm:text-sm text-slate-500 mt-2">支持后台增加新区域</div>
          </div>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 sm:gap-6">
          <div className="text-center bg-white rounded border border-slate-200 p-3 sm:p-4">
            <div className="text-lg sm:text-xl font-semibold text-slate-800">89</div>
            <div className="text-xs sm:text-sm text-slate-600">监测台站</div>
          </div>
          <div className="text-center bg-white rounded border border-slate-200 p-3 sm:p-4">
            <div className="text-lg sm:text-xl font-semibold text-slate-800">103</div>
            <div className="text-xs sm:text-sm text-slate-600">井平台</div>
          </div>
          <div className="text-center bg-white rounded border border-slate-200 p-3 sm:p-4">
            <div className="text-lg sm:text-xl font-semibold text-slate-800">3</div>
            <div className="text-xs sm:text-sm text-slate-600">断层级别</div>
          </div>
          <div className="text-center bg-white rounded border border-slate-200 p-3 sm:p-4">
            <div className="text-lg sm:text-xl font-semibold text-slate-800">7km</div>
            <div className="text-xs sm:text-sm text-slate-600">最大深度</div>
          </div>
        </div>
      </div>
    </section>
  );
}
