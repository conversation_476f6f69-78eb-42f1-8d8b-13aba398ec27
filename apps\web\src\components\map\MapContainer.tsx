import { useMemo, useState, useEffect } from 'react';
import Map from 'react-map-gl/maplibre';
import { MapLayers } from './MapLayers';
import { MapControls } from '../ui/MapControls';
import { FloatingLogo } from '../ui/FloatingLogo';
import { FloatingLayerIcons } from '../ui/FloatingLayerIcons';
import { RightControlPanel } from '../ui/RightControlPanel';
import { ThreeModeToggle } from '../ui/ThreeModeToggle';
import { MapLegend } from '../ui/MapLegend';
import { MapScale } from '../ui/MapScale';
import { BottomTimelineBar } from '../ui/BottomTimelineBar';
import { UnifiedDataTable } from '../data/UnifiedDataTable';
import { DetailPanel } from '../ui/DetailPanel';
import { SpatialFilterToolbar } from '../ui/spatialFilter';
import { MobileSettingsButton } from '../ui/MobileSettingsButton';
import { MobileSettingsDrawer } from '../ui/MobileSettingsDrawer';
import { MobileAnalysisButton } from '../ui/MobileAnalysisButton';
import { AnalysisToolButton } from '../analysis/AnalysisToolButton';
import { ThreeViewer } from '../three/ThreeViewer';
import { useMapStore } from '../../stores/useMapStore';
import { useIsMobile } from '../../hooks/useIsMobile';
import { TimelinePlayer } from '../timeline/TimelinePlayer';
import { useTimelineState } from '../../hooks/useTimelineState';
import maplibregl from 'maplibre-gl';
import 'maplibre-gl/dist/maplibre-gl.css';

interface MapContainerProps {
  center?: [number, number];
  zoom?: number;
}

/**
 * 主地图组件 - 负责基础地图配置和渲染
 */
export function MapContainer({
  center = [105.45, 29.17],
  zoom = 10
}: MapContainerProps = {}) {
  // UI状态管理
  const [showDataTable, setShowDataTable] = useState(false);
  const [showLayerTable, setShowLayerTable] = useState<string | null>(null);
  const [showMobileSettings, setShowMobileSettings] = useState(false);
  const [showMobileAnalysis, setShowMobileAnalysis] = useState(false);

  // 三维模式状态
  const { isThreeMode } = useMapStore();

  // 移动端检测
  const { isMobile } = useIsMobile();

  // 时间轴状态
  const { isTimelineVisible } = useTimelineState();

  // 基础空白地图样式
  const emptyMapStyle = useMemo(() => ({
    version: 8 as const,
    glyphs: 'https://demotiles.maplibre.org/font/{fontstack}/{range}.pbf',
    sources: {},
    layers: [
      {
        id: 'basemap',
        type: 'background',
        layout: { visibility: 'none' },
        paint: {}
      },
      ...(new Array(100).fill(0).map((_, index) => ({
        id: `index_${index}`,
        type: 'background',
        layout: { visibility: 'none' },
        paint: {}
      })) as any)
    ]
  }), []);

  return (
    <div className="flex flex-col w-full h-full overflow-hidden">
      {/* 地图主区域 */}
      <div className="flex-1 relative">
        {isThreeMode ? (
          /* 三维视图 */
          <div className="w-full h-full bg-slate-100 relative">
            <ThreeViewer />

            {/* 三维模式下的图例 */}
            <MapLegend />

            {/* 三维模式下的数据表格 */}
            <UnifiedDataTable
              isVisible={showDataTable || !!showLayerTable}
              onClose={() => {
                setShowDataTable(false);
                setShowLayerTable(null);
              }}
              tableType={showDataTable ? 'earthquakes' : (showLayerTable as any)}
            />

            {/* 三维模式下的详情面板 */}
            <DetailPanel />
          </div>
        ) : (
          /* 二维地图 */
          <Map
            mapLib={maplibregl}
            initialViewState={{
              longitude: center[0],
              latitude: center[1],
              zoom: zoom
            }}
            style={{ width: '100%', height: '100%' }}
            mapStyle={emptyMapStyle}
            interactiveLayerIds={['earthquakes-base', 'earthquakes-hover', 'earthquakes-selected', 'faults', 'well-trajectories', 'well-platforms', 'stations']}
          >
            {/* 地图图层组件 */}
            <MapLayers />

            {/* 桌面端地图控制组件 */}
            {!isMobile && <MapControls />}

            {/* 空间筛选工具条 - 修复后的版本 */}
            <SpatialFilterToolbar/>

            {/* 统一数据表格 - 在Map组件内部，可以使用useMap hook */}
            <UnifiedDataTable
              isVisible={showDataTable || !!showLayerTable}
              onClose={() => {
                setShowDataTable(false);
                setShowLayerTable(null);
              }}
              tableType={showDataTable ? 'earthquakes' : (showLayerTable as any)}
            />

            {/* 详情面板 */}
            <DetailPanel />

            {/* 左下角图例 - 移到Map组件内部以便使用useMap hook */}
            <MapLegend />

            {/* 右下角比例尺 - 在Map组件内部以便使用useMap hook */}
            <MapScale />
          </Map>
        )}
      </div>

      {/* 浮动Logo和状态 (左上角) */}
      <FloatingLogo />

      {/* 桌面端浮动图层图标 */}
      {!isMobile && (
        <FloatingLayerIcons
          showDataTable={showDataTable}
          showLayerTable={showLayerTable}
          onShowLayerTable={(layerKey: string) => {
            if (layerKey === 'earthquakes') {
              // 地震数据表格切换逻辑
              setShowDataTable(!showDataTable);
              setShowLayerTable(null); // 关闭其他图层表格
            } else {
              // 其他图层表格切换逻辑
              if (showLayerTable === layerKey) {
                // 如果当前显示的就是这个图层表格，则关闭
                setShowLayerTable(null);
              } else {
                // 否则显示这个图层表格
                setShowLayerTable(layerKey);
                setShowDataTable(false); // 关闭地震数据表格
              }
            }
          }}
        />
      )}

      {/* 桌面端右侧底图切换面板 */}
      {!isMobile && <RightControlPanel />}

      {/* 移动端设置按钮 */}
      {isMobile && (
        <MobileSettingsButton onClick={() => {
          setShowMobileSettings(old => !old);
          setShowMobileAnalysis(false); // 关闭分析抽屉
        }} />
      )}

      {/* 移动端分析按钮 */}
      {isMobile && (
        <MobileAnalysisButton onClick={() => {
          setShowMobileAnalysis(old => !old);
          setShowMobileSettings(false); // 关闭设置抽屉
        }} />
      )}

      {/* 移动端设置抽屉 */}
      {isMobile && (
        <MobileSettingsDrawer
          isOpen={showMobileSettings}
          onClose={() => setShowMobileSettings(false)}
          showDataTable={showDataTable}
          showLayerTable={showLayerTable}
          type="settings"
          onShowLayerTable={(layerKey: string) => {
            if (layerKey === 'earthquakes') {
              // 地震数据表格切换逻辑
              setShowDataTable(!showDataTable);
              setShowLayerTable(null); // 关闭其他图层表格
            } else {
              // 其他图层表格切换逻辑
              if (showLayerTable === layerKey) {
                // 如果当前显示的就是这个图层表格，则关闭
                setShowLayerTable(null);
              } else {
                // 否则显示这个图层表格
                setShowLayerTable(layerKey);
                setShowDataTable(false); // 关闭地震数据表格
              }
            }
          }}
        />
      )}

      {/* 移动端分析抽屉 */}
      {isMobile && (
        <MobileSettingsDrawer
          isOpen={showMobileAnalysis}
          onClose={() => setShowMobileAnalysis(false)}
          type="analysis"
        />
      )}

      {/* 三维模式切换按钮 */}
      <ThreeModeToggle />

      {/* 桌面端分析工具按钮 */}
      {!isMobile && <AnalysisToolButton className={isThreeMode ? 'top-[340px]' : 'top-[280px]'} />}

      {/* 桌面端浮动时间轴 */}
      {!isMobile && <BottomTimelineBar />}

      {/* 移动端底部时间轴面板 */}
      {isMobile && (
        <div className="bg-white border-t border-slate-200 shadow-lg">
          <TimelinePlayer
            isVisible={true}
            onClose={() => {}}
            isEmbedded={true}
            isMobileBottom={true}
          />
        </div>
      )}

    </div>
  );
}
