import { useEffect } from 'react';
import { useMap } from 'react-map-gl/maplibre';
import { useMapStore } from '../../stores/useMapStore';

/**
 * 地图事件处理组件
 * 使用 useMap 获取地图实例并订阅事件
 */
export function MapEvents() {
  const { current: map } = useMap();

  const {
    setMapInstance,
    setSelectedEarthquake,
    setSelectedFault,
    setSelectedWell,
    setSelectedStation,
    setSelectedFocalMechanism,
    setHoveredEarthquake,
    setHoveredFault,
    setHoveredWell,
    setHoveredStation,
    setHoveredFocalMechanism,
    setPanelPinned,
    isDrawingMode,
  } = useMapStore();

  // 设置地图实例到store
  useEffect(() => {
    if (map) {
      setMapInstance(map);
    }
    return () => {
      setMapInstance(null);
    };
  }, [map, setMapInstance]);

  useEffect(() => {
    if (!map) return;

    // 获取当前存在的图层
    const getAvailableLayers = () => {
      const availableLayers = [];
      // 检查地震图层的所有子图层
      if (map.getLayer('earthquakes-base')) availableLayers.push('earthquakes-base');
      if (map.getLayer('earthquakes-hover')) availableLayers.push('earthquakes-hover');
      if (map.getLayer('earthquakes-selected')) availableLayers.push('earthquakes-selected');
      if (map.getLayer('faults')) availableLayers.push('faults');
      if (map.getLayer('well-trajectories')) availableLayers.push('well-trajectories');
      if (map.getLayer('well-platforms')) availableLayers.push('well-platforms');
      if (map.getLayer('well-platform-labels')) availableLayers.push('well-platform-labels');
      if (map.getLayer('stations')) availableLayers.push('stations');
      if (map.getLayer('focal-mechanisms')) availableLayers.push('focal-mechanisms');
      return availableLayers;
    };

    // 处理MapLibre图层点击事件
    const handleMapClick = (event: any) => {
      // 如果正在绘制，禁用所有地图点击事件
      if (isDrawingMode) {
        return;
      }

      const availableLayers = getAvailableLayers();
      if (availableLayers.length === 0) {
        // 点击空白区域，清除选中状态
        setSelectedFault(null);
        setSelectedWell(null);
        setSelectedStation(null);
        setSelectedFocalMechanism(null);
        setPanelPinned(false);
        return;
      }

      const features = map.queryRenderedFeatures(event.point, {
        layers: availableLayers
      });
      
      if (!features || features.length === 0) {
        // 点击空白区域，清除选中状态
        setSelectedEarthquake(null);
        setSelectedFault(null);
        setSelectedWell(null);
        setSelectedStation(null);
        setSelectedFocalMechanism(null);
        setPanelPinned(false);
        return;
      }

      const feature = features[0];
      const layerId = feature.layer.id;

      // 先清除其他图层的选中状态
      setSelectedEarthquake(null);
      setSelectedFault(null);
      setSelectedWell(null);
      setSelectedStation(null);
      setSelectedFocalMechanism(null);

      switch (layerId) {
        case 'earthquakes-base':
        case 'earthquakes-hover':
        case 'earthquakes-selected':
          setSelectedEarthquake(feature.properties.eventId);
          setPanelPinned(true);
          break;
        case 'faults':
          setSelectedFault(feature.properties.id);
          setPanelPinned(true);
          break;
        case 'well-trajectories':
        case 'well-platforms':
        case 'well-platform-labels':
          setSelectedWell(feature.properties.id);
          setPanelPinned(true);
          break;
        case 'stations':
          setSelectedStation(feature.properties.id);
          setPanelPinned(true);
          break;
        case 'focal-mechanisms':
          setSelectedFocalMechanism(feature.properties.id);
          setPanelPinned(true);
          break;
      }
    };

    // 处理MapLibre图层悬停事件
    const handleMapMouseMove = (event: any) => {
      // 如果正在绘制，禁用所有地图悬停事件
      if (isDrawingMode) {
        return;
      }

      const availableLayers = getAvailableLayers();
      if (availableLayers.length === 0) {
        // 清除所有hover状态和鼠标样式
        setHoveredFault(null);
        setHoveredWell(null);
        setHoveredStation(null);
        map.getCanvas().style.cursor = '';
        return;
      }

      const features = map.queryRenderedFeatures(event.point, {
        layers: availableLayers
      });
      if (!features || features.length === 0) {
        // 清除所有hover状态和鼠标样式
        setHoveredEarthquake(null);
        setHoveredFault(null);
        setHoveredWell(null);
        setHoveredStation(null);
        map.getCanvas().style.cursor = '';
        return;
      }

      const feature = features[0];
      const layerId = feature.layer.id;

      // 设置鼠标样式为指针
      map.getCanvas().style.cursor = 'pointer';

      // 清除其他图层的hover状态
      setHoveredEarthquake(null);
      setHoveredFault(null);
      setHoveredWell(null);
      setHoveredStation(null);
      setHoveredFocalMechanism(null);

      switch (layerId) {
        case 'earthquakes-base':
        case 'earthquakes-hover':
        case 'earthquakes-selected':
          setHoveredEarthquake(feature.properties.eventId);
          break;
        case 'faults':
          setHoveredFault(feature.properties.id);
          break;
        case 'well-trajectories':
        case 'well-platforms':
        case 'well-platform-labels':
          setHoveredWell(feature.properties.id);
          break;
        case 'stations':
          setHoveredStation(feature.properties.id);
          break;
        case 'focal-mechanisms':
          setHoveredFocalMechanism(feature.properties.id);
          break;
      }
    };

    // 处理鼠标离开地图事件
    const handleMapMouseLeave = () => {
      // 如果正在绘制，禁用所有地图离开事件
      if (isDrawingMode) {
        return;
      }

      setHoveredEarthquake(null);
      setHoveredFault(null);
      setHoveredWell(null);
      setHoveredStation(null);
      setHoveredFocalMechanism(null);
      map.getCanvas().style.cursor = '';
    };

    // 订阅事件
    map.on('click', handleMapClick);
    map.on('mousemove', handleMapMouseMove);
    map.on('mouseleave', handleMapMouseLeave);

    // 清理函数
    return () => {
      map.off('click', handleMapClick);
      map.off('mousemove', handleMapMouseMove);
      map.off('mouseleave', handleMapMouseLeave);
    };
  }, [
    map,
    setSelectedEarthquake,
    setSelectedFault,
    setSelectedWell,
    setSelectedStation,
    setHoveredEarthquake,
    setHoveredFault,
    setHoveredWell,
    setHoveredStation,
    setPanelPinned,
    isDrawingMode,
  ]);

  return null; // 这个组件不渲染任何内容
}
