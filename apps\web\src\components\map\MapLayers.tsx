import React from 'react';
import { MapEvents } from './MapEvents';
import { TiandituLayer } from './layers/TiandituLayer';
import { EarthquakeMapLibreLayer } from './layers/EarthquakeMapLibreLayer';
import { FaultLayer } from './layers/FaultLayer';
import { WellTrajectoryLayer } from './layers/WellTrajectoryLayer';
import { WellPlatformLayer } from './layers/WellPlatformLayer';
import { StationLayer } from './layers/StationLayer';
import { FocalMechanismLayer } from './layers/FocalMechanismLayer';
import { SpatialFilterLayer } from './layers/SpatialFilterLayer';
import { useMapIcons } from '../../hooks/useMapIcons';

/**
 * 地图图层容器组件
 * 组合所有独立的图层组件
 */
export const MapLayers = React.memo(() => {
  // 加载自定义图标
  useMapIcons();

  return (
    <>
      {/* 地图事件处理 */}
      <MapEvents />

      {/* 天地图底图 */}
      <TiandituLayer />

      {/* 地震图层（MapLibre原生） */}
      <EarthquakeMapLibreLayer />

      {/* 断层图层 */}
      <FaultLayer />

      {/* 井轨迹图层 */}
      <WellTrajectoryLayer />

      {/* 井平台图层 */}
      <WellPlatformLayer />

      {/* 台站图层 */}
      <StationLayer />

      {/* 震源机制解图层 */}
      <FocalMechanismLayer />

      {/* 空间筛选图层 */}
      <SpatialFilterLayer />
    </>
  );
});

MapLayers.displayName = 'MapLayers';
