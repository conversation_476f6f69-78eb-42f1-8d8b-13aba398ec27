import React, { useState, useEffect } from 'react';
import { Source, Layer, Marker } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';

// 特效乡镇点位组件
interface TownshipMarkerProps {
  longitude: number;
  latitude: number;
  name: string;
}

const TownshipMarker: React.FC<TownshipMarkerProps> = ({ longitude, latitude, name }) => {
  return (
    <Marker longitude={longitude} latitude={latitude} anchor="center">
      <div className="relative group">
        {/* 外圈脉冲效果 */}
        <div className="absolute inset-0 w-6 h-6 -translate-x-1/2 -translate-y-1/2 
                       bg-emerald-400 rounded-full opacity-25 
                       animate-ping" 
             style={{ animationDuration: '2.5s' }}></div>
        
        {/* 中圈呼吸效果 */}
        <div className="absolute inset-0 w-4 h-4 -translate-x-1/2 -translate-y-1/2 
                       bg-emerald-500 rounded-full opacity-35 
                       animate-pulse" 
             style={{ animationDuration: '3s' }}></div>
        
        {/* 主点 */}
        <div className="relative w-3 h-3 -translate-x-1/2 -translate-y-1/2 
                       bg-emerald-600 rounded-full border-2 border-white 
                       shadow-lg transform transition-transform duration-200 
                       group-hover:scale-125 group-hover:shadow-xl"></div>
        
        {/* 标签 - 默认显示 */}
        <div className="absolute top-full left-1/2 -translate-x-1/2 mt-2 
                       px-2 py-1 bg-white/90 backdrop-blur-sm rounded-md 
                       text-xs font-medium text-emerald-800 
                       border border-emerald-200 shadow-lg whitespace-nowrap
                       opacity-100 group-hover:opacity-100 transition-opacity duration-200
                       pointer-events-none z-10">
          {name}
          {/* 小箭头 */}
          <div className="absolute bottom-full left-1/2 -translate-x-1/2 
                         w-0 h-0 border-l-2 border-r-2 border-b-2 
                         border-l-transparent border-r-transparent border-b-emerald-200"></div>
        </div>
      </div>
    </Marker>
  );
};

/**
 * 行政区划图层组件 - 包含县级边界、乡镇边界和乡镇点
 */
export const AdministrativeLayer = React.memo(() => {
  const { 
    layerVisibility,
    administrativeSubLayers,
    setAdministrativeLoading,
  } = useMapStore();
  
  const [countyData, setCountyData] = useState<any>(null);
  const [townshipLineData, setTownshipLineData] = useState<any>(null);
  const [townshipPointData, setTownshipPointData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载 GeoJSON 数据
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setAdministrativeLoading(true); // 设置全局加载状态
        setError(null);

        // 并行加载三个数据文件
        const [countyResponse, townshipLineResponse, townshipPointResponse] = await Promise.all([
          fetch('/converted_geojson/line/县级边界.geojson'),
          fetch('/converted_geojson/line/乡镇边界.geojson'),
          fetch('/converted_geojson/point/乡镇点.geojson'),
        ]);

        if (!countyResponse.ok || !townshipLineResponse.ok || !townshipPointResponse.ok) {
          throw new Error('Failed to load administrative data');
        }

        const [county, townshipLine, townshipPoint] = await Promise.all([
          countyResponse.json(),
          townshipLineResponse.json(),
          townshipPointResponse.json(),
        ]);

        setCountyData(county);
        setTownshipLineData(townshipLine);
        setTownshipPointData(townshipPoint);
      } catch (err) {
        console.error('Error loading administrative data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
        setAdministrativeLoading(false); // 清除全局加载状态
      }
    };

    loadData();
  }, []);

  // 如果行政区划图层未开启，则不渲染
  if (!layerVisibility.administrative || loading || error) {
    return null;
  }

  return (
    <>
      {/* 县级行政区划边界 */}
      {administrativeSubLayers.countyBoundaries && countyData && (
        <Source id="county-boundaries-source" type="geojson" data={countyData}>
          <Layer
            id="county-boundaries"
            type="line"
            beforeId="index_85"
            paint={{
              'line-color': '#7C3AED', // 更醒目的紫色
              'line-width': [
                'interpolate',
                ['linear'],
                ['zoom'],
                6, 2,
                10, 3,
                14, 4
              ],
              'line-opacity': 0.9, // 提高透明度
              'line-dasharray': [4, 2] // 添加虚线样式
            }}
            layout={{
              'line-join': 'round',
              'line-cap': 'round'
            }}
          />
        </Source>
      )}

      {/* 乡镇行政区划边界 */}
      {administrativeSubLayers.townshipBoundaries && townshipLineData && (
        <Source id="township-boundaries-source" type="geojson" data={townshipLineData}>
          <Layer
            id="township-boundaries"
            type="line"
            beforeId="index_86"
            paint={{
              'line-color': '#9CA3AF', // 灰色，与三维模式一致
              'line-width': [
                'interpolate',
                ['linear'],
                ['zoom'],
                8, 1,
                12, 2,
                16, 3
              ],
              'line-opacity': 0.8, // 提高透明度
              'line-dasharray': [6, 3] // 添加虚线样式
            }}
            layout={{
              'line-join': 'round',
              'line-cap': 'round'
            }}
          />
        </Source>
      )}

      {/* 乡镇行政区划点 - 使用特效 Marker 组件 */}
      {administrativeSubLayers.townshipPoints && townshipPointData && 
        townshipPointData.features.map((feature: any, index: number) => {
          if (feature.geometry.type === 'Point') {
            const [longitude, latitude] = feature.geometry.coordinates;
            const name = feature.properties?.name || `乡镇-${index}`;
            
            return (
              <TownshipMarker
                key={`township-marker-${index}`}
                longitude={longitude}
                latitude={latitude}
                name={name}
              />
            );
          }
          return null;
        }).filter(Boolean)
      }
    </>
  );
});

AdministrativeLayer.displayName = 'AdministrativeLayer';