import React, { useMemo } from 'react';
import { Source, Layer } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import { useEarthquakeData } from '../../../hooks/useEarthquakeData';
import * as turf from '@turf/turf';

// Turbo色带颜色数组，从蓝到红色
export const turboColors = [
  '#30123b', '#4145ab', '#4675ed', '#39a2fc', '#1ac8d6', '#5fe4a0', '#a4fc3b', '#d1e231', '#faba39', '#f8765c', '#e73c7e', '#bd3786'
];
    

// 根据时间计算turbo颜色的函数
export const getTurboColorFromTime = (time: string, timeRange: { min: number, max: number } | null) => {
  if (!timeRange || timeRange.min === timeRange.max) {
    return '#23b7d6'; // 默认turbo中间色
  }
  
  const eventTime = new Date(time).getTime();
  const ratio = (eventTime - timeRange.min) / (timeRange.max - timeRange.min);
  
  // 根据ratio选择颜色
  const index = Math.floor(ratio * (turboColors.length - 1));
  const t = (ratio * (turboColors.length - 1)) - index;
  
  if (index >= turboColors.length - 1) {
    return turboColors[turboColors.length - 1];
  }
  
  // 简单插值
  const color1 = turboColors[index];
  const color2 = turboColors[index + 1];
  
  // 解析hex颜色
  const hex1 = color1.replace('#', '');
  const hex2 = color2.replace('#', '');
  
  const r1 = parseInt(hex1.substr(0, 2), 16);
  const g1 = parseInt(hex1.substr(2, 2), 16);
  const b1 = parseInt(hex1.substr(4, 2), 16);
  
  const r2 = parseInt(hex2.substr(0, 2), 16);
  const g2 = parseInt(hex2.substr(2, 2), 16);
  const b2 = parseInt(hex2.substr(4, 2), 16);
  
  const r = Math.round(r1 + (r2 - r1) * t);
  const g = Math.round(g1 + (g2 - g1) * t);
  const b = Math.round(b1 + (b2 - b1) * t);
  
  return `rgb(${r}, ${g}, ${b})`;
};

/**
 * 地震图层组件（MapLibre原生）
 */
export const EarthquakeMapLibreLayer = React.memo(() => {
  const {
    layerVisibility,
    filters,
    selectedEarthquake,
    hoveredEarthquake,
  } = useMapStore();

  // 获取数据
  const earthquakeData = useEarthquakeData();

  // 过滤地震数据
  const filteredData = useMemo(() => {
    const filtered = earthquakeData.layerData.filter(d => {
      // 时间过滤
      if (filters.timeRange) {
        const eventTime = new Date(d.time);
        if (eventTime < filters.timeRange.start || eventTime > filters.timeRange.end) {
          return false;
        }
      }

      // 震级过滤
      if (filters.magnitudeRange) {
        if (d.magnitude < filters.magnitudeRange[0] || d.magnitude > filters.magnitudeRange[1]) {
          return false;
        }
      }

      // 深度过滤
      if (filters.depthRange) {
        if (d.depth < filters.depthRange[0] || d.depth > filters.depthRange[1]) {
          return false;
        }
      }

      // 空间筛选
      if (filters.spatialFilter && filters.spatialFilter.center && filters.spatialFilter.radius) {
        const distance = turf.distance(
          d.coordinates,
          filters.spatialFilter.center,
          { units: 'kilometers' }
        );
        if (distance > filters.spatialFilter.radius) {
          return false;
        }
      }

      return true;
    });

    // 按时间排序，时间较早的在前面，较新的在后面
    // 这样较新的地震事件会渲染在上层
    return filtered.sort((a, b) => {
      const timeA = new Date(a.time).getTime();
      const timeB = new Date(b.time).getTime();
      return timeA - timeB; // 升序排列，较早的时间在前
    });
  }, [earthquakeData.layerData, filters.timeRange, filters.magnitudeRange, filters.depthRange]);

  // 计算时间范围用于颜色映射 - 使用时间轴的整体时间范围而不是过滤后的数据范围
  const timeRange = useMemo(() => {
    // 优先使用数据上下文中的整体时间范围
    if (earthquakeData.dataTimeRange) {
      return {
        min: earthquakeData.dataTimeRange.start.getTime(),
        max: earthquakeData.dataTimeRange.end.getTime()
      };
    }

    // 如果没有整体时间范围，则使用所有数据（不是过滤后的数据）计算
    if (earthquakeData.layerData.length === 0) return null;

    const times = earthquakeData.layerData.map(d => new Date(d.time).getTime());
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    return { min: minTime, max: maxTime };
  }, [earthquakeData.dataTimeRange, earthquakeData.layerData]);

  // 根据震级计算圆点大小
  const getRadiusFromMagnitude = (magnitude: number) => {
    const mag = Math.max(magnitude || 0, -1);
    
    if (mag < -0.5) return 3;      // 极小地震
    if (mag < 0) return 4;         // 微震
    if (mag < 0.5) return 5;       // 小微震
    if (mag < 1) return 6;         // 小震
    if (mag < 1.5) return 7;       // 中小震
    if (mag < 2) return 8;         // 中震
    if (mag < 2.5) return 9;       // 较大地震
    if (mag < 3) return 10;         // 大地震
    if (mag < 3.5) return 11;      // 强震
    if (mag < 4) return 12;        // 强震
    return 13;                     // 极强震
  };

  // 准备地震数据为GeoJSON
  const earthquakeGeoJSON = useMemo(() => ({
    type: 'FeatureCollection' as const,
    features: filteredData.map((earthquake, index) => ({
      type: 'Feature' as const,
      properties: {
        id: String(earthquake.eventId || index),
        eventId: earthquake.eventId,
        magnitude: earthquake.magnitude || 0,
        depth: earthquake.depth || 0,
        time: earthquake.time,
        // 计算基础半径 - 根据震级变化，使用新的分级
        baseRadius: getRadiusFromMagnitude(earthquake.magnitude || 0),
        // 计算颜色 - 根据时间变化，使用turbo色带
        color: getTurboColorFromTime(earthquake.time, timeRange)
      },
      geometry: {
        type: 'Point' as const,
        coordinates: earthquake.coordinates
      }
    }))
  }), [filteredData, timeRange]);

  if (!layerVisibility.earthquakes) {
    console.log('🚫 EarthquakeMapLibreLayer 图层不可见，跳过渲染');
    return null;
  }

  return (
    <Source id="earthquakes-source" type="geojson" data={earthquakeGeoJSON}>
      {/* 基础地震图层 - 只显示非hover和非selected的点 */}
      <Layer
        id="earthquakes-base"
        type="circle"
        filter={[
          'all',
          ['!=', ['get', 'eventId'], selectedEarthquake || ''],
          ['!=', ['get', 'eventId'], hoveredEarthquake || '']
        ]}
        paint={{
          'circle-radius': ['get', 'baseRadius'],
          'circle-color': ['get', 'color'],
          'circle-opacity': 0.8,
          'circle-stroke-color': '#ffffff',
          'circle-stroke-width': 1,
          'circle-stroke-opacity': 0.6
        }}
      />

      {/* Hover状态图层 - 显示在基础图层之上 */}
      <Layer
        id="earthquakes-hover"
        type="circle"
        filter={[
          'all',
          ['==', ['get', 'eventId'], hoveredEarthquake || ''],
          ['!=', ['get', 'eventId'], selectedEarthquake || '']
        ]}
        paint={{
          'circle-radius': ['*', ['get', 'baseRadius'], 1.3],
          'circle-color': '#f97316', // 橙色高亮
          'circle-opacity': 0.9,
          'circle-stroke-color': '#ffffff',
          'circle-stroke-width': 2,
          'circle-stroke-opacity': 1.0
        }}
      />

      {/* Selected状态图层 - 显示在最顶层 */}
      <Layer
        id="earthquakes-selected"
        type="circle"
        filter={['==', ['get', 'eventId'], selectedEarthquake || '']}
        paint={{
          'circle-radius': ['*', ['get', 'baseRadius'], 1.5],
          'circle-color': '#ffff00', // 黄色高亮
          'circle-opacity': 1.0,
          'circle-stroke-color': '#ffffff',
          'circle-stroke-width': 3,
          'circle-stroke-opacity': 1.0
        }}
      />
    </Source>
  );
});

EarthquakeMapLibreLayer.displayName = 'EarthquakeMapLibreLayer';
