import React, { useMemo } from 'react';
import { Source, Layer } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import { useAllLayerData } from '../../../hooks/useLayerData';

/**
 * 断层图层组件
 */
export const FaultLayer = React.memo(() => {
  const { 
    layerVisibility, 
    selectedFault, 
    hoveredFault 
  } = useMapStore();
  
  const layerData = useAllLayerData();

  // 准备断层数据为GeoJSON
  const faultGeoJSON = useMemo(() => ({
    type: 'FeatureCollection' as const,
    features: layerData.faults.layerData.map((fault: any, index: number) => ({
      type: 'Feature' as const,
      properties: {
        id: String(fault.id || index),
        name: fault.name || `断层${index + 1}`,
        level: fault.level || 3 // 添加断层等级属性
      },
      geometry: {
        type: 'LineString' as const,
        coordinates: fault.coordinates
      }
    }))
  }), [layerData.faults.layerData]);

  if (!layerVisibility.faults) {
    return null;
  }

  return (
    <Source id="faults-source" type="geojson" data={faultGeoJSON}>
      <Layer
        id="faults"
        type="line"
        beforeId="index_90"
        paint={{
          'line-color': [
            'case',
            ['==', ['get', 'id'], selectedFault || ''],
            '#fbbf24', // 选中状态：黄色
            ['==', ['get', 'id'], hoveredFault || ''],
            '#f97316', // 悬停状态：橙色
            // 默认状态：根据断层等级设置颜色
            [
              'case',
              ['==', ['get', 'level'], 1], '#dc2626', // 一级断层：深红色
              ['==', ['get', 'level'], 2], '#ea580c', // 二级断层：橙红色
              '#eab308' // 三级断层：黄色
            ]
          ],
          'line-width': [
            'case',
            ['==', ['get', 'id'], selectedFault || ''],
            5, // 选中状态：统一使用二级断层粗细
            ['==', ['get', 'id'], hoveredFault || ''],
            4, // 悬停状态：统一使用二级断层粗细
            3  // 默认状态：统一使用二级断层粗细，不区分等级
          ],
          'line-opacity': [
            'case',
            ['==', ['get', 'id'], selectedFault || ''],
            1.0, // 选中状态：完全不透明
            ['==', ['get', 'id'], hoveredFault || ''],
            0.9, // 悬停状态：高透明度
            0.8  // 默认状态：中等透明度
          ]
        }}
      />
    </Source>
  );
});

FaultLayer.displayName = 'FaultLayer';
