import React, { useMemo, useCallback, useState, useEffect, useRef } from 'react';
import { Marker } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import { useFocalMechanismData } from '../../../hooks/useFocalMechanismData';
import { getOrCreatePNG, getOrCreatePNGFromMT, createMomentTensorFromArray } from '../../../utils/beachball';
import type { FocalMechanism } from '../../../types';
import type { MomentTensor } from '../../../utils/beachball';

// 沙滩球组件
interface BeachBallProps {
  mechanism: FocalMechanism;
  isSelected: boolean;
  isHovered: boolean;
  isOtherSelected: boolean; // 是否有其他球被选中
  onClick: (e: React.MouseEvent<HTMLDivElement>) => void;
  onMouseEnter: (e: React.MouseEvent<HTMLDivElement>) => void;
  onMouseLeave: (e: React.MouseEvent<HTMLDivElement>) => void;
  zIndex?: number;
  displayMode?: 'traditional' | 'tensor';
}

const BeachBall: React.FC<BeachBallProps> = React.memo(({
  mechanism,
  isSelected,
  isHovered,
  isOtherSelected,
  onClick,
  onMouseEnter,
  onMouseLeave,
  zIndex = 1,
  displayMode = 'traditional'
}) => {
  // 分离基础大小和状态相关的样式
  const baseSize = 32;
  const [isVisible, setIsVisible] = useState(false);
  const hasAppearedRef = useRef(false);

  // PNG状态
  const [pngDataUri, setPngDataUri] = useState<string | null>(null);

  // 获取PNG图片
  useEffect(() => {
    if (displayMode === 'traditional') {
      // 传统三参数模式
      getOrCreatePNG(
        mechanism.strike1,
        mechanism.dip1,
        mechanism.rake1,
        baseSize // 使用固定的基础大小
      ).then(setPngDataUri);
    } else {
      // 矩阵张量模式
      try {
        // 直接使用真实的矩阵张量数据
        const mt: MomentTensor = {
          mxx: mechanism.mxx,
          myy: mechanism.myy,
          mzz: mechanism.mzz,
          mxy: mechanism.mxy,
          mxz: mechanism.mxz,
          myz: mechanism.myz
        };

        getOrCreatePNGFromMT(mt, baseSize).then(setPngDataUri);
      } catch (error) {
        console.error('矩阵张量计算错误:', error);
        // 降级到传统模式
        getOrCreatePNG(
          mechanism.strike1,
          mechanism.dip1,
          mechanism.rake1,
          baseSize
        ).then(setPngDataUri);
      }
    }
  }, [mechanism.strike1, mechanism.dip1, mechanism.rake1, mechanism.id, mechanism.mxx, mechanism.myy, mechanism.mzz, mechanism.mxy, mechanism.mxz, mechanism.myz, displayMode]);

  // 出现动画 - 只在组件首次挂载时执行一次
  useEffect(() => {
    if (!hasAppearedRef.current) {
      hasAppearedRef.current = true;
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, Math.random() * 500); // 随机延迟，产生波浪效果

      return () => clearTimeout(timer);
    }
  }, []);

  // 动态计算大小和样式
  const dynamicStyles = useMemo(() => {
    // 如果还未显示，则使用初始状态
    if (!isVisible) {
      return {
        scale: 0,
        opacity: 0,
        filter: 'contrast(1.1) brightness(1.05)',
        isInitialShow: true
      };
    }

    // 基础状态（已显示后的正常状态）
    let scale = 1;
    let opacity = 0.8;
    let filter = 'contrast(1.1) brightness(1.05)';
    let boxShadow = 'none';

    // 状态相关的调整 - 选中状态优先级最高，始终完全不透明
    if (isSelected) {
      // 选中状态：始终在顶层且完全不透明，不受其他状态影响
      scale = 1.5;
      opacity = 1.0; // 完全不透明，不受isOtherSelected影响
      filter = 'contrast(1.2) brightness(1.1) drop-shadow(0 4px 8px rgba(0,0,0,0.3))';
    } else if (isHovered) {
      // 悬停状态：仅在未选中时生效
      scale = 1.25;
      opacity = isOtherSelected ? 0.5 : 0.95; // 如果有其他选中，稍微降低透明度
      filter = 'contrast(1.15) brightness(1.08)';
    } else if (isOtherSelected) {
      // 当有其他球被选中时，半隐藏当前球
      opacity = 0.3;
    }

    return {
      scale,
      opacity,
      filter,
      boxShadow,
      isInitialShow: false
    };
  }, [isSelected, isHovered, isOtherSelected, isVisible]);

  // 容器样式
  const containerStyle = useMemo(() => ({
    transform: `scale(${dynamicStyles.scale})`,
    opacity: dynamicStyles.opacity,
    filter: dynamicStyles.filter,
    transition: dynamicStyles.isInitialShow 
      ? 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)' // 初始出现动画 - 较慢的弹性动画
      : 'transform 0.15s ease-out, opacity 0.15s ease-out, filter 0.15s ease-out, box-shadow 0.15s ease-out', // 状态变化动画 - 快速响应
    position: 'relative' as const,
    zIndex,
    width: baseSize,
    height: baseSize
  }), [dynamicStyles, zIndex]);

  // 脉冲效果（仅在选中时显示）
  const pulseRingStyle = useMemo(() => ({
    position: 'absolute' as const,
    top: '50%',
    left: '50%',
    width: baseSize * 2,
    height: baseSize * 2,
    transform: 'translate(-50%, -50%)',
    borderRadius: '50%',
    border: '2px solid rgba(59, 130, 246, 0.4)',
    opacity: isSelected && isVisible ? 1 : 0,
    animation: isSelected && isVisible ? 'pulse-ring 2s infinite' : 'none',
    pointerEvents: 'none' as const
  }), [isSelected, isVisible]);

  // 添加自定义CSS动画
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      @keyframes pulse-ring {
        0% {
          transform: translate(-50%, -50%) scale(0.8);
          opacity: 0.8;
        }
        50% {
          transform: translate(-50%, -50%) scale(1.2);
          opacity: 0.3;
        }
        100% {
          transform: translate(-50%, -50%) scale(1.5);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
    return () => {
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
    };
  }, []);

  return (
    <div className="relative flex flex-col items-center">
      {/* 脉冲圆环 */}
      <div style={pulseRingStyle} />

      {/* 沙滩球容器 */}
      <div
        className="cursor-pointer flex items-center justify-center"
        style={containerStyle}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        title={`震源机制解 M${mechanism.magnitude.toFixed(1)}`}
      >
        {pngDataUri ? (
          <img
            src={pngDataUri}
            alt="震源机制解"
            style={{
              width: baseSize,
              height: baseSize,
              imageRendering: 'auto'
            }}
          />
        ) : (
          <div
            style={{
              width: baseSize,
              height: baseSize,
              backgroundColor: '#e2e8f0',
              borderRadius: '50%',
              opacity: 0.5
            }}
            className="animate-pulse"
          />
        )}
      </div>

      {/* 震级标签 - 只在hover和active状态下显示 */}
      {(isSelected || isHovered) && (
        <div
          className="absolute px-1 py-0.5 bg-white border border-gray-300 rounded text-xs font-medium text-gray-700 shadow-sm pointer-events-none"
          style={{
            fontSize: '10px',
            opacity: !isVisible ? 0 : 1,
            transform: `translateX(-50%) scale(${!isVisible ? 0.8 : 1})`, // 标签不跟随沙滩球缩放
            left: '50%',
            top: isSelected ? `${baseSize * 1.1}px` : `${baseSize * 1.0}px`, // 标签位置更往下
            transition: isVisible ? 'all 0.15s ease-out' : 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)',
            zIndex: zIndex - 1 // 让标签在沙滩球下方
          }}
        >
          M{mechanism.magnitude.toFixed(1)}
        </div>
      )}

      {/* 选中状态的光环效果 */}
      {isSelected && isVisible && (
        <div
          className="absolute top-1/2 left-1/2 rounded-full pointer-events-none"
          style={{
            width: baseSize * 1.8,
            height: baseSize * 1.8,
            transform: 'translate(-50%, -50%)',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%)',
            animation: 'pulse 2s infinite',
            zIndex: zIndex - 1
          }}
        />
      )}
    </div>
  );
});

BeachBall.displayName = 'BeachBall';

/**
 * 震源机制解图层组件
 * 优化版本：使用分层渲染确保选中和悬停的marker在顶层
 */
export const FocalMechanismLayer = React.memo(() => {
  const {
    layerVisibility,
    selectedFocalMechanism,
    hoveredFocalMechanism,
    setSelectedFocalMechanism,
    setHoveredFocalMechanism,
    focalMechanismDisplayMode
  } = useMapStore();

  // 使用新的context获取过滤后的震源机制解数据
  const { focalMechanisms, loading } = useFocalMechanismData();

  // 优化事件处理函数，避免每次渲染都创建新函数 - 支持切换选中状态
  const handleMechanismClick = useCallback((mechanismId: string) => {
    // 如果点击的是已选中的机制解，则取消选中
    if (selectedFocalMechanism === mechanismId) {
      setSelectedFocalMechanism(null);
    } else {
      setSelectedFocalMechanism(mechanismId);
    }
  }, [selectedFocalMechanism, setSelectedFocalMechanism]);

  const handleMechanismMouseEnter = useCallback((mechanismId: string) => {
    setHoveredFocalMechanism(mechanismId);
  }, [setHoveredFocalMechanism]);

  const handleMechanismMouseLeave = useCallback(() => {
    setHoveredFocalMechanism(null);
  }, [setHoveredFocalMechanism]);

  // 统一渲染所有markers，避免状态变化时重新创建组件
  const allMarkers = useMemo(() => {
    // 如果图层不可见、正在加载或没有数据，返回空数组
    if (!layerVisibility.focalMechanisms || loading || focalMechanisms.length === 0) {
      return [];
    }

    // 检查是否有选中的机制解
    const hasSelectedMechanism = selectedFocalMechanism !== null;

    return focalMechanisms.map((mechanism) => {
      const mechanismId = String(mechanism.id);
      const isSelected = selectedFocalMechanism === mechanismId;
      const isHovered = hoveredFocalMechanism === mechanismId;
      const isOtherSelected = hasSelectedMechanism && !isSelected;

      // 确定zIndex - 选中状态最高优先级，确保始终在顶层
      let zIndex = 1; // 普通状态
      if (isSelected) {
        zIndex = 10000; // 选中状态：最高优先级，始终在顶层
      } else if (isHovered) {
        zIndex = 999; // 悬停状态：次优先级
      }

      return (
        <Marker
          key={mechanism.id} // 使用稳定的key，避免重新创建组件
          longitude={mechanism.longitude}
          latitude={mechanism.latitude}
          anchor="center"
        >
          <BeachBall
            mechanism={mechanism}
            isSelected={isSelected}
            isHovered={isHovered}
            isOtherSelected={isOtherSelected}
            onClick={(e: React.MouseEvent<HTMLDivElement>) => {
              e.stopPropagation();
              handleMechanismClick(mechanismId);
            }}
            onMouseEnter={() => handleMechanismMouseEnter(mechanismId)}
            onMouseLeave={handleMechanismMouseLeave}
            zIndex={zIndex}
            displayMode={focalMechanismDisplayMode}
          />
        </Marker>
      );
    });
  }, [
    layerVisibility.focalMechanisms,
    loading,
    focalMechanisms,
    selectedFocalMechanism,
    hoveredFocalMechanism,
    handleMechanismClick,
    handleMechanismMouseEnter,
    handleMechanismMouseLeave,
    focalMechanismDisplayMode
  ]);

  // 现在可以安全地进行条件渲染
  if (!layerVisibility.focalMechanisms || loading || focalMechanisms.length === 0) {
    return null;
  }

  return (
    <>
      {/* 统一渲染所有markers，通过zIndex控制层级 */}
      {allMarkers}
    </>
  );
});

FocalMechanismLayer.displayName = 'FocalMechanismLayer';