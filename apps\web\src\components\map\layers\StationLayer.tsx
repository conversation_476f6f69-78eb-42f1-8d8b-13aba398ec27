import React, { useMemo } from 'react';
import { Source, Layer } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import { useAllLayerData } from '../../../hooks/useLayerData';

/**
 * 监测台站图层组件
 */
export const StationLayer = React.memo(() => {
  const { 
    layerVisibility, 
    selectedStation, 
    hoveredStation 
  } = useMapStore();
  
  const layerData = useAllLayerData();

  // 准备台站数据为GeoJSON
  const stationGeoJSON = useMemo(() => ({
    type: 'FeatureCollection' as const,
    features: layerData.stations.layerData.map((station: any, index: number) => ({
      type: 'Feature' as const,
      properties: {
        id: String(station.id || index),
        name: station.name || `台站${index + 1}`,
        status: station.status || 'active'
      },
      geometry: {
        type: 'Point' as const,
        coordinates: station.coordinates
      }
    }))
  }), [layerData.stations.layerData]);

  if (!layerVisibility.stations) {
    return null;
  }

  return (
    <Source id="stations-source" type="geojson" data={stationGeoJSON}>
      {/* 台站图标图层 - 使用PNG图标 */}
      <Layer
        id="stations"
        type="symbol"
        beforeId="index_92"
        layout={{
          'icon-image': 'monitoring-station-icon', // 使用PNG图标
          'icon-size': [
            'case',
            ['==', ['get', 'id'], selectedStation || ''],
            1.2, // 选中状态：大图标
            ['==', ['get', 'id'], hoveredStation || ''],
            1.0, // 悬停状态：中等图标
            0.8  // 默认状态：小图标
          ],
          'icon-allow-overlap': true,
          'icon-ignore-placement': true
        }}
        paint={{
          'icon-color': [
            'case',
            // 选中状态：黄色高亮
            ['==', ['get', 'id'], selectedStation || ''],
            '#fbbf24',
            // 悬停状态：根据原状态显示更亮的颜色
            ['==', ['get', 'id'], hoveredStation || ''],
            [
              'case',
              ['==', ['get', 'status'], 'active'],
              '#fb923c', // 在线悬停：更亮的橙色
              ['==', ['get', 'status'], 'maintenance'],
              '#60a5fa', // 维护悬停：更亮的蓝色
              '#9ca3af'  // 不在线悬停：更亮的灰色
            ],
            // 根据台站状态显示不同颜色
            ['==', ['get', 'status'], 'active'],
            '#f97316', // 在线状态：橙色
            ['==', ['get', 'status'], 'maintenance'],
            '#3b82f6', // 维护状态：蓝色
            '#6b7280'  // 不在线状态：灰色
          ],
          'icon-opacity': [
            'case',
            ['==', ['get', 'id'], selectedStation || ''],
            1.0, // 选中状态：完全不透明
            ['==', ['get', 'id'], hoveredStation || ''],
            0.95, // 悬停状态：高透明度
            0.9   // 默认状态：中等透明度
          ]
        }}
      />
    </Source>
  );
});

StationLayer.displayName = 'StationLayer';
