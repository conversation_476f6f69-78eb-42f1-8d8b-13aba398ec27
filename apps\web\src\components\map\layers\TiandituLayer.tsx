import React, { useMemo } from 'react';
import { Source, Layer } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';

/**
 * 天地图底图图层组件
 */
export const TiandituLayer = React.memo(() => {
  const { baseMapType, showLabels } = useMapStore();

  // 获取天地图底图URL
  const getTiandituBaseUrl = useMemo(() => {
    const tiandituKey = '3686563411241d67b07f5c771e8fd645';
    switch (baseMapType) {
      case 'satellite':
        return `https://t0.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituKey}`;
      case 'terrain':
        return `https://t0.tianditu.gov.cn/ter_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=ter&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituKey}`;
      default: // 'vector'
        return `https://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituKey}`;
    }
  }, [baseMapType]);

  // 获取天地图标注URL
  const getTiandituLabelUrl = useMemo(() => {
    const tiandituKey = '3686563411241d67b07f5c771e8fd645';
    switch (baseMapType) {
      case 'satellite':
        return `https://t0.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituKey}`;
      case 'terrain':
        return `https://t0.tianditu.gov.cn/cta_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cta&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituKey}`;
      default: // 'vector'
        return `https://t0.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tiandituKey}`;
    }
  }, [baseMapType]);

  return (
    <>
      {/* 天地图底图 */}
      <Source 
        id="tianditu-base" 
        type="raster" 
        tiles={[getTiandituBaseUrl]}
        tileSize={256}
        maxzoom={18}
      >
        <Layer
          id="tianditu-base-layer"
          type="raster"
          beforeId="index_0"
        />
      </Source>

      {/* 天地图标注 - 根据showLabels控制显示 */}
      {showLabels && (
        <Source
          id="tianditu-label"
          type="raster"
          tiles={[getTiandituLabelUrl]}
          tileSize={256}
          maxzoom={18}
        >
          <Layer
            id="tianditu-label-layer"
            type="raster"
            beforeId="index_99"
          />
        </Source>
      )}
    </>
  );
});

TiandituLayer.displayName = 'TiandituLayer';
