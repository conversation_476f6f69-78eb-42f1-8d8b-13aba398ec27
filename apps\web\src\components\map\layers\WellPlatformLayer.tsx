import React, { memo, useMemo } from 'react';
import { Source, Layer } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import { usePlatformData } from '../../../hooks/useLayerData';

/**
 * 井平台图层组件
 */
export const WellPlatformLayer = memo(() => {
  const { layerVisibility } = useMapStore();
  const { selectedWell, hoveredWell } = useMapStore();

  const { platforms, loading, error } = usePlatformData();

  // 准备井平台数据为GeoJSON
  const wellPlatformGeoJSON = useMemo(() => {
    return {
      type: 'FeatureCollection' as const,
      features: platforms.map(platform => ({
        type: 'Feature' as const,
        properties: {
          id: String(platform.id),
          name: platform.name,
          platform_name: platform.name,
          platform_id: platform.id,
          region: platform.region,
          isPlatform: true
        },
        geometry: {
          type: 'Point' as const,
          coordinates: [platform.longitude, platform.latitude]
        }
      }))
    };
  }, [platforms]);

  if (!layerVisibility.wellPlatforms) {
    return null;
  }

  return (
    <>
      {/* 井平台数据源 */}
      <Source id="well-platforms-source" type="geojson" data={wellPlatformGeoJSON}>
        {/* 井平台图标图层 - 使用PNG图标 */}
        <Layer
          id="well-platforms"
          type="symbol"
          beforeId="index_92"
          layout={{
            'icon-image': 'well-platform-icon', // 使用PNG图标
            'icon-size': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              1.2, // 选中状态：大图标
              ['==', ['get', 'id'], hoveredWell || ''],
              1.0, // 悬停状态：中等图标
              0.8  // 默认状态：小图标
            ],
            'icon-allow-overlap': true,
            'icon-ignore-placement': true
          }}
          paint={{
            'icon-color': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              '#fbbf24', // 选中状态：黄色
              ['==', ['get', 'id'], hoveredWell || ''],
              '#06b6d4', // 悬停状态：青色
              '#10b981'  // 默认状态：绿色
            ],
            'icon-opacity': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              1.0, // 选中状态：完全不透明
              ['==', ['get', 'id'], hoveredWell || ''],
              0.9, // 悬停状态：高透明度
              0.8  // 默认状态：中等透明度
            ]
          }}
        />

        {/* 井平台标签图层 */}
        <Layer
          id="well-platform-labels"
          type="symbol"
          beforeId="index_91"
          layout={{
            'text-field': ['get', 'platform_name'],
            'text-size': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              14, // 选中状态：大字体
              ['==', ['get', 'id'], hoveredWell || ''],
              12, // 悬停状态：中等字体
              10  // 默认状态：小字体
            ],
            'text-offset': [0, 1.5], // 向下偏移，避免与点重叠
            'text-anchor': 'top',
            'text-allow-overlap': false,
            'text-ignore-placement': false
          }}
          paint={{
            'text-color': '#000000',
            'text-halo-color': '#ffffff',
            'text-halo-width': 1,
            'text-opacity': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              1.0, // 选中状态：完全不透明
              ['==', ['get', 'id'], hoveredWell || ''],
              0.9, // 悬停状态：高透明度
              0.7  // 默认状态：中等透明度
            ]
          }}
        />
      </Source>
    </>
  );
});

WellPlatformLayer.displayName = 'WellPlatformLayer';
