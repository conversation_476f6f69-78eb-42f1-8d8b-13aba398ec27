import { memo, useMemo } from 'react';
import { Source, Layer } from 'react-map-gl/maplibre';
import { useMapStore } from '../../../stores/useMapStore';
import { useWellData } from '../../../hooks/useLayerData';

/**
 * 井轨迹图层组件
 */
export const WellTrajectoryLayer = memo(() => {
  try {
    const { layerVisibility } = useMapStore();
    const { selectedWell, hoveredWell } = useMapStore();

    const { wells, loading, error } = useWellData();

  // 准备井轨迹数据为GeoJSON（只包含轨迹线）
  const wellTrajectoryGeoJSON = useMemo(() => {
    if (!wells || wells.length === 0) {
      console.log('🔵 WellTrajectoryLayer: 没有井轨迹数据');
      return {
        type: 'FeatureCollection' as const,
        features: []
      };
    }

    console.log('🔵 WellTrajectoryLayer: 处理', wells.length, '条井轨迹数据');

    return {
      type: 'FeatureCollection' as const,
      features: wells.map((well: any, index: number) => {
        try {
          if (!well.coordinates) {
            console.warn('井轨迹缺少坐标数据:', well);
            return null;
          }

          const coords = JSON.parse(well.coordinates);

          // 检查是否为MultiLineString格式
          const isMultiLineString = Array.isArray(coords) &&
            coords.length > 0 &&
            Array.isArray(coords[0]) &&
            coords[0].length > 0 &&
            Array.isArray(coords[0][0]) &&
            typeof coords[0][0][0] === 'number';

          if (isMultiLineString) {
            // MultiLineString格式：多条线段组成的井轨迹
            return {
              type: 'Feature' as const,
              properties: {
                id: String(well.id || index),
                name: well.name || `井轨迹${index + 1}`,
                platform_id: well.platform_id,
                region: well.region,
                isTrajectory: true
              },
              geometry: {
                type: 'MultiLineString' as const,
                coordinates: coords
              }
            };
          } else if (Array.isArray(coords) && coords.length > 1) {
            // 单条LineString格式
            return {
              type: 'Feature' as const,
              properties: {
                id: String(well.id || index),
                name: well.name || `井轨迹${index + 1}`,
                platform_id: well.platform_id,
                region: well.region,
                isTrajectory: true
              },
              geometry: {
                type: 'LineString' as const,
                coordinates: coords
              }
            };
          } else {
            // 跳过单点格式
            return null;
          }
        } catch (error) {
          console.error('解析井轨迹坐标失败:', error, well);
          return null;
        }
      }).filter((feature): feature is NonNullable<typeof feature> => feature !== null) // 过滤掉null值
    };
  }, [wells, loading, error]);

  if (!layerVisibility.wellTrajectories) {
    return null;
  }

  return (
    <>
      {/* 井轨迹数据源 */}
      <Source id="well-trajectories-source" type="geojson" data={wellTrajectoryGeoJSON}>
        {/* 井轨迹线图层 */}
        <Layer
          id="well-trajectories"
          type="line"
          beforeId="index_91"
          paint={{
            'line-color': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              '#fbbf24', // 选中状态：黄色
              ['==', ['get', 'id'], hoveredWell || ''],
              '#4b5563', // 悬停状态：深灰色
              '#374151'  // 默认状态：灰黑色
            ],
            'line-width': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              4, // 选中状态：粗线
              ['==', ['get', 'id'], hoveredWell || ''],
              3, // 悬停状态：中等线
              2  // 默认状态：细线
            ],
            'line-opacity': [
              'case',
              ['==', ['get', 'id'], selectedWell || ''],
              1.0, // 选中状态：完全不透明
              ['==', ['get', 'id'], hoveredWell || ''],
              0.9, // 悬停状态：高透明度
              0.8  // 默认状态：中等透明度
            ]
          }}
        />
      </Source>
    </>
  );
  } catch (error) {
    console.error('WellTrajectoryLayer 渲染错误:', error);
    return null;
  }
});

WellTrajectoryLayer.displayName = 'WellTrajectoryLayer';
