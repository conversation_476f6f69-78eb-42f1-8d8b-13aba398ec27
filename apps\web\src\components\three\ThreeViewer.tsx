import React, { useRef, useCallback } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import {
  EarthquakePoints,
  WellTrajectories,
  WellPlatforms,
  Faults,
  MonitoringStations,
  AdministrativeBoundaries,
  CoordinateAxes,
  GroundGrid,
  CanvasClickHandler,
  ThreeControlsManager
} from './components';
import { useMapStore } from '../../stores/useMapStore';
import { useIsMobileOnly } from '@/hooks/useIsMobile';

// 主要的三维场景组件
function ThreeScene({ controlsRef }: { controlsRef: React.MutableRefObject<any> }) {
  const orbitControlsRef = useRef<any>(null);
  const { setAdjustingView } = useMapStore();
  const stabilizeTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 清除稳定化定时器的函数
  const clearStabilizeTimer = useCallback(() => {
    if (stabilizeTimerRef.current) {
      clearTimeout(stabilizeTimerRef.current);
      stabilizeTimerRef.current = null;
    }
  }, []);

  // 启动稳定化检测的函数
  const startStabilizeTimer = useCallback(() => {
    clearStabilizeTimer();
    stabilizeTimerRef.current = setTimeout(() => {
      setAdjustingView(false);
      stabilizeTimerRef.current = null;
    }, 200); // 200ms延迟确保视角完全稳定
  }, [clearStabilizeTimer, setAdjustingView]);

  return (
    <>
      {/* 环境光 */}
      <ambientLight intensity={0.4} />

      {/* 方向光 */}
      <directionalLight position={[10, 10, 5]} intensity={0.8} />

      {/* 地面网格 */}
      <GroundGrid />

      {/* 坐标轴 */}
      <CoordinateAxes />

      {/* 地震点 */}
      <EarthquakePoints />

      {/* 井轨迹 */}
      <WellTrajectories />

      {/* 井平台 */}
      <WellPlatforms />

      {/* 断层 */}
      <Faults />

      {/* 监测台站 */}
      <MonitoringStations />

      {/* 行政区划 */}
      <AdministrativeBoundaries />

      {/* Canvas点击事件处理 */}
      <CanvasClickHandler />

      {/* 3D控制管理器 */}
      <ThreeControlsManager controlsRef={controlsRef} orbitControlsRef={orbitControlsRef} />

      {/* 轨道控制器 */}
      <OrbitControls
        ref={orbitControlsRef}
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI}
        minDistance={10}
        maxDistance={500}
        onStart={() => {
          // 开始调整视角时立即禁用hover事件并清除稳定化定时器
          console.log('🎯 开始调整3D视角，禁用hover事件');
          clearStabilizeTimer();
          setAdjustingView(true);
        }}
        onChange={() => {
          // 视角变化时重置稳定化定时器
          startStabilizeTimer();
        }}
        onEnd={() => {
          // 拖动结束时启动稳定化检测
          console.log('🎯 拖动结束，启动视角稳定化检测');
          startStabilizeTimer();
        }}
      />
    </>
  );
}

// 主要的ThreeViewer组件
export function ThreeViewer() {
  const isMobile = useIsMobileOnly();
  const controlsRef = useRef<any>(null);

  return (
    <div className="w-full h-full relative">
      {/* 磨砂玻璃背景效果 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-slate-100 backdrop-blur-sm" />

      {/* 3D控制按钮 - 调整位置为 top-24 right-6 */}
      {!isMobile && <div className="absolute top-24 right-6 z-20 flex flex-col space-y-2 fade-in">
        <button
          onClick={() => {
            console.log('🔘 点击初始视角按钮', controlsRef.current);
            controlsRef.current?.resetToInitial?.();
          }}
          className="px-3 py-2 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg border border-slate-200 hover:bg-opacity-100 transition-all duration-200 text-sm font-medium text-slate-700 hover:text-slate-900"
          title="恢复初始视角"
        >
          <div className="flex flex-col items-center space-y-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 21l4-4 4 4"/>
            </svg>
            <span className="text-xs">初始</span>
          </div>
        </button>

        <button
          onClick={() => {
            controlsRef.current?.switchToTopView?.();
          }}
          className="px-3 py-2 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg border border-slate-200 hover:bg-opacity-100 transition-all duration-200 text-sm font-medium text-slate-700 hover:text-slate-900"
          title="垂直平视视角"
        >
          <div className="flex flex-col items-center space-y-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 3v18m0-18l4 4m-4-4L8 7"/>
            </svg>
            <span className="text-xs">俯视</span>
          </div>
        </button>

        <button
          onClick={() => controlsRef.current?.switchToFrontView?.()}
          className="px-3 py-2 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg border border-slate-200 hover:bg-opacity-100 transition-all duration-200 text-sm font-medium text-slate-700 hover:text-slate-900"
          title="前视视角"
        >
          <div className="flex flex-col items-center space-y-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span className="text-xs">正视</span>
          </div>
        </button>

        <button
          onClick={() => controlsRef.current?.switchToSideView?.()}
          className="px-3 py-2 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg shadow-lg border border-slate-200 hover:bg-opacity-100 transition-all duration-200 text-sm font-medium text-slate-700 hover:text-slate-900"
          title="侧视视角"
        >
          <div className="flex flex-col items-center space-y-1">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"/>
            </svg>
            <span className="text-xs">侧视</span>
          </div>
        </button>
      </div>}

      <Canvas
        camera={{
          position: [0, 30, 30], // 俯视向上倾斜的初始视角
          fov: 60,
          near: 0.1,
          far: 1000
        }}
        style={{ background: 'transparent' }}
        className="relative z-0"
      >
        <ThreeScene controlsRef={controlsRef} />
      </Canvas>
    </div>
  );
}
