import { useMemo, useState, useEffect, useRef } from 'react';
import { Line, Html, Circle } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import * as THREE from 'three';
import { useMapStore } from '../../../stores/useMapStore';



// 脉冲圆圈组件
function PulsingCircle({ position, name }: { position: [number, number, number], name: string }) {
  const groupRef = useRef<THREE.Group>(null);
  const pulseRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (pulseRef.current) {
      const time = state.clock.elapsedTime;
      const pulse = Math.sin(time * 2) * 0.2 + 1;
      pulseRef.current.scale.setScalar(pulse);
      const material = pulseRef.current.material as THREE.MeshBasicMaterial;
      if (material && 'opacity' in material) {
        material.opacity = (2 - pulse) * 0.3;
      }
    }
  });
  
  return (
    <group ref={groupRef} position={position}>
      {/* 贴地圆圈 - 主圆圈 缩小 */}
      <Circle args={[0.8]} rotation={[-Math.PI / 2, 0, 0]} position={[0, 0, 0]}>
        <meshBasicMaterial 
          color="#10B981" 
          transparent 
          opacity={0.6}
          side={THREE.DoubleSide}
        />
      </Circle>
      
      {/* 脉冲圆圈 缩小 */}
      <Circle ref={pulseRef} args={[1.0]} rotation={[-Math.PI / 2, 0, 0]} position={[0, 0.005, 0]}>
        <meshBasicMaterial 
          color="#34D399" 
          transparent 
          opacity={0.3}
          side={THREE.DoubleSide}
        />
      </Circle>
      
      {/* 内部发光圆圈 缩小 */}
      <Circle args={[0.5]} rotation={[-Math.PI / 2, 0, 0]} position={[0, 0.01, 0]}>
        <meshBasicMaterial 
          color="#34D399" 
          transparent 
          opacity={0.8}
          side={THREE.DoubleSide}
        />
      </Circle>
      
      {/* 中心点 缩小 */}
      <Circle args={[0.2]} rotation={[-Math.PI / 2, 0, 0]} position={[0, 0.02, 0]}>
        <meshBasicMaterial 
          color="#FFFFFF" 
          transparent 
          opacity={1}
          side={THREE.DoubleSide}
        />
      </Circle>
      
      {/* 中文文字标签 - 位置降低，样式优化 */}
      <Html
        position={[0, 0.8, 0]}
        center
        style={{
          color: '#065F46',
          fontSize: '11px',
          fontWeight: 'bold',
          textShadow: '2px 2px 4px rgba(255,255,255,0.9), -1px -1px 2px rgba(255,255,255,0.9)',
          userSelect: 'none',
          pointerEvents: 'none',
          whiteSpace: 'nowrap',
          backgroundColor: 'rgba(255,255,255,0.9)',
          padding: '2px 6px',
          borderRadius: '4px',
          border: '1px solid rgba(16,185,129,0.4)',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        {name}
      </Html>
    </group>
  );
}

// 行政区划三维组件
export function AdministrativeBoundaries() {
  const {
    layerVisibility,
    administrativeSubLayers,
    setAdministrativeLoading,
  } = useMapStore();
  
  const [countyData, setCountyData] = useState<any>(null);
  const [townshipLineData, setTownshipLineData] = useState<any>(null);
  const [townshipPointData, setTownshipPointData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载 GeoJSON 数据
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setAdministrativeLoading(true);
        setError(null);

        // 并行加载三个数据文件
        const [countyResponse, townshipLineResponse, townshipPointResponse] = await Promise.all([
          fetch('/converted_geojson/line/县级边界.geojson'),
          fetch('/converted_geojson/line/乡镇边界.geojson'),
          fetch('/converted_geojson/point/乡镇点.geojson'),
        ]);

        if (!countyResponse.ok || !townshipLineResponse.ok || !townshipPointResponse.ok) {
          throw new Error('Failed to load administrative data');
        }

        const [county, townshipLine, townshipPoint] = await Promise.all([
          countyResponse.json(),
          townshipLineResponse.json(),
          townshipPointResponse.json(),
        ]);

        setCountyData(county);
        setTownshipLineData(townshipLine);
        setTownshipPointData(townshipPoint);
      } catch (err) {
        console.error('Error loading administrative data:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
        setAdministrativeLoading(false);
      }
    };

    loadData();
  }, [setAdministrativeLoading]);

  // 将 GeoJSON 坐标转换为 Three.js 坐标 - 与地震数据使用相同的投影
  const projectCoordinates = (coordinates: [number, number]) => {
    // 使用与地震数据相同的坐标中心和缩放比例
    const x = (coordinates[0] - 105.45) * 100;
    const z = -(coordinates[1] - 29.17) * 100; // 反转Z轴，使俯视时上北下南
    const y = 0.5; // 行政区划在地面稍上方
    
    return [x, y, z] as [number, number, number];
  };

  // 生成县级边界静态线条
  const countyLines = useMemo(() => {
    if (!countyData || !administrativeSubLayers.countyBoundaries) return [];
    
    return countyData.features.map((feature: any, index: number) => {
      if (feature.geometry.type === 'LineString') {
        const points = feature.geometry.coordinates.map((coord: [number, number]) => 
          new THREE.Vector3(...projectCoordinates(coord))
        );
        return (
          <Line
            key={`county-${index}`}
            points={points}
            color="#7C3AED" // 紫色
            lineWidth={3}
            transparent
            opacity={0.9}
          />
        );
      } else if (feature.geometry.type === 'MultiLineString') {
        return feature.geometry.coordinates.map((lineCoords: [number, number][], lineIndex: number) => {
          const points = lineCoords.map((coord: [number, number]) => 
            new THREE.Vector3(...projectCoordinates(coord))
          );
          return (
            <Line
              key={`county-${index}-${lineIndex}`}
              points={points}
              color="#7C3AED" // 紫色
              lineWidth={3}
              transparent
              opacity={0.9}
            />
          );
        });
      }
      return null;
    }).flat().filter(Boolean);
  }, [countyData, administrativeSubLayers.countyBoundaries]);

  // 生成乡镇边界线条
  const townshipLines = useMemo(() => {
    if (!townshipLineData || !administrativeSubLayers.townshipBoundaries) return [];
    
    return townshipLineData.features.map((feature: any, index: number) => {
      if (feature.geometry.type === 'LineString') {
        const points = feature.geometry.coordinates.map((coord: [number, number]) => 
          new THREE.Vector3(...projectCoordinates(coord))
        );
        return (
          <Line
            key={`township-line-${index}`}
            points={points}
            color="#9CA3AF" // 灰色
            lineWidth={2}
            transparent
            opacity={0.8}
          />
        );
      } else if (feature.geometry.type === 'MultiLineString') {
        return feature.geometry.coordinates.map((lineCoords: [number, number][], lineIndex: number) => {
          const points = lineCoords.map((coord: [number, number]) => 
            new THREE.Vector3(...projectCoordinates(coord))
          );
          return (
            <Line
              key={`township-line-${index}-${lineIndex}`}
              points={points}
              color="#9CA3AF" // 灰色
              lineWidth={2}
              transparent
              opacity={0.8}
            />
          );
        });
      }
      return null;
    }).flat().filter(Boolean);
  }, [townshipLineData, administrativeSubLayers.townshipBoundaries]);

  // 生成乡镇点位 - 使用脉冲圆圈组件
  const townshipPoints = useMemo(() => {
    if (!townshipPointData || !administrativeSubLayers.townshipPoints) return [];
    
    return townshipPointData.features.map((feature: any, index: number) => {
      if (feature.geometry.type === 'Point') {
        const [x, , z] = projectCoordinates(feature.geometry.coordinates);
        const name = feature.properties?.name || `乡镇-${index}`;
        
        return (
          <PulsingCircle
            key={`township-point-${index}`}
            position={[x, 0.1, z]}
            name={name}
          />
        );
      }
      return null;
    }).filter(Boolean);
  }, [townshipPointData, administrativeSubLayers.townshipPoints]);

  if (!layerVisibility.administrative || loading || error) {
    return null;
  }

  return (
    <group>
      {/* 县级边界 */}
      {countyLines}
      
      {/* 乡镇边界 */}
      {townshipLines}
      
      {/* 乡镇点位 */}
      {townshipPoints}
    </group>
  );
}