import React, { useMemo, useState } from 'react';
import { useThree } from '@react-three/fiber';
import { useEarthquakeData } from '../../../contexts/EarthquakeDataContext';
import { useMapStore } from '../../../stores/useMapStore';
import { turboColors } from '../../map/layers/EarthquakeMapLibreLayer';
import * as turf from '@turf/turf';

// 地震点组件 - 由于instancedMesh的限制，改为单独的mesh以支持交互
export function EarthquakePoints() {
  const {
    layerVisibility,
    filters,
    setSelectedEarthquake,
    setHoveredEarthquake,
    setPanelPinned,
    selectedEarthquake
  } = useMapStore();
  const earthquakeData = useEarthquakeData();
  const { gl } = useThree();
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  // 过滤地震数据 - 使用与地图相同的过滤逻辑
  const filteredData = useMemo(() => {
    const filtered = earthquakeData.layerData.filter(d => {
      // 时间过滤
      if (filters.timeRange) {
        const eventTime = new Date(d.time);
        if (eventTime < filters.timeRange.start || eventTime > filters.timeRange.end) {
          return false;
        }
      }

      // 震级过滤
      if (filters.magnitudeRange) {
        if (d.magnitude < filters.magnitudeRange[0] || d.magnitude > filters.magnitudeRange[1]) {
          return false;
        }
      }

      // 深度过滤
      if (filters.depthRange) {
        if (d.depth < filters.depthRange[0] || d.depth > filters.depthRange[1]) {
          return false;
        }
      }

      // 空间筛选
      if (filters.spatialFilter && filters.spatialFilter.center && filters.spatialFilter.radius) {
        const distance = turf.distance(
          d.coordinates,
          filters.spatialFilter.center,
          { units: 'kilometers' }
        );
        if (distance > filters.spatialFilter.radius) {
          return false;
        }
      }

      return true;
    });

    // 按时间排序，与地图保持一致
    return filtered.sort((a, b) => {
      const timeA = new Date(a.time).getTime();
      const timeB = new Date(b.time).getTime();
      return timeA - timeB;
    });
  }, [earthquakeData.layerData, filters.timeRange, filters.magnitudeRange, filters.depthRange]);

  // 计算时间范围用于颜色映射 - 使用整体数据时间范围而不是过滤后的数据范围
  const timeRange = useMemo(() => {
    // 优先使用数据上下文中的整体时间范围
    if (earthquakeData.dataTimeRange) {
      const range = {
        min: earthquakeData.dataTimeRange.start.getTime(),
        max: earthquakeData.dataTimeRange.end.getTime()
      };
      console.log('🎨 ThreeViewer 使用数据上下文时间范围:', {
        start: earthquakeData.dataTimeRange.start.toISOString(),
        end: earthquakeData.dataTimeRange.end.toISOString(),
        range
      });
      return range;
    }

    // 如果没有整体时间范围，则使用所有数据（不是过滤后的数据）计算
    if (earthquakeData.layerData.length === 0) {
      console.log('🎨 ThreeViewer 无地震数据，使用当前时间');
      return { min: Date.now(), max: Date.now() };
    }

    const times = earthquakeData.layerData.map((eq: any) => new Date(eq.time).getTime());
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    const range = { min: minTime, max: maxTime };
    console.log('🎨 ThreeViewer 使用layerData计算时间范围:', {
      count: earthquakeData.layerData.length,
      range,
      minDate: new Date(minTime).toISOString(),
      maxDate: new Date(maxTime).toISOString()
    });

    return range;
  }, [earthquakeData.dataTimeRange, earthquakeData.layerData]);

  // 处理地震点交互事件
  const handleClick = (eq: any) => {
    setSelectedEarthquake(eq.eventId);
    setPanelPinned(true);
  };

  const handlePointerOver = (eq: any) => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredEarthquake(eq.eventId);
    setHoveredId(eq.eventId);
    gl.domElement.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredEarthquake(null);
    setHoveredId(null);
    gl.domElement.style.cursor = 'default';
  };

  // 计算地震点的颜色和大小
  const getEarthquakeColor = (eq: any) => {
    const eventTime = new Date(eq.time).getTime();
    let normalizedTime = 0;

    if (timeRange.max > timeRange.min) {
      normalizedTime = (eventTime - timeRange.min) / (timeRange.max - timeRange.min);
      normalizedTime = Math.max(0, Math.min(1, normalizedTime));
    } else {
      normalizedTime = 0.5;
    }

    const colorIndex = Math.floor(normalizedTime * (turboColors.length - 1));
    return turboColors[colorIndex] || turboColors[Math.floor(turboColors.length / 2)];
  };

  const getEarthquakeScale = (magnitude: number) => {
    const m = Math.max(magnitude || 0, -1);
    if (m < -0.5) return 0.15;
    if (m < 0) return 0.2;
    if (m < 0.5) return 0.25;
    if (m < 1) return 0.3;
    if (m < 1.5) return 0.35;
    if (m < 2) return 0.4;
    if (m < 2.5) return 0.45;
    if (m < 3) return 0.5;
    if (m < 3.5) return 0.55;
    if (m < 4) return 0.6;
    return 0.65;
  };

  if (!layerVisibility.earthquakes || filteredData.length === 0) return null;

  return (
    <group>
      {filteredData.map((eq: any, index: number) => {
        const x = (eq.coordinates[0] - 105.45) * 100;
        const z = -(eq.coordinates[1] - 29.17) * 100; // 反转Z轴，使俯视时上北下南
        const y = -eq.depth * 1.0;
        const isHovered = hoveredId === eq.eventId;
        const isSelected = selectedEarthquake === eq.eventId;
        const scale = getEarthquakeScale(eq.magnitude);
        const color = getEarthquakeColor(eq);

        // 选中状态：放大1.5倍，hover状态：放大1.3倍
        const scaleMultiplier = isSelected ? 1.5 : (isHovered ? 1.3 : 1);

        return (
          <mesh
            key={eq.eventId || index}
            position={[x, y, z]}
            scale={[scale * scaleMultiplier, scale * scaleMultiplier, scale * scaleMultiplier]}
            onClick={() => handleClick(eq)}
            onPointerOver={() => handlePointerOver(eq)}
            onPointerOut={handlePointerOut}
          >
            <sphereGeometry args={[0.5, 8, 6]} />
            <meshPhongMaterial
              color={color}
              emissive={isSelected ? color : '#000000'}
              emissiveIntensity={isSelected ? 0.3 : 0}
            />
          </mesh>
        );
      })}
    </group>
  );
}
