import React, { useState } from 'react';
import { useThree } from '@react-three/fiber';
import { Line, Cone } from '@react-three/drei';
import * as THREE from 'three';
import { useAllLayerData, useStationData } from '../../../hooks/useLayerData';
import { useMapStore } from '../../../stores/useMapStore';

// 断层组件
export function Faults() {
  const {
    layerVisibility,
    setSelectedFault,
    setHoveredFault,
    setPanelPinned,
    selectedFault
  } = useMapStore();
  const layerData = useAllLayerData();
  const faults = layerData.faults.layerData;
  const { gl } = useThree();
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  if (!layerVisibility.faults) return null;

  const handleClick = (fault: any) => {
    setSelectedFault(String(fault.id));
    setPanelPinned(true);
  };

  const handlePointerOver = (fault: any) => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredFault(String(fault.id));
    setHoveredId(String(fault.id));
    gl.domElement.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredFault(null);
    setHoveredId(null);
    gl.domElement.style.cursor = 'default';
  };

  return (
    <group>
      {faults.map((fault: any, index: number) => {
        if (!fault.coordinates || fault.coordinates.length < 2) return null;

        const points = fault.coordinates.map(([lng, lat]: [number, number]) => {
          const x = (lng - 105.45) * 100;
          const z = -(lat - 29.17) * 100; // 反转Z轴，使俯视时上北下南
          const y = 0; // 断层在地表
          return new THREE.Vector3(x, y, z);
        });

        const isHovered = hoveredId === String(fault.id);
        const isSelected = selectedFault === String(fault.id);

        // 根据断层等级设置颜色
        const getColors = (level: number, hovered: boolean, selected: boolean) => {
          const baseColors = {
            1: '#ef4444', // 一级断层：红色
            2: '#f97316', // 二级断层：橙色
            3: '#eab308'  // 三级断层：黄色
          };
          const hoverColors = {
            1: '#f87171', // hover时更亮的红色
            2: '#fb923c', // hover时更亮的橙色
            3: '#facc15'  // hover时更亮的黄色
          };
          const selectedColors = {
            1: '#dc2626', // 选中时更深的红色
            2: '#ea580c', // 选中时更深的橙色
            3: '#ca8a04'  // 选中时更深的黄色
          };

          let colorMap = baseColors;
          if (selected) colorMap = selectedColors;
          else if (hovered) colorMap = hoverColors;

          return colorMap[level as keyof typeof colorMap] || '#6b7280';
        };

        const color = getColors(fault.level, isHovered, isSelected);

        // 选中状态：线条更粗，hover状态：线条变粗
        const lineWidth = isSelected ? 5 : (isHovered ? 4 : 3);

        return (
          <Line
            key={fault.id || index}
            points={points}
            color={color}
            lineWidth={lineWidth}
            onClick={() => handleClick(fault)}
            onPointerOver={() => handlePointerOver(fault)}
            onPointerOut={handlePointerOut}
          />
        );
      })}
    </group>
  );
}

// 监测台站组件
export function MonitoringStations() {
  const {
    layerVisibility,
    setSelectedStation,
    setHoveredStation,
    setPanelPinned,
    selectedStation
  } = useMapStore();
  const { stations } = useStationData();
  const { gl } = useThree();
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  if (!layerVisibility.stations) return null;

  const handleClick = (station: any) => {
    setSelectedStation(String(station.id));
    setPanelPinned(true);
  };

  const handlePointerOver = (station: any) => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredStation(String(station.id));
    setHoveredId(String(station.id));
    gl.domElement.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredStation(null);
    setHoveredId(null);
    gl.domElement.style.cursor = 'default';
  };

  return (
    <group>
      {stations.map((station: any, index: number) => {
        const x = (station.longitude - 105.45) * 100;
        const z = -(station.latitude - 29.17) * 100; // 反转Z轴，使俯视时上北下南
        const y = 0; // 台站在地表
        const isHovered = hoveredId === String(station.id);
        const isSelected = selectedStation === String(station.id);

        // 根据状态设置颜色
        const getStationColor = (status: string, hovered: boolean = false, selected: boolean = false) => {
          const baseColors = {
            active: '#f97316',    // 橙色
            maintenance: '#3b82f6', // 蓝色
            inactive: '#6b7280',   // 灰色
          };
          const hoverColors = {
            active: '#fb923c',    // hover时更亮的橙色
            maintenance: '#60a5fa', // hover时更亮的蓝色
            inactive: '#9ca3af',   // hover时更亮的灰色
          };
          const selectedColors = {
            active: '#ea580c',    // 选中时更深的橙色
            maintenance: '#2563eb', // 选中时更深的蓝色
            inactive: '#4b5563',   // 选中时更深的灰色
          };

          let colorMap = baseColors;
          if (selected) colorMap = selectedColors;
          else if (hovered) colorMap = hoverColors;

          return colorMap[status as keyof typeof colorMap] || colorMap.inactive;
        };

        // 根据状态设置缩放
        const getScale = (hovered: boolean, selected: boolean) => {
          if (selected) return 1.4;  // 选中时最大
          if (hovered) return 1.2;   // hover时放大
          return 1;                  // 默认大小
        };

        const color = getStationColor(station.status, isHovered, isSelected);
        const scale = getScale(isHovered, isSelected);

        return (
          <group key={station.id || index} position={[x, y, z]}>
            {/* 使用倒置圆锥作为三角形台站，缩小一半 */}
            <Cone
              args={[0.3, 0.5, 3]} // 从[0.6, 1.0, 3]缩小一半
              position={[0, 0.25, 0]} // 调整Y位置因为高度变小了
              rotation={[Math.PI, 0, 0]} // 倒置
              onClick={() => handleClick(station)}
              onPointerOver={() => handlePointerOver(station)}
              onPointerOut={handlePointerOut}
              scale={scale}
            >
              <meshPhongMaterial
                color={color}
                emissive={isSelected ? color : '#000000'}
                emissiveIntensity={isSelected ? 0.2 : 0}
              />
            </Cone>
          </group>
        );
      })}
    </group>
  );
}
