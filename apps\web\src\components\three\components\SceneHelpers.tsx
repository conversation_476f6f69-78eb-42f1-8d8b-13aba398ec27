import React from 'react';
import { Text, Line } from '@react-three/drei';
import * as THREE from 'three';

// 坐标轴组件
export function CoordinateAxes() {
  return (
    <group>
      {/* X轴 - 红色 (东西方向) */}
      <Line points={[new THREE.Vector3(0, 0, 0), new THREE.Vector3(50, 0, 0)]} color="red" lineWidth={3} />
      <Text position={[55, 0, 0]} fontSize={3} color="red" fontWeight="bold">东 E</Text>

      {/* 负X轴 - 西方向 */}
      <Line points={[new THREE.Vector3(0, 0, 0), new THREE.Vector3(-50, 0, 0)]} color="red" lineWidth={2} />
      <Text position={[-55, 0, 0]} fontSize={3} color="red" fontWeight="bold">西 W</Text>

      {/* Y轴 - 绿色 (垂直方向) */}
      <Line points={[new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, 50, 0)]} color="green" lineWidth={3} />
      <Text position={[0, 55, 0]} fontSize={3} color="green" fontWeight="bold">上 U</Text>

      {/* 负Y轴 - 下方向 */}
      <Line points={[new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, -30, 0)]} color="green" lineWidth={2} />
      <Text position={[0, -35, 0]} fontSize={3} color="green" fontWeight="bold">下 D</Text>

      {/* Z轴 - 蓝色 (南北方向，Z轴正方向指向南) */}
      <Line points={[new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, 0, 50)]} color="blue" lineWidth={2} />
      <Text position={[0, 0, 55]} fontSize={3} color="blue" fontWeight="bold">南 S</Text>

      {/* 负Z轴指向北 */}
      <Line points={[new THREE.Vector3(0, 0, 0), new THREE.Vector3(0, 0, -50)]} color="blue" lineWidth={3} />
      <Text position={[0, 0, -55]} fontSize={3} color="blue" fontWeight="bold">北 N</Text>
    </group>
  );
}

// 地面网格组件
export function GroundGrid() {
  return (
    <gridHelper args={[200, 20, '#666666', '#999999']} position={[0, 0, 0]} />
  );
}
