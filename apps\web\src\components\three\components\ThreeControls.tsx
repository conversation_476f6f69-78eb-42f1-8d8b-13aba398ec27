import React from 'react';
import { useThree } from '@react-three/fiber';
import { useMapStore } from '../../../stores/useMapStore';

// Canvas点击事件处理组件
export function CanvasClickHandler() {
  const {
    setSelectedEarthquake,
    setSelectedFault,
    setSelectedWell,
    setSelectedStation,
    setPanelPinned,
  } = useMapStore();

  // 处理Canvas点击事件 - 点击空白区域取消选中
  const handleCanvasClick = (event: any) => {
    // 检查是否点击了3D对象
    // 如果event.intersections存在且长度大于0，说明点击了3D对象
    if (event.intersections && event.intersections.length > 0) {
      // 点击了3D对象，不处理（由各个组件的onClick处理）
      return;
    }

    // 点击了空白区域，清除所有选中状态
    setSelectedEarthquake(null);
    setSelectedFault(null);
    setSelectedWell(null);
    setSelectedStation(null);
    setPanelPinned(false);
  };

  return (
    <mesh
      position={[0, -50, 0]} // 放在地面下方，作为背景点击区域
      onClick={handleCanvasClick}
      visible={false} // 不可见，只用于捕获点击事件
    >
      <planeGeometry args={[1000, 1000]} />
      <meshBasicMaterial transparent opacity={0} />
    </mesh>
  );
}

// 3D控制功能组件
export function ThreeControlsManager({
  controlsRef,
  orbitControlsRef
}: {
  controlsRef: React.MutableRefObject<any>;
  orbitControlsRef?: React.MutableRefObject<any>;
}) {
  const { camera } = useThree();

  // 将控制函数暴露给外部
  React.useEffect(() => {
    if (camera) {
      controlsRef.current = {
        // 恢复初始视角 - 俯视向上倾斜的角度，保持上北下南
        // 从较高位置向下看，但保持一定倾斜角度以获得立体感
        resetToInitial: () => {
          camera.position.set(0, 30, 30);
          if (orbitControlsRef?.current) {
            orbitControlsRef.current.target.set(0, 0, 0);
            orbitControlsRef.current.update();
          } else {
            camera.lookAt(0, 0, 0);
            camera.updateProjectionMatrix();
          }
        },
        // 切换到垂直平视视角（俯视）- 从上往下看，北方朝上
        switchToTopView: () => {
          camera.position.set(0, 40, 0);
          if (orbitControlsRef?.current) {
            orbitControlsRef.current.target.set(0, 0, 0);
            orbitControlsRef.current.update();
          } else {
            camera.lookAt(0, 0, 0);
            camera.updateProjectionMatrix();
          }
        },
        // 切换到侧视角 - 从东边看向西边，北方朝右
        switchToSideView: () => {
          camera.position.set(40, 0, 0);
          if (orbitControlsRef?.current) {
            orbitControlsRef.current.target.set(0, 0, 0);
            orbitControlsRef.current.update();
          } else {
            camera.lookAt(0, 0, 0);
            camera.updateProjectionMatrix();
          }
        },
        // 切换到前视角 - 从北边看向南边，南方朝前
        switchToFrontView: () => {
          camera.position.set(0, 0, 40);
          if (orbitControlsRef?.current) {
            orbitControlsRef.current.target.set(0, 0, 0);
            orbitControlsRef.current.update();
          } else {
            camera.lookAt(0, 0, 0);
            camera.updateProjectionMatrix();
          }
        }
      };
    }
  }, [camera, controlsRef, orbitControlsRef]);

  return null;
}
