import React, { useState } from 'react';
import { useThree } from '@react-three/fiber';
import { Line, Cone } from '@react-three/drei';
import * as THREE from 'three';
import { useWellData, usePlatformData } from '../../../hooks/useLayerData';
import { useMapStore } from '../../../stores/useMapStore';

// 井轨迹组件
export function WellTrajectories() {
  const {
    layerVisibility,
    setSelectedWell,
    setHoveredWell,
    setPanelPinned,
    selectedWell
  } = useMapStore();
  const { wells } = useWellData(); // 直接使用原始井轨迹数据
  const { gl } = useThree();
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  // 添加调试信息
  console.log('🔵 WellTrajectories 组件:', {
    wellsCount: wells?.length || 0,
    layerVisible: layerVisibility.wellTrajectories,
    wells: wells?.slice(0, 2) // 只显示前2条数据用于调试
  });

  if (!layerVisibility.wellTrajectories) return null;

  const handleClick = (well: any) => {
    setSelectedWell(String(well.id));
    setPanelPinned(true);
  };

  const handlePointerOver = (well: any) => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredWell(String(well.id));
    setHoveredId(String(well.id));
    gl.domElement.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredWell(null);
    setHoveredId(null);
    gl.domElement.style.cursor = 'default';
  };

  return (
    <group>
      {wells.map((well: any) => {
        try {
          if (!well.coordinates) return null;

          let coords;
          try {
            // 尝试解析JSON
            coords = JSON.parse(well.coordinates);
          } catch (parseError) {
            // 如果JSON解析失败，跳过这个井轨迹
            return null;
          }

          const isHovered = hoveredId === String(well.id);
          const isSelected = selectedWell === String(well.id);

          // 根据状态设置颜色和线宽
          const getColor = (hovered: boolean, selected: boolean) => {
            if (selected) return "#1f2937"; // 选中时更深的颜色
            if (hovered) return "#4b5563";  // hover时变亮
            return "#374151";               // 默认颜色
          };

          const getLineWidth = (hovered: boolean, selected: boolean) => {
            if (selected) return 4;  // 选中时最粗
            if (hovered) return 3;   // hover时变粗
            return 2;                // 默认宽度
          };

          const color = getColor(isHovered, isSelected);
          const lineWidth = getLineWidth(isHovered, isSelected);

          // 检查是否为MultiLineString格式
          const isMultiLineString = Array.isArray(coords) &&
            coords.length > 0 &&
            Array.isArray(coords[0]) &&
            coords[0].length > 0 &&
            Array.isArray(coords[0][0]) &&
            typeof coords[0][0][0] === 'number';

          if (isMultiLineString) {
            // MultiLineString格式：多条线段组成的井轨迹
            return coords.map((lineCoords: number[][], lineIndex: number) => {
              if (!Array.isArray(lineCoords) || lineCoords.length < 2) return null;

              const points = lineCoords.map(([lng, lat, depth = 0]) => {
                const x = (lng - 105.45) * 100;
                const z = -(lat - 29.17) * 100; // 反转Z轴，使俯视时上北下南
                const y = -depth * 1.0; // 深度转换为Y轴
                return new THREE.Vector3(x, y, z);
              });

              return (
                <Line
                  key={`${well.id}-${lineIndex}`}
                  points={points}
                  color={color}
                  lineWidth={lineWidth}
                  onClick={() => handleClick(well)}
                  onPointerOver={() => handlePointerOver(well)}
                  onPointerOut={handlePointerOut}
                />
              );
            });
          } else if (Array.isArray(coords) && coords.length > 1) {
            // 单条LineString格式
            const points = coords.map(([lng, lat, depth = 0]) => {
              const x = (lng - 105.45) * 100;
              const z = -(lat - 29.17) * 100; // 反转Z轴，使俯视时上北下南
              const y = -depth * 1.0;
              return new THREE.Vector3(x, y, z);
            });

            return (
              <Line
                key={well.id}
                points={points}
                color={color}
                lineWidth={lineWidth}
                onClick={() => handleClick(well)}
                onPointerOver={() => handlePointerOver(well)}
                onPointerOut={handlePointerOut}
              />
            );
          }
        } catch (error) {
          // 静默处理错误，避免控制台spam
          return null;
        }
        return null;
      })}
    </group>
  );
}

// 井平台组件
export function WellPlatforms() {
  const {
    layerVisibility,
    setSelectedWell,
    setHoveredWell,
    setPanelPinned,
    selectedWell
  } = useMapStore();
  const { platforms } = usePlatformData();
  const { gl } = useThree();
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  if (!layerVisibility.wellPlatforms) return null;

  const handleClick = (platform: any) => {
    setSelectedWell(String(platform.id));
    setPanelPinned(true);
  };

  const handlePointerOver = (platform: any) => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredWell(String(platform.id));
    setHoveredId(String(platform.id));
    gl.domElement.style.cursor = 'pointer';
  };

  const handlePointerOut = () => {
    // 如果正在调整视角，禁用hover事件
    if (useMapStore.getState().isAdjustingView) {
      return;
    }
    setHoveredWell(null);
    setHoveredId(null);
    gl.domElement.style.cursor = 'default';
  };

  return (
    <group>
      {platforms.map((platform: any, index: number) => {
        const x = (platform.longitude - 105.45) * 100;
        const z = -(platform.latitude - 29.17) * 100; // 反转Z轴，使俯视时上北下南
        const y = 0; // 井平台在地表
        const isHovered = hoveredId === String(platform.id);
        const isSelected = selectedWell === String(platform.id);

        // 根据状态设置颜色和缩放
        const getColor = (hovered: boolean, selected: boolean) => {
          if (selected) return "#059669"; // 选中时更深的绿色
          if (hovered) return "#14b8a6";  // hover时的青绿色
          return "#10b981";               // 默认绿色
        };

        const getScale = (hovered: boolean, selected: boolean) => {
          if (selected) return 1.4;  // 选中时最大
          if (hovered) return 1.2;   // hover时放大
          return 1;                  // 默认大小
        };

        const color = getColor(isHovered, isSelected);
        const scale = getScale(isHovered, isSelected);

        return (
          <group key={platform.id || index} position={[x, y, z]}>
            {/* 使用小方块作为井平台，缩小一半 */}
            <mesh
              position={[0, 0.15, 0]} // 调整Y位置因为高度变小了
              onClick={() => handleClick(platform)}
              onPointerOver={() => handlePointerOver(platform)}
              onPointerOut={handlePointerOut}
              scale={scale}
            >
              <boxGeometry args={[0.3, 1.0, 0.3]} />
              <meshPhongMaterial
                color={color}
                emissive={isSelected ? color : '#000000'}
                emissiveIntensity={isSelected ? 0.2 : 0}
              />
            </mesh>
          </group>
        );
      })}
    </group>
  );
}
