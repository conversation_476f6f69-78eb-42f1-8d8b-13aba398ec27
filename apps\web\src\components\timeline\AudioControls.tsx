import React from 'react';
import { Settings } from 'lucide-react';

interface AudioControlsProps {
  earthquakes: Array<{ id?: string | number; event_id?: string; occurred_at: string; magnitude: number }>;
  maskStartTime: Date | null;
  maskEndTime: Date | null;
  isAudioEnabled: boolean;
  onAudioToggle: () => void;
  volume: number;
  onVolumeChange: (volume: number) => void;
  onConfigClick?: () => void;
  className?: string;
  isMobile?: boolean;
}

export function AudioControls({
  earthquakes,
  maskStartTime,
  maskEndTime,
  isAudioEnabled,
  onAudioToggle,
  volume,
  onVolumeChange,
  onConfigClick,
  className = '',
  isMobile = false
}: AudioControlsProps) {

  // 处理音量变化
  const handleVolumeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(event.target.value);
    onVolumeChange(newVolume);
  };

  // 计算当前时间范围内的地震数量
  const earthquakeCount = earthquakes.filter(eq => {
    if (!maskStartTime || !maskEndTime) return false;
    const eqTime = new Date(eq.occurred_at);
    return eqTime >= maskStartTime && eqTime <= maskEndTime;
  }).length;

  return (
    <div className={`flex items-center ${isMobile ? 'space-x-1' : 'space-x-2'} ${className}`}>
      {/* 音频开关按钮 */}
      <button
        onClick={onAudioToggle}
        disabled={earthquakeCount === 0}
        className={`${
          isMobile ? 'w-8 h-8' : 'w-6 h-6'
        } rounded flex items-center justify-center transition-colors touch-manipulation ${
          isAudioEnabled
            ? 'bg-orange-500 hover:bg-orange-600'
            : earthquakeCount > 0
            ? 'bg-orange-100 hover:bg-orange-200 border border-orange-300'
            : 'bg-slate-100 border border-slate-200 cursor-not-allowed'
        }`}
        title={
          earthquakeCount === 0
            ? '当前时间范围内无地震事件'
            : isAudioEnabled
            ? '关闭音频播放'
            : `开启音频播放 (${earthquakeCount} 个地震事件)`
        }
      >
        {isAudioEnabled ? (
          <svg className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} text-white`} fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
          </svg>
        ) : (
          <svg
            className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} ${earthquakeCount > 0 ? 'text-orange-600' : 'text-slate-400'}`}
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
          </svg>
        )}
      </button>

      {/* 音量控制 - 移动端简化显示 */}
      {!isMobile && (
        <div className="flex items-center space-x-1">
          <svg className="w-3 h-3 text-slate-500" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
          </svg>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={handleVolumeChange}
            className="w-12 h-1 bg-slate-200 rounded-lg appearance-none cursor-pointer slider-thumb"
            title={`音量: ${Math.round(volume * 100)}%`}
          />
          <span className="text-xs text-slate-500 w-6 text-right">
            {Math.round(volume * 100)}
          </span>
        </div>
      )}

      {/* 音频配置按钮 */}
      {onConfigClick && (
        <button
          onClick={onConfigClick}
          className={`${
            isMobile ? 'w-8 h-8' : 'w-6 h-6'
          } rounded flex items-center justify-center bg-slate-100 hover:bg-slate-200 border border-slate-200 transition-colors touch-manipulation`}
          title="音频配置"
        >
          <Settings className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} text-slate-600`} />
        </button>
      )}

      {/* 音频状态指示器 - 移动端不显示文字 */}
      {isAudioEnabled && !isMobile && (
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
          <span className="text-xs text-slate-600">音频已启用</span>
        </div>
      )}
    </div>
  );
}

// 添加自定义滑块样式
const sliderStyles = `
.slider-thumb::-webkit-slider-thumb {
  appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #f97316;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.slider-thumb::-moz-range-thumb {
  height: 12px;
  width: 12px;
  border-radius: 50%;
  background: #f97316;
  cursor: pointer;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = sliderStyles;
  document.head.appendChild(styleElement);
}
