import { useRef, useEffect } from 'react';

interface AudioWaveformVisualizerProps {
  isEnabled: boolean;
  isPlaying: boolean;
  height?: number;
  width?: number;
  className?: string;
  currentTime?: Date | null;
}

export function AudioWaveformVisualizer({
  isEnabled,
  isPlaying,
  height = 60,
  width = 400,
  className = '',
  currentTime = null
}: AudioWaveformVisualizerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const analyserRef = useRef<AnalyserNode | null>(null);
  const dataArrayRef = useRef<Uint8Array | null>(null);

  // 初始化音频分析器
  useEffect(() => {
    if (!isEnabled) {
      return;
    }

    const initAudioAnalyser = async () => {
      try {
        // 获取Tone.js的AudioContext
        const { getContext, getDestination } = await import('tone');
        const context = getContext();

        // 创建分析器节点
        const analyser = context.createAnalyser();
        analyser.fftSize = 256;
        analyser.smoothingTimeConstant = 0.8;

        // 连接到Tone.js的destination
        try {
          const destination = getDestination();
          // 使用Tone.js的connect方法
          destination.connect(analyser);
        } catch (connectError) {
          console.log('无法连接到Tone.js输出，将使用模拟数据');
        }

        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        analyserRef.current = analyser;
        dataArrayRef.current = dataArray;
      } catch (error) {
        console.error('音频波形分析器初始化失败:', error);
      }
    };

    initAudioAnalyser();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isEnabled]);

  // 绘制波形
  const drawWaveform = () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      return;
    }

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 清除画布
    ctx.clearRect(0, 0, width, height);

    // 如果没有播放，显示静态状态
    if (!isPlaying) {
      drawStaticWaveform(ctx);
      return;
    }

    // 播放时显示真实音频波形
    if (analyserRef.current && dataArrayRef.current) {
      drawRealAudioWaveform(ctx);
    } else {
      // 如果没有音频数据，显示模拟波形
      drawDynamicWaveform(ctx);
    }
  };

  // 绘制静态波形（暂停状态）
  const drawStaticWaveform = (ctx: CanvasRenderingContext2D) => {
    const centerY = height / 2;

    // 绘制中心线
    ctx.strokeStyle = 'rgba(148, 163, 184, 0.4)';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
    ctx.setLineDash([]);
  };

  // 绘制真实音频波形（上下对称布局）
  const drawRealAudioWaveform = (ctx: CanvasRenderingContext2D) => {
    if (!analyserRef.current || !dataArrayRef.current) return;

    // 获取频域数据
    analyserRef.current.getByteFrequencyData(dataArrayRef.current);

    const centerY = height / 2;
    const barCount = Math.min(80, dataArrayRef.current.length); // 增加条数获得更细腻效果
    const barWidth = width / barCount;

    // 创建上下对称的橙色渐变
    const gradientTop = ctx.createLinearGradient(0, centerY, 0, 0);
    gradientTop.addColorStop(0, 'rgba(251, 146, 60, 0.3)'); // 中心浅橙色
    gradientTop.addColorStop(0.6, 'rgba(249, 115, 22, 0.5)'); // 橙色
    gradientTop.addColorStop(1, 'rgba(234, 88, 12, 0.7)'); // 顶部深橙色

    const gradientBottom = ctx.createLinearGradient(0, centerY, 0, height);
    gradientBottom.addColorStop(0, 'rgba(251, 146, 60, 0.3)'); // 中心浅橙色
    gradientBottom.addColorStop(0.6, 'rgba(249, 115, 22, 0.5)'); // 橙色
    gradientBottom.addColorStop(1, 'rgba(234, 88, 12, 0.7)'); // 底部深橙色

    // 绘制上下对称的频谱条
    for (let i = 0; i < barCount; i++) {
      const dataIndex = Math.floor((i / barCount) * dataArrayRef.current.length);
      const normalizedValue = dataArrayRef.current[dataIndex] / 255;
      const barHeight = normalizedValue * height * 0.35; // 减小高度避免遮挡

      const x = i * barWidth;
      const gap = 0.5; // 条之间的间隙
      const actualBarWidth = barWidth - gap;

      // 绘制上半部分
      ctx.fillStyle = gradientTop;
      ctx.fillRect(x, centerY - barHeight, actualBarWidth, barHeight);

      // 绘制下半部分（镜像）
      ctx.fillStyle = gradientBottom;
      ctx.fillRect(x, centerY, actualBarWidth, barHeight);
    }

    // 绘制中心线
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 0.5;
    ctx.setLineDash([2, 2]);
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
    ctx.setLineDash([]);

    // 如果没有音频活动，绘制时域波形
    if (dataArrayRef.current.every(val => val === 0)) {
      analyserRef.current.getByteTimeDomainData(dataArrayRef.current);

      // 绘制上半部分波形
      ctx.strokeStyle = 'rgba(249, 115, 22, 0.6)';
      ctx.lineWidth = 1.5;
      ctx.beginPath();

      const sliceWidth = width / dataArrayRef.current.length;
      let x = 0;

      for (let i = 0; i < dataArrayRef.current.length; i++) {
        const v = (dataArrayRef.current[i] - 128) / 128.0;
        const y = centerY - (v * height * 0.2); // 上半部分

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }

        x += sliceWidth;
      }
      ctx.stroke();

      // 绘制下半部分波形（镜像）
      ctx.beginPath();
      x = 0;

      for (let i = 0; i < dataArrayRef.current.length; i++) {
        const v = (dataArrayRef.current[i] - 128) / 128.0;
        const y = centerY + (v * height * 0.2); // 下半部分（镜像）

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }

        x += sliceWidth;
      }
      ctx.stroke();
    }
  };

  // 绘制动态波形（播放状态，上下对称布局）
  const drawDynamicWaveform = (ctx: CanvasRenderingContext2D) => {
    const time = Date.now() * 0.003;
    const centerY = height / 2;
    const amplitude = height * 0.2; // 减小振幅，避免遮挡直方图
    const points = 200;

    // 创建上下对称的橙色渐变
    const gradientTop = ctx.createLinearGradient(0, centerY, 0, 0);
    gradientTop.addColorStop(0, 'rgba(251, 146, 60, 0.4)'); // 中心浅橙色
    gradientTop.addColorStop(0.5, 'rgba(249, 115, 22, 0.6)'); // 橙色
    gradientTop.addColorStop(1, 'rgba(234, 88, 12, 0.8)'); // 顶部深橙色

    const gradientBottom = ctx.createLinearGradient(0, centerY, 0, height);
    gradientBottom.addColorStop(0, 'rgba(251, 146, 60, 0.4)'); // 中心浅橙色
    gradientBottom.addColorStop(0.5, 'rgba(249, 115, 22, 0.6)'); // 橙色
    gradientBottom.addColorStop(1, 'rgba(234, 88, 12, 0.8)'); // 底部深橙色

    ctx.lineWidth = 1.5;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 绘制上半部分波形
    ctx.strokeStyle = gradientTop;
    ctx.beginPath();
    for (let i = 0; i <= points; i++) {
      const x = (i / points) * width;
      const frequency1 = 0.015;
      const frequency2 = 0.04;
      const frequency3 = 0.07;

      // 组合多个正弦波创建复杂波形
      const waveValue =
        Math.sin(time + x * frequency1) * 0.7 +
        Math.sin(time * 1.3 + x * frequency2) * 0.2 +
        Math.sin(time * 2.1 + x * frequency3) * 0.1;

      const y = centerY - Math.abs(waveValue) * amplitude; // 上半部分，取绝对值确保向上

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    // 绘制下半部分波形（镜像）
    ctx.strokeStyle = gradientBottom;
    ctx.beginPath();
    for (let i = 0; i <= points; i++) {
      const x = (i / points) * width;
      const frequency1 = 0.015;
      const frequency2 = 0.04;
      const frequency3 = 0.07;

      // 组合多个正弦波创建复杂波形
      const waveValue =
        Math.sin(time + x * frequency1) * 0.7 +
        Math.sin(time * 1.3 + x * frequency2) * 0.2 +
        Math.sin(time * 2.1 + x * frequency3) * 0.1;

      const y = centerY + Math.abs(waveValue) * amplitude; // 下半部分，镜像

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    // 绘制中心线
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 0.5;
    ctx.setLineDash([3, 3]);
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
    ctx.setLineDash([]);

    // 添加轻微的橙色发光效果
    ctx.shadowColor = 'rgba(249, 115, 22, 0.4)';
    ctx.shadowBlur = 8;

    // 重新绘制上半部分以添加发光效果
    ctx.strokeStyle = gradientTop;
    ctx.lineWidth = 1;
    ctx.beginPath();
    for (let i = 0; i <= points; i++) {
      const x = (i / points) * width;
      const frequency1 = 0.015;
      const frequency2 = 0.04;
      const frequency3 = 0.07;

      const waveValue =
        Math.sin(time + x * frequency1) * 0.7 +
        Math.sin(time * 1.3 + x * frequency2) * 0.2 +
        Math.sin(time * 2.1 + x * frequency3) * 0.1;

      const y = centerY - Math.abs(waveValue) * amplitude;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    // 重新绘制下半部分以添加发光效果
    ctx.strokeStyle = gradientBottom;
    ctx.beginPath();
    for (let i = 0; i <= points; i++) {
      const x = (i / points) * width;
      const frequency1 = 0.015;
      const frequency2 = 0.04;
      const frequency3 = 0.07;

      const waveValue =
        Math.sin(time + x * frequency1) * 0.7 +
        Math.sin(time * 1.3 + x * frequency2) * 0.2 +
        Math.sin(time * 2.1 + x * frequency3) * 0.1;

      const y = centerY + Math.abs(waveValue) * amplitude;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    ctx.shadowBlur = 0;
  };

  // 动画循环
  useEffect(() => {
    if (!isEnabled) {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      return;
    }

    const animate = () => {
      drawWaveform();
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isEnabled, isPlaying, width, height]);

  if (!isEnabled) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="w-full"
        style={{
          height: `${height}px`,
          imageRendering: 'auto'
        }}
      />

      {/* 当前时间显示 - 只在播放时显示 */}
      {isPlaying && currentTime && (
        <div className="absolute top-1 right-2 flex items-center space-x-1">
          <div className="w-1.5 h-1.5 rounded-full bg-orange-500 animate-pulse" />
          <span className="text-xs text-white font-medium font-mono bg-orange-600/70 px-2 py-0.5 rounded tabular-nums">
            {currentTime.toLocaleString('zh-CN', {
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            })}
          </span>
        </div>
      )}
    </div>
  );
}
