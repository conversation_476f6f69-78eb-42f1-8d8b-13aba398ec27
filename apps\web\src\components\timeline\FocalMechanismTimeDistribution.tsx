import React, { useMemo } from 'react';
import type { FocalMechanism } from '../../types';

interface FocalMechanismTimeDistributionProps {
  focalMechanisms: FocalMechanism[];
  totalRange: { start: Date; end: Date };
  maskStart: number;
  maskEnd: number;
  height?: number;
}

export function FocalMechanismTimeDistribution({
  focalMechanisms,
  totalRange,
  maskStart,
  maskEnd,
  height = 60
}: FocalMechanismTimeDistributionProps) {
  
  // 计算每个震源机制解在时间轴上的位置
  const focalMechanismPoints = useMemo(() => {
    if (!focalMechanisms.length || !totalRange) return [];

    const totalTime = totalRange.end.getTime() - totalRange.start.getTime();
    
    return focalMechanisms.map(fm => {
      const time = new Date(`${fm.date}T${fm.time}`);
      const position = (time.getTime() - totalRange.start.getTime()) / totalTime;
      
      return {
        id: fm.id,
        position: Math.max(0, Math.min(1, position)), // 确保在0-1范围内
        time,
        magnitude: fm.magnitude,
        data: fm
      };
    }).filter(point => point.position >= 0 && point.position <= 1); // 过滤掉超出范围的点
  }, [focalMechanisms, totalRange]);

  // 按震级分组点的颜色和大小
  const getPointStyle = (magnitude: number) => {
    // 根据震级确定颜色
    let color = '#ef4444'; // 红色 (大震级)
    if (magnitude < 1.0) {
      color = '#06b6d4'; // 青色 (小震级)
    } else if (magnitude < 2.0) {
      color = '#10b981'; // 绿色 (中小震级)
    } else if (magnitude < 3.0) {
      color = '#f59e0b'; // 黄色 (中震级)
    } else if (magnitude < 4.0) {
      color = '#f97316'; // 橙色 (较大震级)
    }

    // 根据震级确定大小 - 更小的点，避免遮挡
    const size = Math.max(2, Math.min(6, 2 + magnitude * 1.5));

    return {
      backgroundColor: color,
      width: size,
      height: size,
      opacity: 0.7
    };
  };

  // 移除点击事件处理，只用于视觉展示

  return (
    <div className="relative w-full pointer-events-none" style={{ height, pointerEvents: 'none' }}>
      {/* 震源机制解点 - 简化显示，不遮挡时间轴 */}
      <div className="absolute inset-0 pointer-events-none" style={{ pointerEvents: 'none' }}>
        {focalMechanismPoints.map(point => {
          const style = getPointStyle(point.magnitude);
          const isInMask = point.position >= maskStart && point.position <= maskEnd;
          
          return (
            <div
              key={point.id}
              className="absolute rounded-full transition-opacity duration-200"
              style={{
                left: `${point.position * 100}%`,
                top: `${height / 2 - style.height / 2}px`,
                width: style.width,
                height: style.height,
                backgroundColor: style.backgroundColor,
                opacity: isInMask ? 0.8 : 0.2,
                transform: 'translateX(-50%)',
                zIndex: 2,
                pointerEvents: 'none'
              }}
            />
          );
        })}
      </div>
    </div>
  );
} 