import React, { useRef, useCallback } from 'react';

interface TimeHistogramProps {
  data: Array<{ time: Date; count: number }>;
  totalRange: { start: Date; end: Date };
  onTimeClick: (time: Date) => void;
  // 蒙版相关属性
  maskStart: number; // 0-1
  maskEnd: number; // 0-1
  onMaskDrag: (e: React.MouseEvent, type: 'mask' | 'start' | 'end') => void;
  // 时间范围信息
  maskTimeRange: { start: Date; end: Date } | null;
  onTimeRangeChange?: (start: Date, end: Date) => void;
  // 音频模式
  isAudioMode?: boolean;
  // 移动端紧凑模式
  isCompact?: boolean;
}

export function TimeHistogram({
  data,
  totalRange,
  onTimeClick,
  maskStart,
  maskEnd,
  onMaskDrag,
  maskTimeRange,
  isAudioMode = false,
  isCompact = false
}: Omit<TimeHistogramProps, 'onTimeRangeChange'>) {
  // 所有hooks必须在组件顶层调用，不能在条件语句中
  const isDraggingRef = useRef(false);
  const dragStartTimeRef = useRef<number>(0);

  // 触摸事件处理 - 移到顶层
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    isDraggingRef.current = false;
    dragStartTimeRef.current = Date.now();
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    const timeDiff = Date.now() - dragStartTimeRef.current;
    if (timeDiff > 100) {
      isDraggingRef.current = true;
    }
  }, []);

  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    // 如果不是拖拽，则处理为点击事件
    if (!isDraggingRef.current) {
      const touch = e.changedTouches[0];
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      const x = touch.clientX - rect.left;
      const clickRatio = x / rect.width;

      // 计算点击的时间
      const totalTime = totalRange.end.getTime() - totalRange.start.getTime();
      const clickTime = new Date(totalRange.start.getTime() + totalTime * clickRatio);

      onTimeClick(clickTime);
    }

    isDraggingRef.current = false;
  }, [totalRange, onTimeClick]);

  // 触摸拖拽事件处理
  const handleTouchStartDrag = useCallback((e: React.TouchEvent, type: 'mask' | 'start' | 'end') => {
    e.stopPropagation();
    isDraggingRef.current = true;

    // 创建模拟的鼠标事件来复用现有的拖拽逻辑
    const touch = e.touches[0];
    const mouseEvent = {
      ...e,
      clientX: touch.clientX,
      clientY: touch.clientY,
      preventDefault: () => {},
      stopPropagation: () => e.stopPropagation()
    } as any;

    onMaskDrag(mouseEvent, type);
  }, [onMaskDrag]);

  const handleTouchClick = useCallback((e: React.TouchEvent) => {
    e.stopPropagation();
  }, []);

  // 条件渲染移到所有hooks调用之后
  if (!data.length || !totalRange) {
    return (
      <div className={`relative w-full ${isCompact ? 'h-12' : 'h-24'} bg-slate-100 rounded-lg overflow-hidden timeline-container flex items-center justify-center border border-slate-200`}>
        <span className="text-slate-500 text-sm">暂无数据</span>
      </div>
    );
  }

  const maxCount = Math.max(...data.map(d => d.count));

  // 计算蒙版的位置和宽度
  const maskStartPercent = maskStart * 100;
  const maskEndPercent = maskEnd * 100;
  const maskWidth = maskEndPercent - maskStartPercent;

  const handleBarClick = (index: number) => {
    const clickedTime = data[index].time;
    onTimeClick(clickedTime);
  };

  // 处理时间轴区域点击（包括空白区域）
  const handleTimelineClick = (e: React.MouseEvent) => {
    // 如果是拖拽操作，则不处理点击
    if (isDraggingRef.current) {
      return;
    }

    // 阻止事件冒泡，避免与拖拽事件冲突
    e.stopPropagation();

    const container = e.currentTarget;
    const rect = container.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickRatio = clickX / rect.width;

    // 计算点击位置对应的时间
    const totalTime = totalRange.end.getTime() - totalRange.start.getTime();
    const clickedTime = new Date(totalRange.start.getTime() + totalTime * clickRatio);

    onTimeClick(clickedTime);
  };

  // 处理鼠标按下事件
  const handleTimelineMouseDown = (e: React.MouseEvent) => {
    isDraggingRef.current = false;
    dragStartTimeRef.current = Date.now();
  };

  // 处理鼠标移动事件
  const handleTimelineMouseMove = (e: React.MouseEvent) => {
    // 如果鼠标移动超过一定距离或时间，则认为是拖拽
    const timeDiff = Date.now() - dragStartTimeRef.current;
    if (timeDiff > 100) { // 100ms后认为可能是拖拽
      isDraggingRef.current = true;
    }
  };





  // 计算时间刻度
  const getTimeLabels = () => {
    const labels = [];
    const timeSpan = totalRange.end.getTime() - totalRange.start.getTime();
    const labelCount = isCompact ? 4 : 5; // 紧凑模式显示4个标签，普通模式5个

    for (let i = 0; i <= labelCount; i++) {
      const time = new Date(totalRange.start.getTime() + (timeSpan * i) / labelCount);
      labels.push({
        time,
        position: (i / labelCount) * 100,
        label: isCompact
          ? time.toLocaleDateString('zh-CN', {
              month: '2-digit',
              day: '2-digit'
            })
          : time.toLocaleDateString('zh-CN', {
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit'
            })
      });
    }
    return labels;
  };

  const timeLabels = getTimeLabels();

  return (
    <div className="w-full">
      {/* 直方图容器 */}
      <div
        className={`relative w-full ${isCompact ? 'h-10' : 'h-14'} bg-slate-100 rounded overflow-hidden timeline-container border border-slate-200 cursor-pointer touch-manipulation`}
        style={{ touchAction: 'pan-x' }}
        onClick={handleTimelineClick}
        onMouseDown={handleTimelineMouseDown}
        onMouseMove={handleTimelineMouseMove}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* 时间分布直方图 */}
        <div className="flex items-end h-full px-1">
          {data.map((item, index) => {
            // 按照参考截图样式，简化高度计算
            const height = maxCount > 0 ?
              Math.max(2, (item.count / maxCount) * 90) : 2;
            const itemPosition = (index / data.length) * 100;
            const isInMask = itemPosition >= maskStartPercent && itemPosition <= maskEndPercent;

            return (
              <div
                key={index}
                className={`flex-1 cursor-pointer transition-colors ${
                  isInMask
                    ? 'bg-blue-500 hover:bg-blue-600' // 蒙版内高亮显示
                    : 'bg-slate-300 hover:bg-slate-400'
                }`}
                style={{
                  height: `${height}%`,
                  marginRight: '1px'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleBarClick(index);
                }}
                title={`${item.time.toLocaleDateString()}: ${item.count} 事件`}
              />
            );
          })}
        </div>



        {/* 半透明蒙版覆盖层 - 更透明的效果 */}
        <div className="absolute top-0 h-full w-full pointer-events-none" style={{ zIndex: 2 }}>
          {/* 蒙版选中区域 - 非常透明的蓝色覆盖 */}
          <div
            className="absolute top-0 h-full bg-blue-500"
            style={{
              left: `${maskStartPercent}%`,
              width: `${maskWidth}%`,
              opacity: 0.15
            }}
          />
        </div>

        {/* 可拖拽的蒙版控制器 */}
        <div className="absolute top-0 h-full w-full" style={{ zIndex: 3 }}>
          {/* 蒙版主体 - 可拖拽整个蒙版 */}
          <div
            className="absolute top-0 h-full cursor-move hover:bg-blue-500 touch-manipulation"
            style={{
              left: `${maskStartPercent}%`,
              width: `${maskWidth}%`,
              opacity: 0,
              touchAction: 'pan-x'
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              isDraggingRef.current = true;
              onMaskDrag(e, 'mask');
            }}
            onTouchStart={(e) => handleTouchStartDrag(e, 'mask')}
            onClick={(e) => {
              e.stopPropagation();
            }}
            onTouchEnd={handleTouchClick}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.08';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '0';
            }}
          />

          {/* 左侧拖拽手柄 - 美化样式 */}
          <div
            className="absolute top-0 h-full w-2 cursor-ew-resize group touch-manipulation"
            style={{
              left: `${maskStartPercent}%`,
              transform: 'translateX(-50%)',
              touchAction: 'pan-x'
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              isDraggingRef.current = true;
              onMaskDrag(e, 'start');
            }}
            onTouchStart={(e) => handleTouchStartDrag(e, 'start')}
            onClick={(e) => {
              e.stopPropagation();
            }}
            onTouchEnd={handleTouchClick}
          >
            {/* 手柄视觉指示器 - 移动端增大触摸区域 */}
            <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full opacity-60 group-hover:opacity-90 transition-opacity duration-200 ${
              isCompact ? 'w-2 h-6 group-hover:h-8' : 'w-1 h-8 group-hover:h-12'
            } bg-blue-500`} />
          </div>

          {/* 右侧拖拽手柄 - 音频模式下变色 */}
          <div
            className="absolute top-0 h-full w-2 cursor-ew-resize group touch-manipulation"
            style={{
              left: `${maskEndPercent}%`,
              transform: 'translateX(-50%)',
              touchAction: 'pan-x'
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              isDraggingRef.current = true;
              onMaskDrag(e, 'end');
            }}
            onTouchStart={(e) => handleTouchStartDrag(e, 'end')}
            onClick={(e) => {
              e.stopPropagation();
            }}
            onTouchEnd={handleTouchClick}
          >
            {/* 手柄视觉指示器 - 音频模式下使用橙色，移动端增大触摸区域 */}
            <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full opacity-60 group-hover:opacity-90 transition-all duration-200 ${
              isCompact ? 'w-2 h-6 group-hover:h-8' : 'w-1 h-8 group-hover:h-12'
            } ${isAudioMode ? 'bg-orange-500' : 'bg-blue-500'}`} />
          </div>
        </div>

        {/* 时间标签 */}
        {!isCompact && (
          <div className="absolute -bottom-6 left-0 right-0 flex justify-between text-xs text-slate-500">
            <span>{totalRange.start.getFullYear()}</span>
            <span>{totalRange.end.getFullYear()}</span>
          </div>
        )}
      </div>

      {/* 时间刻度标签 */}
      <div className="relative w-full h-6 mt-1">
        {timeLabels.map((label, index) => (
          <div
            key={index}
            className="absolute text-xs text-slate-500 transform -translate-x-1/2"
            style={{ left: `${label.position}%`, top: '2px' }}
          >
            {label.label}
          </div>
        ))}
      </div>
    </div>
  );
}
