import React, { useState, useEffect } from 'react';

interface TimeRangePickerProps {
  currentRange: { start: Date; end: Date } | null;
  loading: boolean;
  onConfirm: (start: Date, end: Date) => void;
  onCancel: () => void;
  isVisible: boolean;
}

interface QuickOption {
  label: string;
  days: number;
  icon: string;
}

const quickOptions: QuickOption[] = [
  { label: '最近1周', days: 7, icon: '📅' },
  { label: '最近1个月', days: 30, icon: '🗓️' },
  { label: '最近半年', days: 180, icon: '📊' },
  { label: '最近1年', days: 365, icon: '📊' },
];

export function TimeRangePicker({
  currentRange,
  loading,
  onConfirm,
  onCancel,
  isVisible
}: TimeRangePickerProps) {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [error, setError] = useState<string | null>(null);

  // 初始化日期值
  useEffect(() => {
    if (currentRange) {
      setStartDate(currentRange.start.toISOString().split('T')[0]);
      setEndDate(currentRange.end.toISOString().split('T')[0]);
    }
  }, [currentRange]);

  // 验证日期范围
  const validateDateRange = (start: string, end: string): string | null => {
    if (!start || !end) {
      return '请选择开始和结束日期';
    }

    const startTime = new Date(start);
    const endTime = new Date(end);
    const now = new Date();

    if (startTime >= endTime) {
      return '开始时间必须早于结束时间';
    }

    if (endTime > now) {
      return '结束时间不能晚于今天';
    }

    const diffInDays = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60 * 24);
    if (diffInDays > 365) {
      return '查询时间范围不能超过一年（365天）';
    }

    return null;
  };

  // 处理日期变化
  const handleDateChange = (type: 'start' | 'end', value: string) => {
    if (type === 'start') {
      setStartDate(value);
      const validationError = validateDateRange(value, endDate);
      setError(validationError);
    } else {
      setEndDate(value);
      const validationError = validateDateRange(startDate, value);
      setError(validationError);
    }
  };

  // 处理快捷选项点击
  const handleQuickOption = (days: number) => {
    const end = new Date();
    const start = new Date(end.getTime() - days * 24 * 60 * 60 * 1000);
    
    const startStr = start.toISOString().split('T')[0];
    const endStr = end.toISOString().split('T')[0];
    
    setStartDate(startStr);
    setEndDate(endStr);
    setError(null);
  };

  // 处理确认
  const handleConfirm = () => {
    const validationError = validateDateRange(startDate, endDate);
    if (validationError) {
      setError(validationError);
      return;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);
    onConfirm(start, end);
  };

  // 处理取消
  const handleCancel = () => {
    setError(null);
    onCancel();
  };

  if (!isVisible) return null;

  const isValid = !error && startDate && endDate;

  return (
    <div
      className="absolute bottom-full left-0 mb-2 z-50 w-80"
      onClick={(e) => e.stopPropagation()}
    >
      <div className="floating-panel-unified rounded-lg p-3 shadow-lg border border-slate-200">
        {/* 标题 */}
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-slate-800">选择数据时间范围</h3>
          <button
            onClick={handleCancel}
            className="text-slate-400 hover:text-slate-600 transition-colors"
            disabled={loading}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>

        {/* 当前范围显示 */}
        {currentRange && (
          <div className="mb-3 p-2 bg-slate-50 rounded">
            <div className="text-xs text-slate-600 mb-1">当前数据范围</div>
            <div className="text-xs font-medium text-slate-800">
              {currentRange.start.toLocaleDateString('zh-CN')} - {currentRange.end.toLocaleDateString('zh-CN')}
            </div>
          </div>
        )}

        {/* 快捷选项 */}
        <div className="mb-3">
          <div className="text-xs text-slate-600 mb-2">快捷选择</div>
          <div className="grid grid-cols-2 gap-1.5">
            {quickOptions.map((option) => (
              <button
                key={option.days}
                onClick={() => handleQuickOption(option.days)}
                disabled={loading}
                className="flex items-center justify-center space-x-1 px-2 py-1.5 text-xs bg-slate-100 hover:bg-slate-200 rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="text-xs">{option.icon}</span>
                <span>{option.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* 日期选择器 */}
        <div className="mb-3">
          <div className="text-xs text-slate-600 mb-2">自定义范围</div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-slate-500 mb-1">开始日期</label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => handleDateChange('start', e.target.value)}
                disabled={loading}
                className="w-full px-2 py-1.5 text-xs border border-slate-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              />
            </div>
            <div>
              <label className="block text-xs text-slate-500 mb-1">结束日期</label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => handleDateChange('end', e.target.value)}
                disabled={loading}
                className="w-full px-2 py-1.5 text-xs border border-slate-200 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
              />
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded">
            <div className="flex items-center space-x-1.5">
              <svg className="w-3 h-3 text-red-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span className="text-xs text-red-600">{error}</span>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex items-center justify-end space-x-2">
          <button
            onClick={handleCancel}
            disabled={loading}
            className="px-3 py-1.5 text-xs text-slate-600 hover:text-slate-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            disabled={loading || !isValid}
            className="px-3 py-1.5 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1.5"
          >
            {loading && (
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
            )}
            <span>{loading ? '更新中...' : '确认'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
