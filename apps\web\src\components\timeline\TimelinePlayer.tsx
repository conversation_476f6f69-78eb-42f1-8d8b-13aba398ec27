import React, { useState, useEffect, useRef } from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { useFocalMechanismData } from '../../hooks/useFocalMechanismData';
import { useTimelineState } from '../../hooks/useTimelineState';
import { useAudioPlayer } from '../../hooks/useAudioPlayerTone';
import { useIsMobile } from '../../hooks/useIsMobile';
import { TimeHistogram } from './TimeHistogram';
import { TimeRangePicker } from './TimeRangePicker';
import { FocalMechanismTimeDistribution } from './FocalMechanismTimeDistribution';
import { AudioControls } from './AudioControls';
import { AudioWaveformVisualizer } from './AudioWaveformVisualizer';
import { AudioConfigPanel } from '../audio/AudioConfigPanel';
import * as turf from '@turf/turf';

interface TimelinePlayerProps {
  isVisible: boolean;
  onClose: () => void;
  isEmbedded?: boolean; // 是否为嵌入模式（从底部状态栏展开）
  isMobileBottom?: boolean; // 是否为移动端底部面板模式
}

export function TimelinePlayer({ isVisible, onClose, isEmbedded = false, isMobileBottom = false }: TimelinePlayerProps) {
  const { setTimeRange, filters, layerVisibility } = useMapStore();
  const { earthquakes, allEarthquakes, loading, error, retryCount, refetch, dataTimeRange: sharedDataTimeRange, setTimelinePlayback, updateDataTimeRange } = useEarthquakeData();

  // 移动端检测
  const { isMobile, isTouchDevice } = useIsMobile();
  
  // 获取震源机制解数据
  const { 
    focalMechanisms, 
    allFocalMechanisms
  } = useFocalMechanismData();

  // 使用共享的时间轴状态
  const timelineState = useTimelineState();
  const {
    isPlaying,
    currentTime,
    maskStart,
    maskEnd,
    playbackSpeed,
    setIsPlaying,
    setCurrentTime,
    setMaskStart,
    setMaskEnd,
    setPlaybackSpeed,
    setMaskTimeRange
  } = timelineState;

  const [showTimeRangePicker, setShowTimeRangePicker] = useState(false);
  const [timeRangePickerLoading, setTimeRangePickerLoading] = useState(false);

  // 拖拽相关的本地状态
  const [isDragging, setIsDragging] = useState(false);
  const [dragType, setDragType] = useState<'mask' | 'start' | 'end' | null>(null);
  const [dragStartX, setDragStartX] = useState(0);
  const [dragStartMaskStart, setDragStartMaskStart] = useState(0);
  const [dragStartMaskEnd, setDragStartMaskEnd] = useState(0);

  // 播放区间控制（暂时使用全部数据范围）
  const [playbackRange] = useState<{start: Date, end: Date} | null>(null);

  // 音频模式下的固定起始时间
  const [audioModeFixedStart, setAudioModeFixedStart] = useState<number | null>(null);

  // 防止快速点击暂停播放的状态
  const [lastClickTime, setLastClickTime] = useState<number>(0);
  const CLICK_DEBOUNCE_TIME = 300; // 300ms内的快速点击不会暂停播放

  // 音频相关状态
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [audioVolume, setAudioVolume] = useState(0.7);
  const [showAudioConfig, setShowAudioConfig] = useState(false);
  const {
    playRealtimeAudio,
    setVolume: setAudioPlayerVolume,
    audioConfig,
    setAudioConfig,
    previewInstrument,
    isSupported: isAudioSupported,
    initializeAudio
  } = useAudioPlayer();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 使用共享的数据时间范围
  const dataTimeRange = sharedDataTimeRange;

  // 音频处理函数
  const handleAudioToggle = async () => {
    if (!isAudioEnabled) {
      // 启用音频时初始化并自动开始播放
      await initializeAudio();
      setIsAudioEnabled(true);
      setIsPlaying(true); // 自动开始播放
    } else {
      // 禁用音频时停止播放
      setIsAudioEnabled(false);
      setIsPlaying(false);
    }
  };

  const handleVolumeChange = (volume: number) => {
    setAudioVolume(volume);
    setAudioPlayerVolume(volume);
  };

  // 计算筛选后的地震数据（用于音频播放）
  const filteredEarthquakesForAudio = React.useMemo(() => {
    if (!allEarthquakes || !allEarthquakes.length) {
      return [];
    }

    return allEarthquakes.filter(eq => {
      // 震级过滤
      if (filters.magnitudeRange) {
        if (eq.magnitude < filters.magnitudeRange[0] || eq.magnitude > filters.magnitudeRange[1]) {
          return false;
        }
      }

      // 深度过滤
      if (filters.depthRange) {
        if (eq.depth < filters.depthRange[0] || eq.depth > filters.depthRange[1]) {
          return false;
        }
      }

      // 区域过滤
      if (filters.selectedRegions && filters.selectedRegions.length > 0) {
        if (!filters.selectedRegions.includes(eq.region)) {
          return false;
        }
      }

      // 空间筛选
      if (filters.spatialFilter && filters.spatialFilter.center && filters.spatialFilter.radius) {
        const distance = turf.distance(
          [eq.longitude, eq.latitude],
          filters.spatialFilter.center,
          { units: 'kilometers' }
        );
        if (distance > filters.spatialFilter.radius) {
          return false;
        }
      }

      return true;
    });
  }, [allEarthquakes, filters]);

  // 音频跟随播放
  useEffect(() => {
    if (isAudioEnabled && isPlaying && filteredEarthquakesForAudio && currentTime) {
      playRealtimeAudio(filteredEarthquakesForAudio, currentTime, 3600000, audioVolume); // 1小时时间窗口
    }
  }, [isAudioEnabled, isPlaying, filteredEarthquakesForAudio, currentTime, audioVolume, playRealtimeAudio]);

  // 计算时间分布数据（用于直方图，使用筛选后的数据）
  const timeDistribution = React.useMemo(() => {
    if (!filteredEarthquakesForAudio || !filteredEarthquakesForAudio.length || !dataTimeRange) {
      return [];
    }

    const bins = 50; // 分成50个时间段
    const timeSpan = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();

    if (timeSpan <= 0) {
      return [];
    }

    const binSize = timeSpan / bins;
    const distribution = new Array(bins).fill(0);

    filteredEarthquakesForAudio.forEach(eq => {
      const time = new Date(eq.occurred_at).getTime();
      const binIndex = Math.floor((time - dataTimeRange.start.getTime()) / binSize);
      if (binIndex >= 0 && binIndex < bins) {
        distribution[binIndex]++;
      }
    });

    const result = distribution.map((count, index) => ({
      time: new Date(dataTimeRange.start.getTime() + index * binSize),
      count
    }));

    return result;
  }, [filteredEarthquakesForAudio, dataTimeRange]);

  // 计算过滤后的震源机制解数据（应用所有过滤器）
  const filteredFocalMechanisms = React.useMemo(() => {
    if (!allFocalMechanisms || !allFocalMechanisms.length || !dataTimeRange) {
      return [];
    }

    return allFocalMechanisms.filter(fm => {
      // 震级过滤
      if (filters.magnitudeRange) {
        if (fm.magnitude < filters.magnitudeRange[0] || fm.magnitude > filters.magnitudeRange[1]) {
          return false;
        }
      }

      // 深度过滤
      if (filters.depthRange) {
        if (fm.depth < filters.depthRange[0] || fm.depth > filters.depthRange[1]) {
          return false;
        }
      }

      // 区域过滤
      if (filters.selectedRegions && filters.selectedRegions.length > 0) {
        if (!filters.selectedRegions.includes(fm.region)) {
          return false;
        }
      }

      // 空间筛选
      if (filters.spatialFilter && filters.spatialFilter.center && filters.spatialFilter.radius) {
        const distance = turf.distance(
          [fm.longitude, fm.latitude],
          filters.spatialFilter.center,
          { units: 'kilometers' }
        );
        if (distance > filters.spatialFilter.radius) {
          return false;
        }
      }

      return true;
    });
  }, [allFocalMechanisms, dataTimeRange, filters.magnitudeRange, filters.depthRange, filters.selectedRegions, filters.spatialFilter]);

  // 获取实际播放范围（优先使用自定义范围）
  const effectivePlaybackRange = React.useMemo(() => {
    return playbackRange || dataTimeRange;
  }, [playbackRange, dataTimeRange]);

  // 计算动态播放步长（根据数据时间跨度无极调整）
  const playbackStepSize = React.useMemo(() => {
    if (!dataTimeRange) {
      return 10 * 60 * 1000; // 默认 10 分钟
    }

    const totalTimeSpan = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
    const totalDays = totalTimeSpan / (24 * 60 * 60 * 1000);

    // 无极调整：让播放步长与时间跨度成正比
    // 目标：整个时间轴播放大约需要 60-120 秒（1-2分钟）
    const targetPlaybackDuration = 90; // 秒
    const updateInterval = 0.1; // 更新间隔 100ms
    const totalSteps = targetPlaybackDuration / updateInterval;
    
    // 计算每步应该跨越的时间（毫秒）
    const stepSize = totalTimeSpan / totalSteps;
    
    // 设置最小和最大步长限制
    const minStepSize = 1 * 60 * 1000; // 最小 1 分钟
    const maxStepSize = 12 * 60 * 60 * 1000; // 最大 12 小时
    
    return Math.max(minStepSize, Math.min(maxStepSize, stepSize));
  }, [dataTimeRange]);

  // 播放控制 - 区分音频模式和普通模式
  useEffect(() => {
    if (isPlaying && dataTimeRange) {
      intervalRef.current = setInterval(() => {
        const totalTime = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
        const timeStepRatio = (playbackSpeed * playbackStepSize) / totalTime;

        if (isAudioEnabled) {
          // 音频模式：固定起始时间，只移动结束时间
          if (audioModeFixedStart === null) {
            // 首次启用音频模式，固定当前的起始时间
            setAudioModeFixedStart(maskStart);
          }

          const fixedStart = audioModeFixedStart !== null ? audioModeFixedStart : maskStart;
          const newMaskEnd = maskEnd + timeStepRatio;

          // 检查是否播放到末尾
          if (newMaskEnd >= 1.0) {
            // 重置：开始时间为0，结束时间给一个小的初始值以确保有时间窗口
            const resetMaskStart = 0;
            const resetMaskEnd = timeStepRatio; // 给一个小的初始时间窗口，确保有地震事件可以播放

            setMaskStart(resetMaskStart);
            setMaskEnd(resetMaskEnd);
            setAudioModeFixedStart(resetMaskStart); // 更新固定起始时间

            // 更新 currentTime 为新的结束时间点
            const newCurrentTime = new Date(dataTimeRange.start.getTime() + totalTime * resetMaskEnd);
            setCurrentTime(newCurrentTime);

            console.log('🔄 音频模式循环播放：重置到起点，初始时间窗口', { resetMaskStart, resetMaskEnd });
          } else {
            // 正常播放：保持起始时间固定，只移动结束时间
            setMaskStart(fixedStart);
            setMaskEnd(newMaskEnd);

            // 更新 currentTime 为蒙版的最大时间点
            const newCurrentTime = new Date(dataTimeRange.start.getTime() + totalTime * newMaskEnd);
            setCurrentTime(newCurrentTime);
          }
        } else {
          // 普通模式：原有的移动整个蒙版的逻辑
          const maskWidth = maskEnd - maskStart;
          const newMaskStart = maskStart + timeStepRatio;
          const newMaskEnd = maskEnd + timeStepRatio;

          // 检查是否触底，如果是则立即重置到起点
          if (newMaskEnd >= 1.0) {
            const resetMaskStart = 0;
            const resetMaskEnd = maskWidth;

            setMaskStart(resetMaskStart);
            setMaskEnd(resetMaskEnd);

            const newCurrentTime = new Date(dataTimeRange.start.getTime() + totalTime * resetMaskEnd);
            setCurrentTime(newCurrentTime);
          } else {
            setMaskStart(newMaskStart);
            setMaskEnd(newMaskEnd);

            const newCurrentTime = new Date(dataTimeRange.start.getTime() + totalTime * newMaskEnd);
            setCurrentTime(newCurrentTime);
          }
        }
      }, 100); // 更丝滑的更新频率，100ms
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [isPlaying, playbackSpeed, playbackStepSize, dataTimeRange, maskStart, maskEnd, setMaskStart, setMaskEnd, setCurrentTime, isAudioEnabled, audioModeFixedStart]);

  // 计算蒙版对应的实际时间范围
  const calculatedMaskTimeRange = React.useMemo(() => {
    if (!dataTimeRange) return null;

    const totalTime = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
    const startTime = new Date(dataTimeRange.start.getTime() + totalTime * maskStart);
    const endTime = new Date(dataTimeRange.start.getTime() + totalTime * maskEnd);

    return { start: startTime, end: endTime };
  }, [dataTimeRange, maskStart, maskEnd]);

  // 音频模式切换处理
  useEffect(() => {
    if (isAudioEnabled) {
      // 启用音频模式时，固定当前的起始时间
      setAudioModeFixedStart(maskStart);
    } else {
      // 禁用音频模式时，清除固定起始时间
      setAudioModeFixedStart(null);
    }
  }, [isAudioEnabled, maskStart]);

  // 更新共享状态中的蒙版时间范围
  useEffect(() => {
    setMaskTimeRange(calculatedMaskTimeRange);
  }, [calculatedMaskTimeRange, setMaskTimeRange]);

  // 更新地图过滤器 - 使用蒙版时间范围
  useEffect(() => {
    if (calculatedMaskTimeRange) {
      setTimeRange({
        start: calculatedMaskTimeRange.start,
        end: calculatedMaskTimeRange.end
      });
    }
  }, [calculatedMaskTimeRange, setTimeRange]);

  // 播放时移动蒙版 - 已移除，现在是播放控制直接移动蒙版，currentTime跟随蒙版
  // useEffect(() => {
  //   if (isPlaying && dataTimeRange) {
  //     // 计算当前时间在整个时间范围中的位置
  //     const totalTime = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
  //     const currentPosition = (currentTime.getTime() - dataTimeRange.start.getTime()) / totalTime;

  //     // 保持蒙版宽度不变，移动蒙版位置
  //     const maskWidth = maskEnd - maskStart;

  //     // 正常移动蒙版，让循环逻辑在播放控制中处理
  //     const newMaskStart = Math.max(0, Math.min(1 - maskWidth, currentPosition - maskWidth / 2));
  //     const newMaskEnd = newMaskStart + maskWidth;

  //     // 使用批量更新减少渲染次数
  //     if (Math.abs(newMaskStart - maskStart) > 0.001 || Math.abs(newMaskEnd - maskEnd) > 0.001) {
  //       setMaskStart(newMaskStart);
  //       setMaskEnd(newMaskEnd);
  //     }
  //   }
  // }, [currentTime, isPlaying, dataTimeRange]);

  // 手动拖拽蒙版后更新播放位置 - 修改为使用蒙版最大时间点
  useEffect(() => {
    if (dataTimeRange) {
      // 根据蒙版最大位置计算对应的时间（蒙版的最大时间点作为currentTime）
      const totalTime = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
      const newTime = new Date(dataTimeRange.start.getTime() + totalTime * maskEnd);

      // 只有当时间差异较大时才更新（避免微小的浮点数差异导致频繁更新）
      if (!currentTime || Math.abs(newTime.getTime() - currentTime.getTime()) > 60000) { // 1分钟差异
        setCurrentTime(newTime);
      }
    }
  }, [maskEnd, dataTimeRange, currentTime, setCurrentTime]);

  // 初始化当前时间和蒙版（只在首次加载时）
  useEffect(() => {
    if (effectivePlaybackRange && (!currentTime || currentTime.getTime() === new Date().getTime())) {
      // 只有当currentTime还是初始值时才设置为开始时间
      setCurrentTime(effectivePlaybackRange.start);
    }
  }, [effectivePlaybackRange, currentTime, setCurrentTime]);

  // 初始化蒙版为最近一周时间范围
  useEffect(() => {
    if (dataTimeRange && maskStart === 0 && maskEnd === 0.1) {
      const totalTime = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
      const oneWeek = 7 * 24 * 60 * 60 * 1000; // 一周的毫秒数
      const weekRatio = Math.min(oneWeek / totalTime, 0.3); // 限制最大为30%，确保不会太大

      // 设置蒙版为最近的七天（从结束时间往前推）
      const newMaskStart = Math.max(0, 1 - weekRatio);
      const newMaskEnd = 1;

      setMaskStart(newMaskStart);
      setMaskEnd(newMaskEnd);

      // 设置当前时间为蒙版的最大时间点（而不是中心位置）
      const maskMaxTime = new Date(
        dataTimeRange.start.getTime() + totalTime * newMaskEnd
      );
      setCurrentTime(maskMaxTime);


    }
  }, [dataTimeRange, maskStart, maskEnd]);

  // 设置时间轴播放模式
  useEffect(() => {
    if (isVisible) {
      setTimelinePlayback(true);
    } else {
      setTimelinePlayback(false);
    }

    return () => {
      setTimelinePlayback(false);
    };
  }, [isVisible, setTimelinePlayback]);

  // 鼠标事件处理
  const handleMouseDown = (e: React.MouseEvent, type: 'mask' | 'start' | 'end') => {
    e.preventDefault();
    setIsDragging(true);
    setDragType(type);
    setDragStartX(e.clientX);
    setDragStartMaskStart(maskStart);
    setDragStartMaskEnd(maskEnd);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !dragType) return;

    const container = document.querySelector('.timeline-container');
    if (!container) return;

    const rect = container.getBoundingClientRect();
    const deltaX = e.clientX - dragStartX;
    const deltaRatio = deltaX / rect.width;

    switch (dragType) {
      case 'mask':
        // 拖动整个蒙版
        const newMaskStart = Math.max(0, Math.min(1 - (dragStartMaskEnd - dragStartMaskStart), dragStartMaskStart + deltaRatio));
        const maskWidth = dragStartMaskEnd - dragStartMaskStart;
        setMaskStart(newMaskStart);
        setMaskEnd(newMaskStart + maskWidth);
        break;
      case 'start':
        // 拖动开始边界
        const newStart = Math.max(0, Math.min(dragStartMaskEnd - 0.01, dragStartMaskStart + deltaRatio));
        setMaskStart(newStart);
        break;
      case 'end':
        // 拖动结束边界
        const newEnd = Math.max(dragStartMaskStart + 0.01, Math.min(1, dragStartMaskEnd + deltaRatio));
        setMaskEnd(newEnd);
        break;
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDragType(null);

    // 延迟重置拖拽状态，确保点击事件不会立即触发
    setTimeout(() => {
      // 重置所有时间轴组件的拖拽状态
      const timelineContainers = document.querySelectorAll('.timeline-container');
      timelineContainers.forEach(container => {
        (container as any)._isDragging = false;
      });
    }, 50);
  };

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragType, dragStartX, dragStartMaskStart, dragStartMaskEnd]);

  const handlePlay = () => {
    const currentTime = Date.now();
    const timeSinceLastClick = currentTime - lastClickTime;

    // 在音频模式下，如果是快速点击（300ms内），则忽略点击，不暂停播放
    if (isAudioEnabled && isPlaying && timeSinceLastClick < CLICK_DEBOUNCE_TIME) {
      return;
    }

    setLastClickTime(currentTime);

    // 在音频模式下，播放按钮只控制指示器的推进，不影响音频播放
    if (isAudioEnabled) {
      setIsPlaying(!isPlaying);
    } else {
      // 普通模式下正常切换播放状态
      setIsPlaying(!isPlaying);
    }
  };

  const handleReset = () => {
    setIsPlaying(false);
    if (dataTimeRange) {
      // 重置蒙版到初始位置
      const totalTime = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
      const oneWeek = 7 * 24 * 60 * 60 * 1000; // 一周的毫秒数
      const weekRatio = Math.min(oneWeek / totalTime, 0.3); // 限制最大为30%，确保不会太大

      const newMaskStart = Math.max(0, 1 - weekRatio);
      const newMaskEnd = 1;

      setMaskStart(newMaskStart);
      setMaskEnd(newMaskEnd);

      // 设置当前时间为蒙版的最大时间点
      const maskMaxTime = new Date(dataTimeRange.start.getTime() + totalTime * newMaskEnd);
      setCurrentTime(maskMaxTime);
    }
  };

  // 显示所有时间范围数据
  const handleShowAllRange = () => {
    setIsPlaying(false);
    if (dataTimeRange) {
      // 设置蒙版为整个时间范围
      setMaskStart(0);
      setMaskEnd(1);

      // 设置当前时间为数据的最大时间点
      setCurrentTime(dataTimeRange.end);
    }
  };

  // 处理时间范围修改
  const handleTimeRangeChange = () => {
    setShowTimeRangePicker(true);
  };

  // 处理时间范围选择器确认
  const handleTimeRangeConfirm = async (start: Date, end: Date) => {
    setTimeRangePickerLoading(true);
    try {
      // 停止播放
      setIsPlaying(false);

      // 更新数据时间范围（重新请求数据）
      await updateDataTimeRange(start, end);

      // 重置时间轴状态到新的时间范围
      const totalTime = end.getTime() - start.getTime();
      const oneWeek = 7 * 24 * 60 * 60 * 1000; // 一周的毫秒数
      const weekRatio = Math.min(oneWeek / totalTime, 0.3); // 限制最大为30%

      // 设置蒙版为最近的七天（从结束时间往前推）
      const newMaskStart = Math.max(0, 1 - weekRatio);
      const newMaskEnd = 1;

      setMaskStart(newMaskStart);
      setMaskEnd(newMaskEnd);

      // 设置当前时间为蒙版的最大时间点
      const maskMaxTime = new Date(start.getTime() + totalTime * newMaskEnd);
      setCurrentTime(maskMaxTime);

      setShowTimeRangePicker(false);

    } catch (error) {
      // 这里可以添加错误提示
    } finally {
      setTimeRangePickerLoading(false);
    }
  };

  // 处理时间范围选择器取消
  const handleTimeRangeCancel = () => {
    setShowTimeRangePicker(false);
    setTimeRangePickerLoading(false);
  };



  // 点击直方图时跳转到指定时间
  const handleHistogramTimeClick = (time: Date) => {
    // 如果正在拖拽，则不处理点击事件
    if (isDragging) {
      return;
    }

    setIsPlaying(false);

    if (dataTimeRange) {
      // 计算点击时间在总时间范围中的位置
      const totalTime = dataTimeRange.end.getTime() - dataTimeRange.start.getTime();
      const clickPosition = (time.getTime() - dataTimeRange.start.getTime()) / totalTime;

      // 保持蒙版宽度不变，以点击位置为中心定位滑块
      const maskWidth = maskEnd - maskStart;
      const halfWidth = maskWidth / 2;

      // 计算新的蒙版位置，以点击位置为中心
      let newMaskStart = clickPosition - halfWidth;
      let newMaskEnd = clickPosition + halfWidth;

      // 边界处理：确保蒙版不超出时间范围
      if (newMaskStart < 0) {
        newMaskStart = 0;
        newMaskEnd = maskWidth;
      } else if (newMaskEnd > 1) {
        newMaskEnd = 1;
        newMaskStart = 1 - maskWidth;
      }

      setMaskStart(newMaskStart);
      setMaskEnd(newMaskEnd);

      // 设置currentTime为滑块中心对应的时间
      const centerPosition = (newMaskStart + newMaskEnd) / 2;
      const centerTime = new Date(dataTimeRange.start.getTime() + totalTime * centerPosition);
      setCurrentTime(centerTime);
    }
  };

  if (!isVisible) return null;

  // 显示加载状态
  if (loading) {
    return (
      <div className="fixed z-50 fade-in bottom-4 left-1/2 transform -translate-x-1/2">
        <div className="floating-panel-unified rounded-lg px-6 py-4">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-slate-600"></div>
            <span className="text-slate-600">
              加载时间轴数据...{retryCount > 0 && ` (重试 ${retryCount}/2)`}
            </span>
          </div>
        </div>
      </div>
    );
  }

  // 显示错误状态或等待数据
  if (error) {
    return (
      <div className="fixed z-50 fade-in bottom-4 left-1/2 transform -translate-x-1/2">
        <div className="floating-panel-unified rounded-lg px-6 py-4">
          <div className="flex items-center justify-between">
            <span className="text-red-600">
              {error}
            </span>
            <button onClick={onClose} className="text-slate-400 hover:text-slate-600 ml-4">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 如果没有数据时间范围，显示等待状态或使用模拟数据
  if (!dataTimeRange) {
    // 如果有地震数据但没有时间范围，可能是数据格式问题
    if (earthquakes && earthquakes.length > 0) {
      return (
        <div className="fixed z-50 fade-in bottom-4 left-1/2 transform -translate-x-1/2">
          <div className="floating-panel-unified rounded-lg px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-yellow-500 rounded-full"></div>
                <span className="text-slate-600">
                  数据格式异常，已获取 {earthquakes.length} 条记录但无法解析时间
                </span>
              </div>
              <button onClick={onClose} className="text-slate-400 hover:text-slate-600 ml-4">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      );
    }

    // 正常的等待状态
    return (
      <div className="fixed z-50 fade-in bottom-4 left-1/2 transform -translate-x-1/2">
        <div className="floating-panel-unified rounded-lg px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-slate-600"></div>
              <span className="text-slate-600">
                等待地震数据加载... (已获取 {earthquakes?.length || 0} 条记录)
              </span>
              <button
                onClick={() => refetch()}
                className="px-2 py-1 text-xs bg-slate-100 hover:bg-slate-200 rounded transition-colors"
                title="手动刷新数据"
              >
                刷新
              </button>
            </div>
            <button onClick={onClose} className="text-slate-400 hover:text-slate-600 ml-4">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 移动端底部面板模式的精简布局
  if (isMobileBottom) {
    return (
      <div className="w-full px-3 py-2">
        {/* 精简的播放控制 */}
        <div className="flex items-center justify-between mb-2">
          {/* 左侧：播放控制 */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handlePlay}
              className="w-10 h-10 rounded bg-blue-500 hover:bg-blue-600 flex items-center justify-center transition-colors touch-manipulation"
              title={
                isAudioEnabled
                  ? (isPlaying ? "暂停指示器推进" : "开始指示器推进")
                  : (isPlaying ? "暂停" : "播放")
              }
            >
              {isPlaying ? (
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
              ) : (
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              )}
            </button>

            {/* 音频控制 */}
            {isAudioSupported && (
              <AudioControls
                earthquakes={earthquakes || []}
                maskStartTime={calculatedMaskTimeRange?.start || null}
                maskEndTime={calculatedMaskTimeRange?.end || null}
                isAudioEnabled={isAudioEnabled}
                onAudioToggle={handleAudioToggle}
                volume={audioVolume}
                onVolumeChange={setAudioVolume}
                onConfigClick={() => setShowAudioConfig(!showAudioConfig)}
                className=""
                isMobile={true}
              />
            )}

            {/* 速度控制 - 移动端使用下拉框 */}
            <div className="relative">
              <select
                value={playbackSpeed}
                onChange={(e) => setPlaybackSpeed(Number(e.target.value))}
                className="appearance-none bg-white border border-slate-200 rounded px-3 py-2 pr-8 text-xs font-medium text-slate-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[70px] min-h-[36px] touch-manipulation"
              >
                <option value={0.1}>0.1x</option>
                <option value={1}>1x</option>
                <option value={5}>5x</option>
                <option value={10}>10x</option>
              </select>
              {/* 自定义下拉箭头 */}
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="w-3 h-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </div>
          </div>

          {/* 右侧：当前时间范围 */}
          <div className="text-xs text-slate-600">
            {calculatedMaskTimeRange && (
              <span>
                {calculatedMaskTimeRange.start.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })} - {calculatedMaskTimeRange.end.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
              </span>
            )}
          </div>
        </div>

        {/* 精简的时间直方图 */}
        <div className="mb-2">
          {effectivePlaybackRange && (
            <TimeHistogram
              data={timeDistribution}
              totalRange={effectivePlaybackRange}
              onTimeClick={handleHistogramTimeClick}
              maskStart={maskStart}
              maskEnd={maskEnd}
              onMaskDrag={handleMouseDown}
              maskTimeRange={calculatedMaskTimeRange}
              isAudioMode={isAudioEnabled}
              isCompact={true}
            />
          )}
        </div>

        {/* 移动端音频配置面板 - 浮动面板 */}
        {showAudioConfig && (
          <div className="fixed bottom-20 left-2 right-2 z-50">
            <AudioConfigPanel
              config={audioConfig}
              onConfigChange={setAudioConfig}
              onClose={() => setShowAudioConfig(false)}
              onPreviewInstrument={previewInstrument}
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`${
      isEmbedded
        ? 'w-full'
        : `fixed z-50 ${isMobile ? 'bottom-2 left-2 right-2' : 'bottom-4 left-1/2 transform -translate-x-1/2'} ${
            isMobile ? 'w-auto' : 'w-[85vw] max-w-3xl'
          } ${isVisible ? 'block' : 'hidden'}`
    }`}>
      <div className={`${
        isEmbedded
          ? 'layer-panel-container px-3 py-2'
          : `layer-panel-container rounded-lg ${isMobile ? 'px-2 py-2' : 'px-3 py-2'}`
      }`}>
        {/* 顶部播放控制 */}
        <div className={`flex items-center justify-between ${isMobile ? 'mb-1' : 'mb-2'}`}>
          {/* 播放控制按钮 - 左侧 */}
          <div className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-1'}`}>
            <button
              onClick={handlePlay}
              className={`${
                isMobile ? 'w-10 h-10' : 'w-7 h-6'
              } rounded bg-blue-500 hover:bg-blue-600 flex items-center justify-center transition-colors touch-manipulation`}
              title={
                isAudioEnabled
                  ? (isPlaying ? "暂停指示器推进" : "开始指示器推进")
                  : (isPlaying ? "暂停" : "播放")
              }
            >
              {isPlaying ? (
                <svg className={`${isMobile ? 'w-5 h-5' : 'w-3 h-3'} text-white`} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
              ) : (
                <svg className={`${isMobile ? 'w-5 h-5' : 'w-3 h-3'} text-white`} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              )}
            </button>

            {/* 播放速度控制 */}
            <div className={`flex items-center ${isMobile ? 'space-x-2 ml-1' : 'space-x-1 ml-2'}`}>
              {!isMobile && <span className="text-xs text-slate-600">速度:</span>}
              <div className={`flex ${isMobile ? 'space-x-1' : 'space-x-1'}`}>
                {(isMobile ? [1, 5] : [0.1, 1, 5, 10]).map(speed => (
                  <button
                    key={speed}
                    onClick={() => setPlaybackSpeed(speed)}
                    className={`${
                      isMobile ? 'px-2 py-1.5 min-w-[36px] min-h-[36px]' : 'px-1.5 py-0.5'
                    } rounded text-xs transition-colors border touch-manipulation ${
                      playbackSpeed === speed
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'text-slate-600 hover:text-slate-800 hover:bg-slate-100 border-slate-200'
                    }`}
                  >
                    {speed}x
                  </button>
                ))}
              </div>
            </div>

            {/* 音频控制 */}
            {isAudioSupported && (
              <div className="ml-3 pl-3 border-l border-slate-200">
                <AudioControls
                  earthquakes={allEarthquakes || []}
                  maskStartTime={calculatedMaskTimeRange?.start || null}
                  maskEndTime={calculatedMaskTimeRange?.end || null}
                  isAudioEnabled={isAudioEnabled}
                  onAudioToggle={handleAudioToggle}
                  volume={audioVolume}
                  onVolumeChange={handleVolumeChange}
                  onConfigClick={() => setShowAudioConfig(true)}
                />
              </div>
            )}
          </div>

          {/* 功能按钮 - 右侧 */}
          <div className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-1'}`}>
            <button
              onClick={handleReset}
              className={`${
                isMobile ? 'w-9 h-9' : 'w-6 h-6'
              } rounded bg-slate-100 hover:bg-slate-200 flex items-center justify-center transition-colors border border-slate-200 touch-manipulation`}
              title="重置到开始"
            >
              <svg className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} text-slate-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
            </button>

            <button
              onClick={handleShowAllRange}
              className={`${
                isMobile ? 'w-9 h-9' : 'w-6 h-6'
              } rounded bg-green-100 hover:bg-green-200 flex items-center justify-center transition-colors border border-green-200 touch-manipulation`}
              title="显示所有时间范围数据"
            >
              <svg className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} text-green-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16l-4-4m0 0l4-4m-4 4h18m-4-4l4 4-4 4"/>
              </svg>
            </button>

            {/* 关闭按钮 - 仅在非嵌入模式显示 */}
            {!isEmbedded && (
              <button
                onClick={onClose}
                className={`${
                  isMobile ? 'w-9 h-9' : 'w-6 h-6'
                } rounded bg-slate-100 hover:bg-slate-200 flex items-center justify-center transition-colors border border-slate-200 touch-manipulation`}
                title="关闭时间轴"
              >
                <svg className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} text-slate-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* 时间分布直方图 */}
        <div className="mb-3">
          {effectivePlaybackRange && (
            <div className="relative">
              {/* 地震事件直方图 - 底层 */}
              <TimeHistogram
                data={timeDistribution}
                totalRange={effectivePlaybackRange}
                onTimeClick={handleHistogramTimeClick}
                maskStart={maskStart}
                maskEnd={maskEnd}
                onMaskDrag={handleMouseDown}
                maskTimeRange={calculatedMaskTimeRange}
                isAudioMode={isAudioEnabled}
              />

              {/* 音频波形可视化器 - 叠加在直方图上方，滑块下方 */}
              {isAudioSupported && isAudioEnabled && (
                <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 3 }}>
                  <AudioWaveformVisualizer
                    isEnabled={isAudioEnabled}
                    isPlaying={isPlaying}
                    height={60}
                    width={600}
                    className="w-full h-full"
                    currentTime={calculatedMaskTimeRange?.end || null}
                  />
                </div>
              )}

              {/* 震源机制解点分布 - 叠加在柱状图上方，滑块下方 */}
              {layerVisibility.focalMechanisms && filteredFocalMechanisms.length > 0 && (
                <div className="absolute inset-0 pointer-events-none" style={{ zIndex: 5 }}>
                  <FocalMechanismTimeDistribution
                    focalMechanisms={filteredFocalMechanisms}
                    totalRange={effectivePlaybackRange}
                    maskStart={maskStart}
                    maskEnd={maskEnd}
                    height={60}
                  />
                </div>
              )}
            </div>
          )}
        </div>

        {/* 底部信息汇总和收起按钮 - 整行可点击收起 */}
        <div
          onClick={onClose}
          className="w-full flex items-center justify-between pt-3 pb-2 border-t border-slate-200 mt-2 text-xs min-h-[2.5rem] hover:bg-slate-50 transition-colors rounded-b-lg cursor-pointer"
          title="点击收起时间轴"
        >
          {/* 左侧：按顺序排列的信息 */}
          <div className="flex items-center space-x-4">
            {/* 1. 数据范围（总计） - 可点击修改 */}
            {dataTimeRange && (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleTimeRangeChange();
                  }}
                  className="flex items-center space-x-1.5 text-slate-600 hover:text-slate-800 transition-colors"
                  title="点击修改数据范围"
                >
                <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                <span>总计 {allEarthquakes?.length || 0} 事件</span>
                <span className="text-slate-500">
                  ({dataTimeRange.start.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })} - {dataTimeRange.end.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })})
                </span>
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
              </button>

              {/* 时间范围选择器 */}
              <TimeRangePicker
                currentRange={dataTimeRange}
                loading={timeRangePickerLoading}
                onConfirm={handleTimeRangeConfirm}
                onCancel={handleTimeRangeCancel}
                isVisible={showTimeRangePicker}
              />
            </div>
            )}

            {/* 2. 当前筛选事件数量（时间范围内的事件） */}
            <div className="flex items-center space-x-1.5 text-slate-700">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="font-medium">当前 {earthquakes?.length || 0} 事件</span>
            </div>



            {/* 3. 当前时间范围 */}
            {calculatedMaskTimeRange && (
              <div className="flex items-center space-x-1.5 text-slate-700">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span className="font-medium">
                  {calculatedMaskTimeRange.start.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })} - {calculatedMaskTimeRange.end.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                </span>
                <span className="text-slate-500">
                  ({Math.round((calculatedMaskTimeRange.end.getTime() - calculatedMaskTimeRange.start.getTime()) / (24 * 60 * 60 * 1000))}天)
                </span>
              </div>
            )}

            {/* 4. 播放状态指示器 */}
            {isPlaying && (
              <div className="flex items-center space-x-1.5 text-green-600">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="font-medium">播放中</span>
              </div>
            )}
          </div>

          {/* 右侧：收起提示 */}
          <div className="flex items-center space-x-1.5 text-slate-500">
            <span>收起</span>
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </div>
        </div>
      </div>

      {/* 音频配置面板 */}
      {showAudioConfig && (
        <div className="absolute bottom-full right-0 mb-2 z-50">
          <AudioConfigPanel
            config={audioConfig}
            onConfigChange={setAudioConfig}
            onClose={() => setShowAudioConfig(false)}
            onPreviewInstrument={previewInstrument}
          />
        </div>
      )}
    </div>
  );
}
