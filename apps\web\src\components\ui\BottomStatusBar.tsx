import React from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';

export function BottomStatusBar() {
  const { viewState } = useMapStore();
  const { layerData } = useEarthquakeData();

  // 计算统计信息
  const totalEarthquakes = layerData?.length || 0;
  const recentEarthquakes = layerData?.filter(eq => {
    const eventTime = new Date(eq.time);
    const now = new Date();
    const daysDiff = (now.getTime() - eventTime.getTime()) / (1000 * 60 * 60 * 24);
    return daysDiff <= 7;
  }).length || 0;

  const formatCoordinate = (value: number, type: 'lat' | 'lng') => {
    const abs = Math.abs(value);
    const direction = type === 'lat' ? (value >= 0 ? 'N' : 'S') : (value >= 0 ? 'E' : 'W');
    return `${abs.toFixed(4)}°${direction}`;
  };

  return (
    <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40">
      <div className="floating-panel-unified rounded-lg px-4 py-2">
        <div className="flex items-center space-x-6 text-xs text-slate-600">
          {/* 坐标信息 */}
          {viewState && (
            <div className="flex items-center space-x-2">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
              </svg>
              <span>
                {formatCoordinate(viewState.latitude, 'lat')}, {formatCoordinate(viewState.longitude, 'lng')}
              </span>
            </div>
          )}

          <div className="w-px h-4 bg-slate-300"></div>

          {/* 缩放级别 */}
          {viewState && (
            <div className="flex items-center space-x-2">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"/>
              </svg>
              <span>缩放: {viewState.zoom.toFixed(1)}</span>
            </div>
          )}

          <div className="w-px h-4 bg-slate-300"></div>

          {/* 数据统计 */}
          <div className="flex items-center space-x-2">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
            </svg>
            <span>总计: {totalEarthquakes} 事件</span>
          </div>

          <div className="w-px h-4 bg-slate-300"></div>

          {/* 最近事件 */}
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>近7天: {recentEarthquakes} 事件</span>
          </div>

          <div className="w-px h-4 bg-slate-300"></div>

          {/* 系统状态 */}
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span>系统正常</span>
          </div>

          <div className="w-px h-4 bg-slate-300"></div>

          {/* 更新时间 */}
          <div className="flex items-center space-x-2">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span>更新: {new Date().toLocaleTimeString('zh-CN', { hour12: false })}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
