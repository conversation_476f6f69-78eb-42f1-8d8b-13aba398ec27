import React, { useState, useEffect } from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { useTimelineState } from '../../hooks/useTimelineState';
import { useIsMobile } from '../../hooks/useIsMobile';
import { TimelinePlayer } from '../timeline/TimelinePlayer';

export function BottomTimelineBar() {
  const [isTimelineExpanded, setIsTimelineExpanded] = useState(true);

  // 移动端检测
  const { isMobile } = useIsMobile();

  // 使用共享的时间轴状态
  const timelineState = useTimelineState();
  const {
    currentTime,
    isPlaying,
    maskTimeRange,
    setIsTimelineVisible,
    isTimelineVisible
  } = timelineState;

  // 计算统计信息
  const { allEarthquakes, earthquakes } = useEarthquakeData();
  const totalEarthquakes = allEarthquakes?.length || 0; // 总数据量
  const currentFilteredEarthquakes = earthquakes?.length || 0; // 当前筛选的数量

  const formatCoordinate = (value: number, type: 'lat' | 'lng') => {
    const abs = Math.abs(value);
    const direction = type === 'lat' ? (value >= 0 ? 'N' : 'S') : (value >= 0 ? 'E' : 'W');
    return `${abs.toFixed(4)}°${direction}`;
  };

  // 移动端显示简单的切换按钮
  if (isMobile) {
    return (
      <div className="fixed bottom-4 right-4 z-40">
        <button
          onClick={() => setIsTimelineVisible(!isTimelineVisible)}
          className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 shadow-lg touch-manipulation ${
            isTimelineVisible
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : 'bg-white hover:bg-slate-50 text-slate-600 border border-slate-200'
          }`}
          title={isTimelineVisible ? "关闭时间轴" : "打开时间轴"}
        >
          {isTimelineVisible ? (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          ) : (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          )}
        </button>
      </div>
    );
  }

  return (
    <>
      {/* 时间轴展开面板 */}
      <TimelinePlayer
        isVisible={isTimelineExpanded}
        onClose={() => {
          setIsTimelineExpanded(false);
          setIsTimelineVisible(false);
        }}
        isEmbedded={false}
      />

      {/* 底部状态栏 - 简化时间轴 */}
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40">
        <div className={isTimelineExpanded ? 'hidden' : 'block'}>
          <button
            onClick={() => {
              setIsTimelineExpanded(true);
              setIsTimelineVisible(true);
            }}
            className="floating-panel-unified rounded-lg px-4 py-2.5 hover:bg-slate-100 hover:bg-opacity-60 transition-all duration-200 cursor-pointer border border-slate-200 border-opacity-40 hover:border-slate-300 hover:border-opacity-80 shadow-lg hover:shadow-xl backdrop-blur-sm"
            title="点击展开时间轴控制器"
          >
            <div className="flex items-center space-x-4 text-sm">
              {/* 当前筛选事件数量 */}
              <div className="flex items-center space-x-1.5 text-slate-700">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="font-medium">{currentFilteredEarthquakes} 事件</span>
              </div>

              <div className="w-px h-4 bg-slate-400 opacity-30"></div>

              {/* 当前时间范围显示 */}
              {maskTimeRange && (
                <div className="flex items-center space-x-1.5 text-slate-600">
                  <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                  <span className="font-medium">
                    {maskTimeRange.start.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })} - {maskTimeRange.end.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                  </span>
                  <span className="text-slate-500 text-xs">
                    ({Math.round((maskTimeRange.end.getTime() - maskTimeRange.start.getTime()) / (24 * 60 * 60 * 1000))}天)
                  </span>
                </div>
              )}

              {/* 播放状态指示器 */}
              {isPlaying && (
                <>
                  <div className="w-px h-4 bg-slate-400 opacity-30"></div>
                  <div className="flex items-center space-x-1.5 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-xs font-medium">播放中</span>
                  </div>
                </>
              )}

              {/* 展开图标 */}
              <div className="flex items-center ml-2">
                <svg className="w-4 h-4 text-slate-500 transition-transform duration-200 hover:text-slate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 15l7-7 7 7"/>
                </svg>
              </div>
            </div>
          </button>
        </div>
      </div>
    </>
  );
}
