import React from 'react';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { useAllLayerData } from '../../hooks/useLayerData';

export function DataStats() {
  const earthquakeData = useEarthquakeData();
  const layerData = useAllLayerData();

  const stats = [
    {
      label: '地震事件',
      value: earthquakeData.earthquakes?.length || 0, // 使用过滤后的数据
      icon: '🔴',
      color: 'text-red-600',
    },
    {
      label: '断层数据',
      value: layerData.faults.faults.length,
      icon: '📏',
      color: 'text-orange-600',
    },
    {
      label: '井轨迹',
      value: layerData.wells.wells.length,
      icon: '🔵',
      color: 'text-blue-600',
    },
    {
      label: '监测台站',
      value: layerData.stations.stations.length,
      icon: '📡',
      color: 'text-green-600',
    },
  ];

  return (
    <div className="space-y-3">
      {stats.map((stat, index) => (
        <div
          key={index}
          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div className="flex items-center space-x-3">
            <span className="text-lg">{stat.icon}</span>
            <span className="font-medium text-gray-800">{stat.label}</span>
          </div>
          <span className={`font-bold text-lg ${stat.color}`}>
            {stat.value.toLocaleString()}
          </span>
        </div>
      ))}

      {/* 震级分布 */}
      {earthquakeData.stats && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h3 className="font-medium text-gray-800 mb-2">震级分布</h3>
          <div className="space-y-1">
            {earthquakeData.stats.magnitudeStats.map((stat, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span className="text-gray-600">{stat.magnitude_range}</span>
                <span className="font-medium">{stat.count}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 断层等级分布 */}
      <div className="p-3 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-800 mb-2">断层等级</h3>
        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">一级断层</span>
            <span className="font-medium text-red-600">
              {layerData.faults.faultsByLevel.level1.length}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">二级断层</span>
            <span className="font-medium text-orange-600">
              {layerData.faults.faultsByLevel.level2.length}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">三级断层</span>
            <span className="font-medium text-yellow-600">
              {layerData.faults.faultsByLevel.level3.length}
            </span>
          </div>
        </div>
      </div>

      {/* 台站状态 */}
      <div className="p-3 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-800 mb-2">台站状态</h3>
        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">活跃</span>
            <span className="font-medium text-green-600">
              {layerData.stations.stationsByStatus.active.length}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">维护中</span>
            <span className="font-medium text-orange-600">
              {layerData.stations.stationsByStatus.maintenance.length}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">非活跃</span>
            <span className="font-medium text-gray-600">
              {layerData.stations.stationsByStatus.inactive.length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
