import { useMap } from 'react-map-gl/maplibre';
import { useMapStore } from '../../stores/useMapStore';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { useAllLayerData } from '../../hooks/useLayerData';
import { useState, useEffect } from 'react';
import { platformApi } from '../../services/api';
import { WellPlatform } from '../../types';
import { getTurboColorFromTime } from '../map/layers/EarthquakeMapLibreLayer';
import { useIsMobile } from '../../hooks/useIsMobile';
import {
  FaultLineIcon,
  WellTrajectoryIcon,
  WellPlatformIcon,
  MonitoringStationIcon
} from '../icons';

export function DetailPanel() {
  const {
    selectedEarthquake,
    selectedFault,
    selectedWell,
    selectedStation,
    hoveredEarthquake,
    hoveredFault,
    hoveredWell,
    hoveredStation,
    isPanelPinned,
    setPanelPinned,
    setSelectedEarthquake,
    setSelectedFault,
    setSelectedWell,
    setSelectedStation,
  } = useMapStore();

  const earthquakeData = useEarthquakeData();
  const layerData = useAllLayerData();
  const mapInstance = useMap();
  const { isMobile } = useIsMobile();
  const [platforms, setPlatforms] = useState<WellPlatform[]>([]);

  // 获取井平台数据
  useEffect(() => {
    const fetchPlatforms = async () => {
      try {
        const response = await platformApi.getPlatforms();
        if (response.success && response.data) {
          setPlatforms(response.data.platforms);
        }
      } catch (error) {
        console.error('获取井平台数据失败:', error);
      }
    };

    fetchPlatforms();
  }, []);

  // 根据震级计算圆点大小的函数（与地图图层保持一致）
  const getRadiusFromMagnitude = (magnitude: number) => {
    const mag = Math.max(magnitude || 0, -1);
    
    if (mag < -0.5) return 3;      // 极小地震
    if (mag < 0) return 4;         // 微震
    if (mag < 0.5) return 5;       // 小微震
    if (mag < 1) return 6;         // 小震
    if (mag < 1.5) return 7;       // 中小震
    if (mag < 2) return 8;         // 中震
    if (mag < 2.5) return 9;       // 较大地震
    if (mag < 3) return 10;        // 大地震
    if (mag < 3.5) return 11;      // 强震
    if (mag < 4) return 12;        // 强震
    return 13;                     // 极强震
  };

  // 计算时间范围用于颜色映射 - 使用时间轴的整体时间范围而不是过滤后的数据范围
  const getTimeRange = () => {
    // 优先使用数据上下文中的整体时间范围
    if (earthquakeData.dataTimeRange) {
      return {
        min: earthquakeData.dataTimeRange.start.getTime(),
        max: earthquakeData.dataTimeRange.end.getTime()
      };
    }

    // 如果没有整体时间范围，则使用所有数据（不是过滤后的数据）计算
    if (earthquakeData.layerData.length === 0) return null;

    const times = earthquakeData.layerData.map(d => new Date(d.time).getTime());
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);

    return { min: minTime, max: maxTime };
  };

  // 确定当前显示的要素
  const getCurrentItem = () => {
    if (isPanelPinned) {
      // 面板固定时显示选中的要素
      if (selectedEarthquake) {
        const earthquake = earthquakeData.layerData.find(e => e.eventId === selectedEarthquake);
        return { item: earthquake, type: 'earthquake' as const };
      }
      if (selectedFault) {
        const fault = layerData.faults.layerData.find(f => f.id === selectedFault);
        return { item: fault, type: 'fault' as const };
      }
      if (selectedWell) {
        // 先在井轨迹中查找
        const trajectory = layerData.wells.wells.find(w => w.id === parseInt(selectedWell));
        if (trajectory) {
          return { item: trajectory, type: 'well-trajectory' as const };
        }
        // 再在井平台中查找
        const platform = platforms.find(p => p.id === parseInt(selectedWell));
        if (platform) {
          return { item: platform, type: 'well-platform' as const };
        }
      }
      if (selectedStation) {
        const station = layerData.stations.layerData.find(s => s.id === selectedStation);
        return { item: station, type: 'station' as const };
      }
    } else if (!isMobile) {
      // 面板未固定时显示悬停的要素（断层除外，断层只在点击时显示）
      // 移动端不显示悬停面板，只显示固定面板
      if (hoveredEarthquake) {
        const earthquake = earthquakeData.layerData.find(e => e.eventId === hoveredEarthquake);
        return { item: earthquake, type: 'earthquake' as const };
      }
      // 注释掉断层的hover显示逻辑，断层只在点击选中时显示
      // if (hoveredFault) {
      //   const fault = layerData.faults.layerData.find(f => f.id === hoveredFault);
      //   return { item: fault, type: 'fault' as const };
      // }
      if (hoveredWell) {
        // 先在井轨迹中查找
        const trajectory = layerData.wells.wells.find(w => w.id === parseInt(hoveredWell));
        if (trajectory) {
          return { item: trajectory, type: 'well-trajectory' as const };
        }
        // 再在井平台中查找
        const platform = platforms.find(p => p.id === parseInt(hoveredWell));
        if (platform) {
          return { item: platform, type: 'well-platform' as const };
        }
      }
      if (hoveredStation) {
        const station = layerData.stations.layerData.find(s => s.id === hoveredStation);
        return { item: station, type: 'station' as const };
      }
    }
    return null;
  };

  const currentData = getCurrentItem();
  const isVisible = currentData && currentData.item;

  const handlePin = () => {
    if (!isPanelPinned && currentData) {
      // 固定面板时，将当前悬停的要素设为选中状态
      switch (currentData.type) {
        case 'earthquake':
          setSelectedEarthquake(hoveredEarthquake);
          break;
        case 'fault':
          setSelectedFault(hoveredFault);
          break;
        case 'well-trajectory':
        case 'well-platform':
          setSelectedWell(hoveredWell);
          break;
        case 'station':
          setSelectedStation(hoveredStation);
          break;
      }
    }
    setPanelPinned(!isPanelPinned);
  };

  const handleZoomTo = () => {
    if (!currentData || !currentData.item) {
      console.warn('DetailPanel: No current data available for zoom');
      return;
    }

    // 尝试获取地图实例
    const map = mapInstance?.current;
    if (!map) {
      console.warn('DetailPanel: Map instance not available');
      return;
    }

    const { item, type } = currentData;
    let coordinates: [number, number] | null = null;

    console.log('DetailPanel: Zooming to', type, item);

    // 计算偏移量，DetailPanel在右侧，不需要向上偏移太多
    // 主要考虑底部时间轴的高度
    const getOffsetCenter = (lng: number, lat: number) => {
      const bounds = map.getBounds();
      const latRange = bounds.getNorth() - bounds.getSouth();

      // DetailPanel的定位功能偏移量较小，主要避开底部时间轴
      let offsetRatio = 0.05; // 默认偏移比例

      if (window.innerWidth < 640) {
        // 移动端：稍微向上偏移
        offsetRatio = 0.08;
      } else if (window.innerWidth < 1024) {
        // 平板端
        offsetRatio = 0.06;
      } else {
        // 桌面端
        offsetRatio = 0.05;
      }

      const latOffset = latRange * offsetRatio;
      return [lng, lat + latOffset];
    };

    // 获取要素的坐标
    try {
      switch (type) {
        case 'earthquake':
          if (item.coordinates && Array.isArray(item.coordinates) && item.coordinates.length >= 2) {
            coordinates = [item.coordinates[0], item.coordinates[1]];
          }
          break;
        case 'fault':
          // 对于线要素，取第一个坐标点
          if (item.coordinates && Array.isArray(item.coordinates) && item.coordinates.length > 0) {
            const firstPoint = item.coordinates[0];
            if (Array.isArray(firstPoint) && firstPoint.length >= 2) {
              coordinates = [firstPoint[0], firstPoint[1]];
            }
          }
          break;
        case 'well-trajectory':
          // 对于井轨迹，解析coordinates字符串并取第一个坐标点
          if (typeof item.coordinates === 'string') {
            try {
              const coords = JSON.parse(item.coordinates);
              if (coords && Array.isArray(coords) && coords.length > 0) {
                // 假设是嵌套数组格式 [[[lng, lat], [lng, lat], ...]]
                if (Array.isArray(coords[0]) && Array.isArray(coords[0][0]) && coords[0][0].length >= 2) {
                  coordinates = [coords[0][0][0], coords[0][0][1]];
                } else if (Array.isArray(coords[0]) && coords[0].length >= 2) {
                  // 直接是坐标数组格式 [[lng, lat], [lng, lat], ...]
                  coordinates = [coords[0][0], coords[0][1]];
                }
              }
            } catch (e) {
              console.error('DetailPanel: Failed to parse well trajectory coordinates:', e);
            }
          }
          break;
        case 'well-platform':
          // 对于井平台，直接使用经纬度
          if (typeof item.longitude === 'number' && typeof item.latitude === 'number') {
            coordinates = [item.longitude, item.latitude];
          }
          break;
        case 'station':
          if (item.coordinates && Array.isArray(item.coordinates) && item.coordinates.length >= 2) {
            coordinates = [item.coordinates[0], item.coordinates[1]];
          }
          break;
      }

      if (coordinates && coordinates.length === 2 && !isNaN(coordinates[0]) && !isNaN(coordinates[1])) {
        console.log('DetailPanel: Flying to coordinates:', coordinates);
        // 缩放到要素位置，使用偏移量
        const [offsetLng, offsetLat] = getOffsetCenter(coordinates[0], coordinates[1]);
        map.flyTo({
          center: [offsetLng, offsetLat],
          zoom: 15, // 适当的缩放级别
          duration: 1000 // 动画持续时间
        });
      } else {
        console.warn('DetailPanel: Invalid coordinates:', coordinates);
      }
    } catch (error) {
      console.error('DetailPanel: Error in handleZoomTo:', error);
    }
  };

  const handleClose = () => {
    setPanelPinned(false);
    setSelectedEarthquake(null);
    setSelectedFault(null);
    setSelectedWell(null);
    setSelectedStation(null);
  };

  const getIcon = () => {
    if (!currentData) return null;
    
    switch (currentData.type) {
      case 'earthquake':
        if (!currentData.item) return null;
        const timeRange = getTimeRange();
        const earthquakeColor = getTurboColorFromTime(currentData.item.time, timeRange);
        
        return (
          <div className="flex items-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full border border-white shadow-sm"
              style={{ backgroundColor: earthquakeColor }}
              title="时间对应的颜色"
            />
          </div>
        );
      case 'fault':
        return (
          <FaultLineIcon className="w-4 h-4 text-orange-600" />
        );
      case 'well-trajectory':
        return (
          <WellTrajectoryIcon className="w-4 h-4 text-blue-600" />
        );
      case 'well-platform':
        return (
          <WellPlatformIcon className="w-4 h-4 text-emerald-600" />
        );
      case 'station':
        if (!currentData.item) return null;
        const stationStatus = currentData.item.status;
        const stationColorClass = stationStatus === 'active' ? 'text-orange-600' : 
                                  stationStatus === 'inactive' ? 'text-gray-400' : 'text-yellow-600';
        
        return (
          <MonitoringStationIcon 
            className={`w-4 h-4 ${stationColorClass}`}
          />
        );
      default:
        return null;
    }
  };

  const getTitle = () => {
    if (!currentData) return '';
    
    switch (currentData.type) {
      case 'earthquake': return '地震事件';
      case 'fault': return '断层';
      case 'well-trajectory': return '井轨迹';
      case 'well-platform': return '井平台';
      case 'station': return '监测台站';
      default: return '';
    }
  };

  const renderContent = () => {
    if (!currentData || !currentData.item) return null;

    const { item, type } = currentData;

    switch (type) {
      case 'earthquake':
        const timeRange = getTimeRange();
        const earthquakeColor = getTurboColorFromTime(item.time, timeRange);
        const earthquakeRadius = getRadiusFromMagnitude(item.magnitude || 0);
        
        return (
          <div className="space-y-2">
            <div className="space-y-3">
              <div>
                <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>震级</label>
                <div className="flex items-center space-x-2">
                  <p className={`${isMobile ? 'text-lg' : 'text-xl'} font-bold text-slate-900`}>
                    M {item.magnitude?.toFixed(1) || 'N/A'}
                  </p>
                </div>
              </div>
              <div>
                <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>深度</label>
                <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{item.depth?.toFixed(1) || 'N/A'} km</p>
              </div>
              <div>
                <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>发生时间</label>
                <div className="flex items-center space-x-2">
                  <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>
                    {item.time ? new Date(item.time).toLocaleString('zh-CN') : 'N/A'}
                  </p>
                </div>
              </div>
              <div>
                <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>位置</label>
                <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>
                  {item.coordinates ? `${item.coordinates[1]?.toFixed(4)}°N, ${item.coordinates[0]?.toFixed(4)}°E` : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        );

      case 'fault':
        return (
          <div className="space-y-2">
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>断层名称</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{item.name || 'N/A'}</p>
            </div>
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>断层等级</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold ${
                item.level === 1 ? 'text-red-600' :
                item.level === 2 ? 'text-orange-600' : 'text-yellow-600'
              }`}>
                {item.level === 1 ? '一级断层' : item.level === 2 ? '二级断层' : '三级断层'}
              </p>
            </div>
          </div>
        );

      case 'well-trajectory':
        // 查找对应的平台名称
        const platform = platforms.find(p => p.id === item.platform_id);
        const platformName = platform ? platform.name : `平台ID: ${item.platform_id}`;

        return (
          <div className="space-y-2">
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>井轨迹名称</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{item.name || 'N/A'}</p>
            </div>
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>所属平台</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{platformName}</p>
            </div>
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>区域</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{item.region || 'N/A'}</p>
            </div>
          </div>
        );

      case 'well-platform':
        return (
          <div className="space-y-2">
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>井平台名称</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{item.name || 'N/A'}</p>
            </div>
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>位置</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>
                {item.latitude && item.longitude ? `${item.latitude.toFixed(4)}°N, ${item.longitude.toFixed(4)}°E` : 'N/A'}
              </p>
            </div>
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>区域</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{item.region || 'N/A'}</p>
            </div>
          </div>
        );

      case 'station':
        return (
          <div className="space-y-2">
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>台站名称</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>{item.name || 'N/A'}</p>
            </div>
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>状态</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold ${
                item.status === 'active' ? 'text-green-600' :
                item.status === 'inactive' ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {item.status === 'active' ? '运行中' :
                 item.status === 'inactive' ? '离线' : '维护中'}
              </p>
            </div>
            <div>
              <label className={`${isMobile ? 'text-xs' : 'text-sm'} font-medium text-slate-500 uppercase tracking-wider`}>位置</label>
              <p className={`${isMobile ? 'text-sm' : 'text-base'} text-slate-700`}>
                {item.coordinates ? `${item.coordinates[1]?.toFixed(4)}°N, ${item.coordinates[0]?.toFixed(4)}°E` : 'N/A'}
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`
      fixed z-40
      ${isMobile
        ? `
          bottom-0 left-0 right-0 max-h-[70vh] overflow-y-auto
          transition-all duration-500 ease-out transform
          ${isVisible ? 'translate-y-0 opacity-100 scale-100' : 'translate-y-full opacity-0 scale-95'}
        `
        : `
          right-20 top-1/2 transform -translate-y-1/2 w-80 transition-all duration-400 ease-out
          ${isVisible ? 'translate-x-0 opacity-100 scale-100' : 'translate-x-full opacity-0 scale-95'}
        `
      }
    `}>
      <div className={`
        bg-white bg-opacity-95 backdrop-blur-sm shadow-xl border border-slate-200
        ${isMobile ? 'rounded-t-lg' : 'rounded-lg'}
      `}>
        {/* 移动端拖拽指示器 */}
        {isMobile && (
          <div className="flex justify-center pt-3 pb-1">
            <div className="w-8 h-1 bg-slate-300 rounded-full transition-colors hover:bg-slate-400"></div>
          </div>
        )}

        {/* Header */}
        <div className={`${isMobile ? 'p-3' : 'p-4'} border-b border-slate-200`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getIcon()}
              <div>
                <h3 className={`${isMobile ? 'text-sm' : 'text-base'} font-semibold text-slate-900`}>
                  {getTitle()}
                </h3>
                <p className={`${isMobile ? 'text-xs' : 'text-sm'} text-slate-500`}>
                  {isPanelPinned ? '已固定' : '悬停显示'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              <button
                onClick={handlePin}
                className={`${isMobile ? 'w-8 h-8' : 'w-9 h-9'} rounded-md flex items-center justify-center transition-colors ${
                  isPanelPinned
                    ? 'text-blue-600 bg-blue-100 hover:bg-blue-200'
                    : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
                }`}
                title={isPanelPinned ? '取消固定' : '固定面板'}
              >
                <svg className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                </svg>
              </button>
              <button
                onClick={handleZoomTo}
                className={`${isMobile ? 'w-8 h-8' : 'w-9 h-9'} rounded-md flex items-center justify-center transition-colors text-slate-400 hover:text-slate-600 hover:bg-slate-100`}
                title="缩放至此位置"
              >
                <svg className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"/>
                </svg>
              </button>
              {isPanelPinned && (
                <button
                  onClick={handleClose}
                  className={`${isMobile ? 'w-8 h-8' : 'w-9 h-9'} rounded-md flex items-center justify-center transition-colors text-slate-400 hover:text-slate-600 hover:bg-slate-100`}
                  title="关闭面板"
                >
                  <svg className={`${isMobile ? 'w-4 h-4' : 'w-5 h-5'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Content - 紧凑布局 */}
        <div className={`${isMobile ? 'p-3 pb-6' : 'p-4'} overflow-y-auto`}>
          <div className="space-y-3">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
