import { useState } from 'react';

interface DualRangeSliderProps {
  value: [number, number];
  onChange: (range: [number, number]) => void;
  min?: number;
  max?: number;
  step?: number;
  showQuickSelect?: boolean;
  className?: string;
  magnitudeStats?: Array<{ magnitude_range: string; count: number }>;
  loading?: boolean;
}

interface QuickSelectOption {
  label: string;
  value: [number, number];
  color: string;
  bgColor: string;
  hoverBgColor: string;
  size: string;
  title: string;
}

export function DualRangeSlider({
  value,
  onChange,
  min = 0,
  max = 6,
  step = 0.1,
  showQuickSelect = true,
  className = "",
  magnitudeStats = [],
  loading = false
}: DualRangeSliderProps) {
  // 防止滑块交叉的处理函数
  const handleValueChange = (index: number, newValue: number) => {
    const newRange = [...value] as [number, number];

    if (index === 0) {
      // 最小值滑块：不能超过最大值
      newRange[0] = Math.min(newValue, value[1]);
    } else {
      // 最大值滑块：不能低于最小值
      newRange[1] = Math.max(newValue, value[0]);
    }

    onChange(newRange);
  };

  // 重置到默认范围
  const handleReset = () => {
    onChange([min, max]);
  };

  // 根据震级范围获取对应的数量
  const getCountForRange = (minMag: number, maxMag: number): number => {
    if (!magnitudeStats || magnitudeStats.length === 0) return 0;

    // 现在magnitudeStats包含与快速选择选项匹配的统计数据
    // 根据范围匹配对应的统计项
    if (minMag === -2 && maxMag === 1) {
      // 小震 (-2-1)
      const stat = magnitudeStats.find(s => s.magnitude_range === '小震 (-2-1)');
      return stat ? stat.count : 0;
    } else if (minMag === 1 && maxMag === 2.5) {
      // 中震 (1-2.5)
      const stat = magnitudeStats.find(s => s.magnitude_range === '中震 (1-2.5)');
      return stat ? stat.count : 0;
    } else if (minMag === 2.5 && maxMag === 6) {
      // 大震 (2.5+)
      const stat = magnitudeStats.find(s => s.magnitude_range === '大震 (2.5+)');
      return stat ? stat.count : 0;
    }

    return 0;
  };

  // 格式化数量显示
  const formatCount = (count: number) => {
    if (loading) return '...';
    if (count === 0) return '0';
    if (count >= 1000) return `${(count / 1000).toFixed(1)}k`;
    return count.toString();
  };

  // 快速选择选项
  const quickSelectOptions: QuickSelectOption[] = [
    {
      label: '小震',
      value: [-2, 1],
      color: '#15803d',
      bgColor: 'rgba(34, 197, 94, 0.1)',
      hoverBgColor: 'rgba(34, 197, 94, 0.15)',
      size: 'w-1.5 h-1.5',
      title: '小震 (-2-1)'
    },
    {
      label: '中震',
      value: [1, 2.5],
      color: '#a16207',
      bgColor: 'rgba(234, 179, 8, 0.1)',
      hoverBgColor: 'rgba(234, 179, 8, 0.15)',
      size: 'w-2.5 h-2.5',
      title: '中震 (1-2.5)'
    },
    {
      label: '大震',
      value: [2.5, 6],
      color: '#dc2626',
      bgColor: 'rgba(239, 68, 68, 0.1)',
      hoverBgColor: 'rgba(239, 68, 68, 0.15)',
      size: 'w-3.5 h-3.5',
      title: '大震 (2.5+)'
    }
  ];

  return (
    <div className={`p-2 rounded-md border transition-colors duration-150 max-sm:p-1.5 ${className}`}
         style={{
           background: 'rgba(255, 255, 255, 0.7)',
           borderColor: 'rgba(71, 85, 105, 0.12)'
         }}>

      {/* 范围显示和圆点图例 */}
      <div className="flex items-center justify-between text-xs text-slate-600 mb-2 max-sm:mb-1">
        <span className="font-medium max-sm:text-xs">{value[0].toFixed(1)} - {value[1].toFixed(1)}</span>
        <div className="flex items-center gap-0.5 max-sm:gap-0">
          <div className="w-1 h-1 bg-slate-400 rounded-full max-sm:w-0.5 max-sm:h-0.5"></div>
          <div className="w-1.5 h-1.5 bg-slate-500 rounded-full max-sm:w-1 max-sm:h-1"></div>
          <div className="w-2 h-2 bg-slate-600 rounded-full max-sm:w-1.5 max-sm:h-1.5"></div>
        </div>
      </div>

      {/* 智能双滑块 - 根据点击位置判断操作哪个滑块 */}
      <div className="relative h-5 mb-2 max-sm:h-4 max-sm:mb-1">
        {/* 背景轨道 */}
        <div className="absolute top-1/2 transform -translate-y-1/2 w-full h-1.5 bg-slate-200 rounded-full max-sm:h-1"></div>

        {/* 选中范围高亮 */}
        <div
          className="absolute top-1/2 transform -translate-y-1/2 h-1.5 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full max-sm:h-1"
          style={{
            left: `${((value[0] - min) / (max - min)) * 100}%`,
            width: `${((value[1] - value[0]) / (max - min)) * 100}%`
          }}
        ></div>

        {/* 智能滑块容器 - 根据点击位置判断操作哪个滑块 */}
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value[0]} // 默认值，会被动态改变
          onChange={(e) => {
            const clickValue = parseFloat(e.target.value);
            const minPos = value[0];
            const maxPos = value[1];
            const midPoint = (minPos + maxPos) / 2;
            
            // 根据点击位置判断操作哪个滑块
            if (clickValue <= midPoint) {
              // 点击位置更接近最小值，操作最小值滑块
              handleValueChange(0, clickValue);
            } else {
              // 点击位置更接近最大值，操作最大值滑块
              handleValueChange(1, clickValue);
            }
          }}
          onMouseDown={(e) => {
            // 在鼠标按下时动态设置滑块值，提供更好的视觉反馈
            const rect = e.currentTarget.getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const clickValue = min + (clickX / rect.width) * (max - min);
            const minPos = value[0];
            const maxPos = value[1];
            const midPoint = (minPos + maxPos) / 2;
            
            // 根据点击位置设置当前滑块值
            if (clickValue <= midPoint) {
              e.currentTarget.value = minPos.toString();
            } else {
              e.currentTarget.value = maxPos.toString();
            }
          }}
          className="absolute top-0 left-0 w-full h-5 opacity-0 cursor-pointer appearance-none"
          style={{
            zIndex: 3,
            pointerEvents: 'auto'
          }}
        />

        {/* 最小值手柄 - 优化拖拽灵敏度 */}
        <div
          className="absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-3.5 h-3.5 bg-white rounded-full shadow-md border-2 border-blue-500 cursor-pointer hover:scale-110 max-sm:w-3 max-sm:h-3"
          style={{
            left: `${((value[0] - min) / (max - min)) * 100}%`,
            zIndex: 6
          }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const container = e.currentTarget.closest('.relative') as HTMLElement;
            const containerRect = container.getBoundingClientRect();
            let isDragging = true;
            
            // 添加拖拽状态类名而不是直接修改transform
            e.currentTarget.classList.add('scale-110', 'shadow-lg');
            e.currentTarget.classList.remove('hover:scale-110');
            document.body.style.cursor = 'grabbing';
            document.body.style.userSelect = 'none';
            
            const handleMouseMove = (moveEvent: MouseEvent) => {
              if (!isDragging) return;
              
              // 使用当前鼠标位置相对于容器的百分比
              const mouseX = moveEvent.clientX - containerRect.left;
              const percentage = Math.max(0, Math.min(1, mouseX / containerRect.width));
              const newValue = min + percentage * (max - min);
              
              // 添加小幅度的减振，让拖拽更平滑
              const roundedValue = Math.round(newValue * (1/step)) / (1/step);
              handleValueChange(0, roundedValue);
            };
            
            const handleMouseUp = () => {
              isDragging = false;
              
              // 恢复类名而不是直接修改样式
              e.currentTarget.classList.remove('scale-110', 'shadow-lg');
              e.currentTarget.classList.add('hover:scale-110');
              document.body.style.cursor = '';
              document.body.style.userSelect = '';
              
              document.removeEventListener('mousemove', handleMouseMove);
              document.removeEventListener('mouseup', handleMouseUp);
            };
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
          }}
        >
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-blue-500 rounded-full pointer-events-none"></div>
        </div>

        {/* 最大值手柄 - 优化拖拽灵敏度 */}
        <div
          className="absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-3.5 h-3.5 bg-white rounded-full shadow-md border-2 border-blue-500 cursor-pointer hover:scale-110 max-sm:w-3 max-sm:h-3"
          style={{
            left: `${((value[1] - min) / (max - min)) * 100}%`,
            zIndex: 6
          }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
            
            const container = e.currentTarget.closest('.relative') as HTMLElement;
            const containerRect = container.getBoundingClientRect();
            let isDragging = true;
            
            // 添加拖拽状态类名而不是直接修改transform
            e.currentTarget.classList.add('scale-110', 'shadow-lg');
            e.currentTarget.classList.remove('hover:scale-110');
            document.body.style.cursor = 'grabbing';
            document.body.style.userSelect = 'none';
            
            const handleMouseMove = (moveEvent: MouseEvent) => {
              if (!isDragging) return;
              
              // 使用当前鼠标位置相对于容器的百分比
              const mouseX = moveEvent.clientX - containerRect.left;
              const percentage = Math.max(0, Math.min(1, mouseX / containerRect.width));
              const newValue = min + percentage * (max - min);
              
              // 添加小幅度的减振，让拖拽更平滑
              const roundedValue = Math.round(newValue * (1/step)) / (1/step);
              handleValueChange(1, roundedValue);
            };
            
            const handleMouseUp = () => {
              isDragging = false;
              
              // 恢复类名而不是直接修改样式
              e.currentTarget.classList.remove('scale-110', 'shadow-lg');
              e.currentTarget.classList.add('hover:scale-110');
              document.body.style.cursor = '';
              document.body.style.userSelect = '';
              
              document.removeEventListener('mousemove', handleMouseMove);
              document.removeEventListener('mouseup', handleMouseUp);
            };
            
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
          }}
        >
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1.5 h-1.5 bg-blue-500 rounded-full pointer-events-none"></div>
        </div>
      </div>

      {/* 简化刻度 */}
      <div className="flex justify-between text-xs text-slate-400 mb-2 max-sm:text-xs max-sm:mb-1">
        <span>{min}</span>
        <span className="max-sm:hidden">{Math.round(max / 3)}</span>
        <span className="max-sm:hidden">{Math.round(max * 2 / 3)}</span>
        <span>{max}</span>
      </div>

      {/* 快速选择和重置按钮 */}
      {showQuickSelect && (
        <div className="flex gap-1">
          {/* 重置按钮 - 小圆点到大圆点的图例示意 */}
          <button
            onClick={handleReset}
            className="flex items-center justify-center px-2 py-1.5 rounded transition-colors duration-150 hover:bg-slate-100"
            style={{
              background: 'rgba(148, 163, 184, 0.1)',
              color: '#64748b'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(148, 163, 184, 0.15)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(148, 163, 184, 0.1)';
            }}
            title="重置震级范围 (-2-6)"
          >
            <div className="flex items-center gap-0.5">
              <div className="w-1 h-1 bg-current rounded-full"></div>
              <div className="w-0.5 h-px bg-current"></div>
              <div className="w-1.5 h-1.5 bg-current rounded-full"></div>
              <div className="w-0.5 h-px bg-current"></div>
              <div className="w-2 h-2 bg-current rounded-full"></div>
            </div>
          </button>

          {/* 圆点图标快速选择 */}
          {quickSelectOptions.map((option, index) => {
            const count = getCountForRange(option.value[0], option.value[1]);
            return (
              <button
                key={index}
                onClick={() => onChange(option.value as [number, number])}
                className="flex-1 flex flex-col items-center justify-center px-1.5 py-1 rounded transition-colors duration-150"
                style={{
                  background: option.bgColor,
                  color: option.color
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = option.hoverBgColor;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = option.bgColor;
                }}
                title={`${option.title} - ${formatCount(count)}条记录`}
              >
                <div className={`${option.size} bg-current rounded-full mb-0.5`}></div>
                <span className="text-xs font-medium leading-none">
                  {formatCount(count)}
                </span>
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
} 