import React from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useLayerData } from '../../contexts/LayerDataContext';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { LoadingSpinner } from './LoadingSpinner';
import { 
  EarthquakeIcon, 
  FaultLineIcon, 
  WellTrajectoryIcon, 
  WellPlatformIcon, 
  MonitoringStationIcon 
} from '../icons';

interface FloatingLayerIconsProps {
  onShowLayerTable?: (layerKey: string) => void;
  showDataTable?: boolean;
  showLayerTable?: string | null;
}

export function FloatingLayerIcons({ onShowLayerTable, showDataTable, showLayerTable }: FloatingLayerIconsProps = {}) {
  const { layerVisibility, toggleLayer } = useMapStore();
  const layerData = useLayerData();
  const { loading: earthquakeLoading } = useEarthquakeData();

  const layerConfig = [
    {
      key: 'earthquakes' as const,
      label: '地震事件',
      icon: EarthquakeIcon,
      color: 'text-red-600',
      inactiveColor: 'text-red-400',
      hoverColor: 'text-red-500',
      activeColor: 'bg-red-50 border-red-400',
      activeStyle: 'bg-red-50/80 border-red-400/70',
      getLoadingState: () => earthquakeLoading,
    },
    {
      key: 'faults' as const,
      label: '断层数据',
      icon: FaultLineIcon,
      color: 'text-orange-600',
      inactiveColor: 'text-orange-400',
      hoverColor: 'text-orange-500',
      activeColor: 'bg-orange-50 border-orange-400',
      activeStyle: 'bg-orange-50/80 border-orange-400/70',
      getLoadingState: (layerData: any) => layerData.faultsLoading,
    },
    {
      key: 'wellTrajectories' as const,
      label: '井轨迹',
      icon: WellTrajectoryIcon,
      color: 'text-blue-600',
      inactiveColor: 'text-blue-400',
      hoverColor: 'text-blue-500',
      activeColor: 'bg-blue-50 border-blue-400',
      activeStyle: 'bg-blue-50/80 border-blue-400/70',
      getLoadingState: (layerData: any) => layerData.wellsLoading,
    },
    {
      key: 'wellPlatforms' as const,
      label: '井平台',
      icon: WellPlatformIcon,
      color: 'text-emerald-600',
      inactiveColor: 'text-emerald-400',
      hoverColor: 'text-emerald-500',
      activeColor: 'bg-emerald-50 border-emerald-400',
      activeStyle: 'bg-emerald-50/80 border-emerald-400/70',
      getLoadingState: (layerData: any) => layerData.platformsLoading,
    },
    {
      key: 'stations' as const,
      label: '监测台站',
      icon: MonitoringStationIcon,
      color: 'text-orange-600',
      inactiveColor: 'text-orange-400',
      hoverColor: 'text-orange-500',
      activeColor: 'bg-orange-50 border-orange-400',
      activeStyle: 'bg-orange-50/80 border-orange-400/70',
      getLoadingState: (layerData: any) => layerData.stationsLoading,
    },
  ];

  return (
    <div className="fixed left-4 top-28 z-50 space-y-3">
      {layerConfig.map((layer) => {
        const isLoading = layer.getLoadingState(layerData);
        const isTableActive = (layer.key === 'earthquakes' && showDataTable) ||
                              (layer.key !== 'earthquakes' && showLayerTable === layer.key);
        const isLayerVisible = layerVisibility[layer.key];
        const IconComponent = layer.icon;

        return (
          <div
            key={layer.key}
            className="group relative"
          >
            {/* 主图标按钮 */}
            <button
              onClick={() => toggleLayer(layer.key)}
              disabled={isLoading}
              className={`
                relative w-12 h-12 rounded-xl border-2 shadow-lg backdrop-blur-sm
                flex items-center justify-center
                transition-all duration-200 ease-in-out
                hover:shadow-xl hover:scale-105 active:scale-95
                ${isLayerVisible 
                  ? `${layer.activeStyle} ${layer.color} shadow-xl` 
                  : `bg-white/90 border-gray-200/60 ${layer.inactiveColor} hover:border-gray-300 hover:bg-white`
                }
                ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
              title={`${isLayerVisible ? '隐藏' : '显示'} ${layer.label}`}
            >
              {isLoading ? (
                <LoadingSpinner size="sm" color="slate" className="w-6 h-6" />
              ) : (
                <IconComponent className="w-6 h-6" />
              )}
            </button>

            {/* 数据表格按钮 - 小图标 */}
            <button
              onClick={() => onShowLayerTable?.(layer.key)}
              className={`
                absolute -top-0.5 -right-0.5 w-5 h-5 rounded-full
                flex items-center justify-center
                transition-all duration-200 ease-in-out
                shadow-md border-2 border-white backdrop-blur-sm
                hover:scale-110 active:scale-95
                ${isTableActive 
                  ? 'bg-blue-500 text-white hover:bg-blue-600' 
                  : 'bg-gray-100/90 text-gray-500 hover:bg-gray-200/90 hover:text-gray-700'
                }
                ${isLayerVisible ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
              `}
              title={`${isTableActive ? '关闭' : '查看'}${layer.label}表格`}
            >
              <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7h18M3 12h18M3 17h18"/>
              </svg>
            </button>

            {/* 图层名称提示 - 鼠标悬停时显示 */}
            <div className="absolute left-16 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
              <div className="bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-lg whitespace-nowrap shadow-lg">
                {layer.label}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
} 