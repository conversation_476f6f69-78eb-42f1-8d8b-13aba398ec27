import React from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useLayerData } from '../../contexts/LayerDataContext';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { LoadingSpinner } from './LoadingSpinner';
import { 
  EarthquakeIcon, 
  FaultLineIcon, 
  WellTrajectoryIcon, 
  WellPlatformIcon, 
  MonitoringStationIcon 
} from '../icons';

interface FloatingLayerIconsProps {
  onShowLayerTable?: (layerKey: string) => void;
  showDataTable?: boolean;
  showLayerTable?: string | null;
}

export function FloatingLayerIcons({ onShowLayerTable, showDataTable, showLayerTable }: FloatingLayerIconsProps = {}) {
  const { layerVisibility, toggleLayer, administrativeSubLayers, toggleAdministrativeSubLayer, administrativeLoading } = useMapStore();
  const [showAdministrativePanel, setShowAdministrativePanel] = React.useState(false);
  const layerData = useLayerData();
  const { loading: earthquakeLoading } = useEarthquakeData();

  const layerConfig = [
    {
      key: 'earthquakes' as const,
      label: '地震事件',
      icon: EarthquakeIcon,
      color: 'text-red-600',
      inactiveColor: 'text-red-400',
      hoverColor: 'text-red-500',
      activeColor: 'bg-red-50 border-red-400',
      activeStyle: 'bg-red-50/80 border-red-400/70',
      getLoadingState: () => earthquakeLoading,
    },
    {
      key: 'faults' as const,
      label: '断层数据',
      icon: FaultLineIcon,
      color: 'text-orange-600',
      inactiveColor: 'text-orange-400',
      hoverColor: 'text-orange-500',
      activeColor: 'bg-orange-50 border-orange-400',
      activeStyle: 'bg-orange-50/80 border-orange-400/70',
      getLoadingState: (layerData: any) => layerData.faultsLoading,
    },
    {
      key: 'wellTrajectories' as const,
      label: '井轨迹',
      icon: WellTrajectoryIcon,
      color: 'text-blue-600',
      inactiveColor: 'text-blue-400',
      hoverColor: 'text-blue-500',
      activeColor: 'bg-blue-50 border-blue-400',
      activeStyle: 'bg-blue-50/80 border-blue-400/70',
      getLoadingState: (layerData: any) => layerData.wellsLoading,
    },
    {
      key: 'wellPlatforms' as const,
      label: '井平台',
      icon: WellPlatformIcon,
      color: 'text-emerald-600',
      inactiveColor: 'text-emerald-400',
      hoverColor: 'text-emerald-500',
      activeColor: 'bg-emerald-50 border-emerald-400',
      activeStyle: 'bg-emerald-50/80 border-emerald-400/70',
      getLoadingState: (layerData: any) => layerData.platformsLoading,
    },
    {
      key: 'stations' as const,
      label: '监测台站',
      icon: MonitoringStationIcon,
      color: 'text-orange-600',
      inactiveColor: 'text-orange-400',
      hoverColor: 'text-orange-500',
      activeColor: 'bg-orange-50 border-orange-400',
      activeStyle: 'bg-orange-50/80 border-orange-400/70',
      getLoadingState: (layerData: any) => layerData.stationsLoading,
    },
    {
      key: 'administrative' as const,
      label: '行政区划',
      icon: ({ className }: { className?: string }) => (
        <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
        </svg>
      ),
      color: 'text-slate-600',
      inactiveColor: 'text-slate-400',
      hoverColor: 'text-slate-500',
      activeColor: 'bg-slate-50 border-slate-400',
      activeStyle: 'bg-slate-50/80 border-slate-400/70',
      getLoadingState: () => administrativeLoading,
    },
  ];

  return (
    <div className="fixed left-4 top-28 z-50 space-y-3">
      {layerConfig.map((layer) => {
        const isLoading = layer.getLoadingState(layerData);
        const isTableActive = (layer.key === 'earthquakes' && showDataTable) ||
                              (layer.key !== 'earthquakes' && showLayerTable === layer.key);
        const isLayerVisible = layerVisibility[layer.key];
        const IconComponent = layer.icon;

        return (
          <div
            key={layer.key}
            className="group relative"
          >
            {/* 主图标按钮 */}
            <button
              onClick={() => toggleLayer(layer.key)}
              disabled={isLoading}
              className={`
                relative w-12 h-12 rounded-xl border-2 shadow-lg backdrop-blur-sm
                flex items-center justify-center
                transition-all duration-200 ease-in-out
                hover:shadow-xl hover:scale-105 active:scale-95
                ${isLayerVisible 
                  ? `${layer.activeStyle} ${layer.color} shadow-xl` 
                  : `bg-white/90 border-gray-200/60 ${layer.inactiveColor} hover:border-gray-300 hover:bg-white`
                }
                ${isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
              title={`${isLayerVisible ? '隐藏' : '显示'} ${layer.label}`}
            >
              {isLoading ? (
                <LoadingSpinner size="sm" color="slate" className="w-6 h-6" />
              ) : (
                <IconComponent className="w-6 h-6" />
              )}
            </button>

            {/* 数据表格按钮或设置按钮 - 小图标 */}
            {layer.key === 'administrative' ? (
              <button
                onClick={() => setShowAdministrativePanel(!showAdministrativePanel)}
                className={`
                  absolute -top-0.5 -right-0.5 w-5 h-5 rounded-full
                  flex items-center justify-center
                  transition-all duration-200 ease-in-out
                  shadow-md border-2 border-white backdrop-blur-sm
                  hover:scale-110 active:scale-95
                  ${showAdministrativePanel 
                    ? 'bg-blue-500 text-white hover:bg-blue-600' 
                    : 'bg-gray-100/90 text-gray-500 hover:bg-gray-200/90 hover:text-gray-700'
                  }
                  ${isLayerVisible ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
                `}
                title={`${showAdministrativePanel ? '关闭' : '打开'}行政区划设置`}
              >
                <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
              </button>
            ) : (
              <button
                onClick={() => onShowLayerTable?.(layer.key)}
                className={`
                  absolute -top-0.5 -right-0.5 w-5 h-5 rounded-full
                  flex items-center justify-center
                  transition-all duration-200 ease-in-out
                  shadow-md border-2 border-white backdrop-blur-sm
                  hover:scale-110 active:scale-95
                  ${isTableActive 
                    ? 'bg-blue-500 text-white hover:bg-blue-600' 
                    : 'bg-gray-100/90 text-gray-500 hover:bg-gray-200/90 hover:text-gray-700'
                  }
                  ${isLayerVisible ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'}
                `}
                title={`${isTableActive ? '关闭' : '查看'}${layer.label}表格`}
              >
                <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7h18M3 12h18M3 17h18"/>
                </svg>
              </button>
            )}

            {/* 图层名称提示 - 鼠标悬停时显示 */}
            <div className="absolute left-16 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50">
              <div className="bg-black/80 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-lg whitespace-nowrap shadow-lg">
                {layer.label}
              </div>
            </div>

            {/* 行政区划子图层控制面板 - 与行政区划按钮对齐 */}
            {layer.key === 'administrative' && showAdministrativePanel && layerVisibility.administrative && (
              <div className="absolute left-16 top-0 w-48 bg-white/95 backdrop-blur-sm border border-gray-200/60 rounded-xl p-4 shadow-xl z-50">
                <div className="text-sm font-medium text-gray-700 mb-3">行政区划子图层</div>
                <div className="space-y-3">
                  {/* 县级边界 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                      </svg>
                      <span className="text-xs text-gray-700">县级边界</span>
                    </div>
                    <button
                      onClick={() => toggleAdministrativeSubLayer('countyBoundaries')}
                      className={`
                        w-8 h-4 rounded-full transition-colors duration-200 ease-in-out
                        ${administrativeSubLayers.countyBoundaries ? 'bg-blue-500' : 'bg-gray-300'}
                      `}
                    >
                      <div className={`
                        w-3 h-3 bg-white rounded-full shadow-md transform transition-transform duration-200 ease-in-out
                        ${administrativeSubLayers.countyBoundaries ? 'translate-x-4' : 'translate-x-0.5'}
                      `} />
                    </button>
                  </div>

                  {/* 乡镇边界 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"/>
                      </svg>
                      <span className="text-xs text-gray-700">乡镇边界</span>
                    </div>
                    <button
                      onClick={() => toggleAdministrativeSubLayer('townshipBoundaries')}
                      className={`
                        w-8 h-4 rounded-full transition-colors duration-200 ease-in-out
                        ${administrativeSubLayers.townshipBoundaries ? 'bg-blue-500' : 'bg-gray-300'}
                      `}
                    >
                      <div className={`
                        w-3 h-3 bg-white rounded-full shadow-md transform transition-transform duration-200 ease-in-out
                        ${administrativeSubLayers.townshipBoundaries ? 'translate-x-4' : 'translate-x-0.5'}
                      `} />
                    </button>
                  </div>

                  {/* 乡镇点位 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <svg className="w-3 h-3 text-emerald-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                      </svg>
                      <span className="text-xs text-gray-700">乡镇点位</span>
                    </div>
                    <button
                      onClick={() => toggleAdministrativeSubLayer('townshipPoints')}
                      className={`
                        w-8 h-4 rounded-full transition-colors duration-200 ease-in-out
                        ${administrativeSubLayers.townshipPoints ? 'bg-blue-500' : 'bg-gray-300'}
                      `}
                    >
                      <div className={`
                        w-3 h-3 bg-white rounded-full shadow-md transform transition-transform duration-200 ease-in-out
                        ${administrativeSubLayers.townshipPoints ? 'translate-x-4' : 'translate-x-0.5'}
                      `} />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      })}
      

    </div>
  );
} 