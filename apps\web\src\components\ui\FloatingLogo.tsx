import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Logo } from './Logo';

export function FloatingLogo() {
  const navigate = useNavigate();
  const [isHovered, setIsHovered] = useState(false);

  const handleLogoClick = () => {
    navigate('/');
  };

  return (
    <div className="fixed top-4 left-4 z-50">
      <div
        className="group cursor-pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleLogoClick}
      >
        <div className={`relative transition-all duration-300 ease-out ${
          isHovered
            ? 'bg-white rounded-md p-2 shadow-lg'
            : 'bg-white rounded-md p-2 shadow-sm'
        }`}>

          <div className="relative flex items-center">
            {/* 简洁Logo图标 */}
            <div className="relative flex-shrink-0">
              <div className={`transition-all duration-200 ${isHovered ? 'scale-105' : 'scale-100'}`}>
                <div className={`w-8 h-8 rounded-md flex items-center justify-center transition-all duration-200`}>
                  <Logo size="md" />
                </div>
              </div>

              {/* 状态点 - 始终显示 */}
              <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-white shadow-sm">
                <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-60"></div>
              </div>
            </div>

            {/* 展开文字 */}
            <div className={`transition-all duration-300 overflow-hidden ${
              isHovered
                ? 'ml-2.5 max-w-32 opacity-100 translate-x-0'
                : 'max-w-0 opacity-0 translate-x-[-10px]'
            }`}>
              <div className="whitespace-nowrap">
                <div className="text-sm font-semibold text-slate-800">
                  ZJU-RISEMAP
                </div>
                <div className="text-xs text-slate-600 -mt-0.5">
                  地震监测平台
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
