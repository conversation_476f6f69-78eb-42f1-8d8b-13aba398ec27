import React from 'react';
import { Logo } from './Logo';

export function Footer() {
  return (
    <footer className="bg-slate-900 text-white py-8 sm:py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-6 sm:mb-8">
          <div className="sm:col-span-2 lg:col-span-1">
            <div className="flex items-center mb-4">
              <Logo size="md" variant="light" />
              <span className="ml-2 font-semibold">ZJU-RISEMAP</span>
            </div>
            <p className="text-slate-400 text-sm leading-relaxed">
              由浙江大学地震研究团队自主研发并持续优化，集成实时监测、智能分析和前兆识别功能，为地震风险评估与科学管控提供专业支持。
            </p>
          </div>

          <div>
            <h4 className="font-semibold mb-3 sm:mb-4">研究工具</h4>
            <ul className="space-y-2 text-sm text-slate-400">
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">数据可视化</a></li>
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">时间序列分析</a></li>
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">数据导出工具</a></li>
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">API文档</a></li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold mb-3 sm:mb-4">学术资源</h4>
            <ul className="space-y-2 text-sm text-slate-400">
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">使用指南</a></li>
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">研究论文</a></li>
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">数据集信息</a></li>
              <li><a href="#" className="hover:text-white transition-colors touch-manipulation">联系支持</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-slate-800 pt-6 sm:pt-8 text-center">
          <p className="text-slate-400 text-sm mb-2">
            © 2025 RiseMap 研究平台。保留所有权利。
          </p>
          <p className="text-slate-500 text-xs">
            专为学术和科学研究目的开发
          </p>
        </div>
      </div>
    </footer>
  );
}
