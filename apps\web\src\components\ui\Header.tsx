import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/useAuthStore';

export function Header() {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16 sm:h-20">
          {/* Logo & Brand */}
          <div className="flex items-center">
            <img src="/logo2.svg" alt="ZJU-RISEMAP" className="h-12 sm:h-18" />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <button
              onClick={() => navigate('/')}
              className="px-3 py-2 text-sm font-medium text-slate-700 border-b-2 border-slate-700"
            >
              首页
            </button>
            <button
              onClick={() => navigate('/map')}
              className="px-3 py-2 text-sm font-medium text-slate-500 hover:text-slate-700 transition-colors"
            >
              地图
            </button>
          </nav>

          {/* Desktop User Menu */}
          <div className="hidden md:flex items-center">
            {user ? (
              <div className="flex items-center space-x-4">
                <span className="text-sm text-slate-600 hidden lg:inline">欢迎, {user.email}</span>
                <button
                  onClick={logout}
                  className="bg-slate-700 text-white px-4 py-2 rounded text-sm font-medium hover:bg-slate-800 transition-colors"
                >
                  退出登录
                </button>
              </div>
            ) : (
              <button
                onClick={() => navigate('/login')}
                className="bg-slate-700 text-white px-4 py-2 rounded text-sm font-medium hover:bg-slate-800 transition-colors"
              >
                登录
              </button>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-slate-400 hover:text-slate-500 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-slate-500"
              aria-expanded="false"
            >
              <span className="sr-only">打开主菜单</span>
              {!isMobileMenuOpen ? (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              ) : (
                <svg className="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
            <button
              onClick={() => {
                navigate('/');
                setIsMobileMenuOpen(false);
              }}
              className="block px-3 py-2 text-base font-medium text-slate-700 hover:text-slate-900 hover:bg-slate-50 rounded-md w-full text-left"
            >
              首页
            </button>
            <button
              onClick={() => {
                navigate('/map');
                setIsMobileMenuOpen(false);
              }}
              className="block px-3 py-2 text-base font-medium text-slate-500 hover:text-slate-700 hover:bg-slate-50 rounded-md w-full text-left"
            >
              地图
            </button>

            {/* Mobile User Menu */}
            <div className="border-t border-gray-200 pt-4">
              {user ? (
                <div className="space-y-2">
                  <div className="px-3 py-2 text-sm text-slate-600">
                    欢迎, {user.email}
                  </div>
                  <button
                    onClick={() => {
                      logout();
                      setIsMobileMenuOpen(false);
                    }}
                    className="block w-full text-left px-3 py-2 text-base font-medium text-white bg-slate-700 hover:bg-slate-800 rounded-md"
                  >
                    退出登录
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => {
                    navigate('/login');
                    setIsMobileMenuOpen(false);
                  }}
                  className="block w-full text-left px-3 py-2 text-base font-medium text-white bg-slate-700 hover:bg-slate-800 rounded-md"
                >
                  登录
                </button>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
