import React from 'react';

interface LeftControlPanelProps {
  onToggleSidebar: () => void;
  showSidebar: boolean;
}

export function LeftControlPanel({
  onToggleSidebar,
  showSidebar
}: LeftControlPanelProps) {
  return (
    <div className={`fixed z-50 transition-all duration-300 ease-in-out ${
      showSidebar ? 'opacity-0 pointer-events-none' : 'opacity-100'
    }
      left-4 top-20`}>
      {/* 图层管理按钮 - 只在侧边栏关闭时显示 */}
      <div
        className="bg-white rounded-md p-2 shadow-sm hover:bg-slate-200 transition-colors cursor-pointer"
        onClick={onToggleSidebar}
      >
        <button
          className="w-8 h-8 rounded-md flex items-center justify-center group"
          title="图层管理 (L)"
        >
          <svg className="w-4 h-4 text-slate-600 group-hover:text-slate-800 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
          </svg>
        </button>
      </div>
    </div>
  );
}
