import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  variant?: 'default' | 'light';
}

export function Logo({ size = 'md', className = '', variant = 'default' }: LogoProps) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={`${sizeClasses[size]} ${className}`}>
      <img
        src="/logo.svg"
        alt="RiseMap Logo"
        className="w-full h-full drop-shadow-lg"
      />
    </div>
  );
}
