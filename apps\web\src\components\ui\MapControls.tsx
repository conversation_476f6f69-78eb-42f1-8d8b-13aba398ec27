import { useCallback } from 'react';
import { useMap } from 'react-map-gl/maplibre';
import { useMapStore } from '../../stores/useMapStore';
import { useIsMobile } from '../../hooks/useIsMobile';

export function MapControls() {
  const { current: map } = useMap();
  const { initialViewState } = useMapStore();
  const { isMobile } = useIsMobile();

  // 如果地图实例未定义，不渲染组件
  if (!map) {
    return null;
  }

  const handleZoomIn = useCallback(() => {
    if (!map) return;
    map.zoomIn();
  }, [map]);

  const handleZoomOut = useCallback(() => {
    if (!map) return;
    map.zoomOut();
  }, [map]);

  const handleResetView = useCallback(() => {
    if (!map) return;
    map.flyTo({
      center: [initialViewState.longitude, initialViewState.latitude],
      zoom: initialViewState.zoom,
      pitch: initialViewState.pitch,
      bearing: initialViewState.bearing,
      duration: 1000
    });
  }, [map, initialViewState]);

  const handleToggle3D = useCallback(() => {
    if (!map) return;
    const currentPitch = map.getPitch();
    map.flyTo({
      pitch: currentPitch > 0 ? 0 : 45,
      duration: 500
    });
  }, [map]);

  return (
    <div className={`fixed z-50 fade-in ${
      isMobile
        ? 'right-2 bottom-20'
        : 'right-4 top-24 sm:right-6'
    }`}>
      {/* 地图控制面板 - 竖排布局 */}
      <div className={`floating-panel-unified rounded-lg space-y-1 ${
        isMobile ? 'p-1' : 'p-2'
      }`}>
        <button
          onClick={handleResetView}
          className={`rounded-md hover:bg-slate-200 flex items-center justify-center transition-colors ${
            isMobile ? 'w-6 h-6' : 'w-8 h-8'
          }`}
          title="重置视角"
        >
          <svg className={`text-slate-600 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
          </svg>
        </button>
        <button
          onClick={handleZoomIn}
          className={`rounded-md hover:bg-slate-200 flex items-center justify-center transition-colors ${
            isMobile ? 'w-6 h-6' : 'w-8 h-8'
          }`}
          title="放大"
        >
          <svg className={`text-slate-600 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
        </button>
        <button
          onClick={handleZoomOut}
          className={`rounded-md hover:bg-slate-200 flex items-center justify-center transition-colors ${
            isMobile ? 'w-6 h-6' : 'w-8 h-8'
          }`}
          title="缩小"
        >
          <svg className={`text-slate-600 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 12H4"/>
          </svg>
        </button>
        <div className={`w-full bg-slate-300 ${isMobile ? 'h-px my-0.5' : 'h-px my-1'}`}></div>
        <button
          onClick={handleToggle3D}
          className={`rounded-md hover:bg-slate-200 flex items-center justify-center transition-colors ${
            isMobile ? 'w-6 h-6' : 'w-8 h-8'
          } ${map.getPitch() > 0 ? 'bg-slate-200' : ''}`}
          title="3D视角"
        >
          <svg className={`text-slate-600 ${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
          </svg>
        </button>
      </div>
    </div>
  );
}
