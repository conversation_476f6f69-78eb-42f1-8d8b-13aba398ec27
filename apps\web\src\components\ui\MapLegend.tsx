import { useState } from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { turboColors } from '../map/layers/EarthquakeMapLibreLayer';
import { DualRangeSlider } from './DualRangeSlider';
import { useDataStats } from '../../hooks/useDataStats';
import { useEarthquakeData } from '../../contexts/EarthquakeDataContext';
import { useFilteredLayerStats } from '../../hooks/useFilteredLayerStats';
import { 
  EarthquakeIcon, 
  FaultLineIcon, 
  WellTrajectoryIcon, 
  WellPlatformIcon, 
  MonitoringStationIcon 
} from '../icons';
import * as turf from '@turf/turf';

export function MapLegend() {
  const { layerVisibility, filters, setMagnitudeRange } = useMapStore();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { stats } = useDataStats();
  const { earthquakes, allEarthquakes, loading: earthquakeLoading, dataTimeRange } = useEarthquakeData();
  const filteredStats = useFilteredLayerStats();

  // 基于时间和空间筛选的地震数据计算快速选择选项的震级统计（不应用震级筛选）
  const calculateQuickSelectStats = () => {
    if (!allEarthquakes || allEarthquakes.length === 0) {
      return [];
    }

    // 应用时间和空间筛选，但不应用震级筛选
    const filteredForStats = allEarthquakes.filter(eq => {
      // 时间过滤
      if (filters.timeRange) {
        const eqTime = new Date(eq.occurred_at);
        if (eqTime < filters.timeRange.start || eqTime > filters.timeRange.end) {
          return false;
        }
      }

      // 深度过滤
      if (filters.depthRange) {
        if (eq.depth < filters.depthRange[0] || eq.depth > filters.depthRange[1]) {
          return false;
        }
      }

      // 区域过滤
      if (filters.selectedRegions && filters.selectedRegions.length > 0) {
        if (!filters.selectedRegions.includes(eq.region)) {
          return false;
        }
      }

      // 空间筛选
      if (filters.spatialFilter && filters.spatialFilter.center && filters.spatialFilter.radius) {
        try {
          const distance = turf.distance(
            [eq.longitude, eq.latitude],
            filters.spatialFilter.center,
            { units: 'kilometers' }
          );
          if (distance > filters.spatialFilter.radius) {
            return false;
          }
        } catch (error) {
          console.error('空间筛选计算错误:', error);
          return true; // 出错时不筛选
        }
      }

      return true;
    });

    // 根据快速选择选项的范围计算统计数据
    const stats = [
      { magnitude_range: '小震 (-2-1)', count: filteredForStats.filter(eq => eq.magnitude >= -2 && eq.magnitude <= 1).length },
      { magnitude_range: '中震 (1-2.5)', count: filteredForStats.filter(eq => eq.magnitude > 1 && eq.magnitude <= 2.5).length },
      { magnitude_range: '大震 (2.5+)', count: filteredForStats.filter(eq => eq.magnitude > 2.5).length },
    ];

    return stats;
  };

  // 格式化数量显示
  const formatCount = (count: number, loading: boolean = false) => {
    if (loading) return '...';
    if (count === 0) return '0';
    if (count >= 1000) return `${(count / 1000).toFixed(1)}k`;
    return count.toString();
  };

  // 格式化时间范围显示
  const formatTimeRange = (start: Date, end: Date) => {
    const formatDate = (date: Date) => {
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    };

    return `${formatDate(start)} - ${formatDate(end)}`;
  };

  // 获取当前筛选时间范围
  const getCurrentTimeRange = () => {
    if (filters.timeRange) {
      return formatTimeRange(filters.timeRange.start, filters.timeRange.end);
    }
    return '';
  };

  // 获取总数据时间范围
  const getTotalTimeRange = () => {
    if (dataTimeRange) {
      return formatTimeRange(dataTimeRange.start, dataTimeRange.end);
    }
    return '';
  };

  // 断层等级图例 - 保持颜色区分，但统一使用二级断层粗细
  const faultLegend = [
    { level: '一级断层', color: 'bg-red-600', width: 'h-0.5' }, // #dc2626，统一粗细
    { level: '二级断层', color: 'bg-orange-600', width: 'h-0.5' }, // #ea580c，统一粗细
    { level: '三级断层', color: 'bg-yellow-500', width: 'h-0.5' }, // #eab308，统一粗细
  ];

  // 台站状态图例 - 根据实际代码中的颜色配置
  const stationLegend = [
    { status: '在线', color: 'bg-orange-500', icon: '▼' }, // #f97316 橙色倒三角
    { status: '维护中', color: 'bg-blue-500', icon: '▼' }, // #3b82f6 蓝色倒三角
    { status: '离线', color: 'bg-gray-500', icon: '▼' }, // #6b7280 灰色倒三角
  ];

  if (isCollapsed) {
    return (
      <div className="absolute z-1 left-4 bottom-4 fade-in
        sm:left-6 sm:bottom-6
        max-sm:left-1 max-sm:bottom-1">
        <button
          onClick={() => setIsCollapsed(false)}
          className="floating-panel-unified rounded-lg p-2 hover:bg-white/20 transition-all duration-200 active:scale-95
            max-sm:p-1.5 max-sm:rounded-md"
          style={{
            background: `
              linear-gradient(135deg,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(248, 250, 252, 0.8) 100%
              )
            `,
            backdropFilter: 'blur(12px) saturate(150%)',
            WebkitBackdropFilter: 'blur(12px) saturate(150%)',
            boxShadow: `
              0 4px 16px rgba(0, 0, 0, 0.1),
              0 1px 0 rgba(255, 255, 255, 0.5) inset
            `
          }}
          title="展开图例"
        >
          <div className="flex items-center space-x-1.5 max-sm:space-x-1">
            <span className="text-xs font-medium text-slate-700 max-sm:text-xs">图例</span>
            <svg className="w-3 h-3 text-slate-500 max-sm:w-2.5 max-sm:h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
        </button>
      </div>
    );
  }

  return (
    <div className="absolute z-1 left-4 bottom-4 fade-in
      sm:left-6 sm:bottom-6
      max-sm:left-1 max-sm:bottom-1">
      <div className="floating-panel-unified rounded-lg w-64 max-h-96 flex flex-col
        max-sm:w-48 max-sm:max-h-64"
        style={{
          background: `
            linear-gradient(135deg,
              rgba(255, 255, 255, 0.95) 0%,
              rgba(248, 250, 252, 0.9) 100%
            )
          `,
          backdropFilter: 'blur(16px) saturate(180%)',
          WebkitBackdropFilter: 'blur(16px) saturate(180%)',
          boxShadow: `
            0 8px 32px rgba(0, 0, 0, 0.12),
            0 1px 0 rgba(255, 255, 255, 0.5) inset
          `
        }}>
        {/* 图例标题 - 可点击收起 */}
        <div
          onClick={() => setIsCollapsed(true)}
          className="flex items-center justify-between p-3 pb-2 flex-shrink-0 cursor-pointer hover:bg-white/20 transition-all duration-200 active:scale-[0.98] border-b border-white/10
            max-sm:p-2 max-sm:pb-1"
          style={{
            background: 'linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)'
          }}
          title="点击收起图例"
        >
          <div className="flex items-center space-x-1.5 max-sm:space-x-1">
            <h3 className="text-sm font-semibold text-slate-900 max-sm:text-xs">图例</h3>
          </div>
          <div className="flex items-center space-x-1 max-sm:space-x-0.5">
            <span className="text-[10px] text-slate-400 max-sm:hidden">收起</span>
            <svg className="w-3 h-3 text-slate-500 max-sm:w-2.5 max-sm:h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </div>
        </div>



        {/* 图例内容 - 可滚动区域 */}
        <div className="flex-1 overflow-y-auto px-3 pb-3 max-sm:px-2 max-sm:pb-2">
          <div className="space-y-4 max-sm:space-y-1.5">
          {/* 地震事件图例 */}
          {layerVisibility.earthquakes && (
            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-2 flex items-center justify-between
                max-sm:text-xs max-sm:mb-1">
                <div className="flex items-center">
                  <EarthquakeIcon className="w-4 h-4 mr-2 max-sm:w-3 max-sm:h-3 max-sm:mr-1" style={{ color: '#dc2626' }} />
                  <span className="max-sm:text-xs">地震事件</span>
                  {getCurrentTimeRange() && (
                    <span className="ml-2 text-xs text-slate-500 font-normal max-sm:hidden">
                      {getCurrentTimeRange()}
                    </span>
                  )}
                </div>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full
                  max-sm:text-xs max-sm:px-1 max-sm:py-0">
                  {formatCount(earthquakes?.length || 0, earthquakeLoading)}
                </span>
              </h4>

              {/* 时间turbo色带 */}
              <div className="mb-3 max-sm:mb-2">
                <h5 className="text-xs font-medium text-slate-600 mb-1 max-sm:text-xs max-sm:mb-0.5">
                  <span className="max-sm:hidden">颜色 ({getTotalTimeRange() || '发生时间'})</span>
                  <span className="sm:hidden">颜色</span>
                </h5>
                <div className="flex items-center">
                  <div className="w-20 h-3 rounded-full border border-white max-sm:w-16 max-sm:h-2"
                       style={{
                         background: `linear-gradient(to right, ${turboColors.join(', ')})`
                       }}></div>
                  <div className="ml-2 text-xs text-slate-600 max-sm:ml-1">
                    <div className="flex justify-between w-16 max-sm:w-12">
                      <span className="max-sm:text-xs">较早</span>
                      <span className="max-sm:text-xs">较晚</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 震级大小 */}
              <div>
                <h5 className="text-xs font-medium text-slate-600 mb-1 max-sm:text-xs max-sm:mb-0.5">大小 (震级)</h5>

                {/* 震级过滤器 */}
                <DualRangeSlider
                  value={filters.magnitudeRange}
                  onChange={setMagnitudeRange}
                  min={-2}
                  max={6}
                  step={0.1}
                  showQuickSelect={true}
                  className="mb-2 max-sm:mb-1"
                  magnitudeStats={calculateQuickSelectStats()}
                  loading={earthquakeLoading}
                />
              </div>
            </div>
          )}

          {/* 断层数据图例 */}
          {layerVisibility.faults && (
            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-2 flex items-center justify-between
                max-sm:text-xs max-sm:mb-1">
                <div className="flex items-center">
                  <FaultLineIcon className="w-4 h-4 mr-2 max-sm:w-3 max-sm:h-3 max-sm:mr-1" style={{ color: '#ea580c' }} />
                  <span className="max-sm:text-xs">断层数据</span>
                </div>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full
                  max-sm:text-xs max-sm:px-1 max-sm:py-0">
                  {formatCount(filteredStats.faults.total, filteredStats.loading)}
                </span>
              </h4>
              <div className="space-y-1.5 max-sm:space-y-0.5">
                {faultLegend.map((item, index) => {
                  const levelStat = filteredStats.faults.levelStats.find(s => s.level === index + 1);
                  const count = levelStat?.count || 0;
                  return (
                    <div key={index} className="flex items-center justify-between text-xs text-slate-600">
                      <div className="flex items-center">
                        <div className={`w-6 ${item.width} ${item.color} mr-2 flex-shrink-0 rounded-sm max-sm:w-4 max-sm:mr-1`}></div>
                        <span className="max-sm:text-xs">{item.level}</span>
                      </div>
                      <span className="text-xs text-slate-400 max-sm:text-xs">
                        {formatCount(count, filteredStats.loading)}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* 井轨迹图例 */}
          {layerVisibility.wellTrajectories && (
            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-2 flex items-center justify-between
                max-sm:text-xs max-sm:mb-1">
                <div className="flex items-center">
                  <WellTrajectoryIcon className="w-4 h-4 mr-2 max-sm:w-3 max-sm:h-3 max-sm:mr-1" style={{ color: '#2563eb' }} />
                  <span className="max-sm:text-xs">井轨迹</span>
                </div>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full
                  max-sm:text-xs max-sm:px-1 max-sm:py-0">
                  {formatCount(filteredStats.wells.total, filteredStats.loading)}
                </span>
              </h4>
              <div className="space-y-1.5 max-sm:space-y-0.5">
                <div className="flex items-center justify-between text-xs text-slate-600">
                  <div className="flex items-center">
                    <div className="w-6 h-0.5 bg-gray-700 mr-2 flex-shrink-0 rounded-sm max-sm:w-4 max-sm:mr-1"></div>
                    <span className="max-sm:text-xs">井轨迹路径</span>
                  </div>
                  <span className="text-xs text-slate-400 max-sm:text-xs">
                    {formatCount(filteredStats.wells.total, filteredStats.loading)}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 井平台图例 */}
          {layerVisibility.wellPlatforms && (
            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-2 flex items-center justify-between
                max-sm:text-xs max-sm:mb-1">
                <div className="flex items-center">
                  <WellPlatformIcon className="w-4 h-4 mr-2 max-sm:w-3 max-sm:h-3 max-sm:mr-1" style={{ color: '#059669' }} />
                  <span className="max-sm:text-xs">井平台</span>
                </div>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full
                  max-sm:text-xs max-sm:px-1 max-sm:py-0">
                  {formatCount(filteredStats.platforms.total, filteredStats.loading)}
                </span>
              </h4>
              <div className="space-y-1.5 max-sm:space-y-0.5">
                <div className="flex items-center justify-between text-xs text-slate-600">
                  <div className="flex items-center">
                    <WellPlatformIcon className="w-4 h-4 mr-2 flex-shrink-0 max-sm:w-3 max-sm:h-3 max-sm:mr-1" style={{ color: '#059669' }} />
                    <span className="max-sm:text-xs">井平台位置</span>
                  </div>
                  <span className="text-xs text-slate-400 max-sm:text-xs">
                    {formatCount(filteredStats.platforms.total, filteredStats.loading)}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 监测台站图例 */}
          {layerVisibility.stations && (
            <div>
              <h4 className="text-xs font-medium text-slate-700 mb-2 flex items-center justify-between
                max-sm:text-xs max-sm:mb-1">
                <div className="flex items-center">
                  <MonitoringStationIcon className="w-4 h-4 mr-2 max-sm:w-3 max-sm:h-3 max-sm:mr-1" style={{ color: '#ea580c' }} />
                  <span className="max-sm:text-xs">监测台站</span>
                </div>
                <span className="text-xs text-slate-500 bg-slate-100 px-2 py-0.5 rounded-full
                  max-sm:text-xs max-sm:px-1 max-sm:py-0">
                  {formatCount(filteredStats.stations.total, filteredStats.loading)}
                </span>
              </h4>
              <div className="space-y-1.5 max-sm:space-y-0.5">
                {stationLegend.map((item, index) => {
                  const statusMapping: Record<string, string> = {
                    '在线': 'active',
                    '维护中': 'maintenance',
                    '离线': 'inactive'
                  };
                  const statusStat = filteredStats.stations.statusStats.find(s => s.status === statusMapping[item.status]);
                  const count = statusStat?.count || 0;

                  // 为不同状态设置颜色
                  const getStatusColor = (status: string) => {
                    switch (status) {
                      case '在线': return '#f97316'; // 橙色
                      case '维护中': return '#3b82f6'; // 蓝色
                      case '离线': return '#6b7280'; // 灰色
                      default: return '#f97316';
                    }
                  };

                  return (
                    <div key={index} className="flex items-center justify-between text-xs text-slate-600">
                      <div className="flex items-center">
                        <MonitoringStationIcon
                          className="w-4 h-4 mr-2 flex-shrink-0 max-sm:w-3 max-sm:h-3 max-sm:mr-1"
                          style={{ color: getStatusColor(item.status) }}
                        />
                        <span className="max-sm:text-xs">{item.status}</span>
                      </div>
                      <span className="text-xs text-slate-400 max-sm:text-xs">
                        {formatCount(count, filteredStats.loading)}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          </div>
        </div>
      </div>
    </div>
  );
}
