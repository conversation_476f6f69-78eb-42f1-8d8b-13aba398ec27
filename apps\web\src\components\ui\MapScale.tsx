import React, { useState, useEffect } from 'react';
import { useMap } from 'react-map-gl/maplibre';

export function MapScale() {
  const { current: map } = useMap();
  const [scaleInfo, setScaleInfo] = useState({ distance: '50km', width: 64 });

  // 计算比例尺
  const calculateScale = () => {
    if (!map) return;
    
    const zoom = map.getZoom();
    const center = map.getCenter();
    
    // 基于缩放级别计算合适的距离
    // 这个公式基于Web Mercator投影的特性
    const metersPerPixel = 156543.03392 * Math.cos(center.lat * Math.PI / 180) / Math.pow(2, zoom);
    
    // 目标宽度为64像素
    const targetWidthPixels = 64;
    const distanceMeters = metersPerPixel * targetWidthPixels;
    
    // 转换为合适的单位和舍入到合理的数值
    let distance: string;
    let actualWidthPixels: number;
    
    if (distanceMeters < 1000) {
      // 小于1km，使用米
      const roundedMeters = Math.round(distanceMeters / 10) * 10; // 舍入到10米
      distance = `${roundedMeters}m`;
      actualWidthPixels = (roundedMeters / metersPerPixel);
    } else if (distanceMeters < 100000) {
      // 1km-100km，使用公里
      const km = distanceMeters / 1000;
      const roundedKm = km < 10 ? Math.round(km * 2) / 2 : Math.round(km); // 小于10km时舍入到0.5km
      distance = `${roundedKm}km`;
      actualWidthPixels = (roundedKm * 1000 / metersPerPixel);
    } else {
      // 大于100km
      const km = Math.round(distanceMeters / 1000);
      distance = `${km}km`;
      actualWidthPixels = (km * 1000 / metersPerPixel);
    }
    
    const finalWidth = Math.max(20, Math.min(100, actualWidthPixels));
    setScaleInfo({ distance, width: finalWidth });
  };

  // 监听地图缩放变化
  useEffect(() => {
    if (!map) return;
    
    // 初始计算
    calculateScale();
    
    const handleZoomEnd = () => {
      calculateScale();
    };
    
    const handleMoveEnd = () => {
      calculateScale();
    };
    
    // 使用正确的MapLibre事件名称
    map.on('zoomend', handleZoomEnd);
    map.on('moveend', handleMoveEnd);
    
    return () => {
      map.off('zoomend', handleZoomEnd);
      map.off('moveend', handleMoveEnd);
    };
  }, [map]);

  return (
    <div className="absolute z-1 right-4 bottom-4 fade-in
      sm:right-6 sm:bottom-6
      max-sm:right-2 max-sm:bottom-2">
      <div className="floating-panel-unified rounded-lg p-3 pb-2">
        <div className="flex items-center">
          <div
            className="h-0.5 bg-slate-600 border-l-2 border-r-2 border-slate-600
              transition-all duration-500 ease-out"
            style={{ width: `${scaleInfo.width}px` }}
          ></div>
        </div>
        <div
          className="flex justify-between text-xs text-slate-500 mt-1
            transition-all duration-500 ease-out"
          style={{ width: `${scaleInfo.width}px` }}
        >
          <span>0</span>
          <span className="font-medium transition-all duration-500 ease-out">
            {scaleInfo.distance}
          </span>
        </div>
      </div>
    </div>
  );
}
