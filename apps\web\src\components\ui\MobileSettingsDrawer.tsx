import React, { useState } from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useLayerData } from '../../contexts/LayerDataContext';
import { useEarthquakeData } from '../../hooks/useEarthquakeData';
import { LoadingSpinner } from './LoadingSpinner';
import {
  EarthquakeIcon,
  FaultLineIcon,
  WellTrajectoryIcon,
  WellPlatformIcon,
  MonitoringStationIcon,
  AnalysisToolIcon,
  BeachBallIcon
} from '../icons';
import {
  GRLawAnalysis,
  DynamicBValueAnalysis,
  FocalMechanismDisplay,
  CoulombStressCalculator
} from '../analysis/modules';

interface MobileSettingsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onShowLayerTable?: (layerKey: string) => void;
  showDataTable?: boolean;
  showLayerTable?: string | null;
  type?: 'settings' | 'analysis';
}

export function MobileSettingsDrawer({
  isOpen,
  onClose,
  onShowLayerTable,
  showDataTable,
  showLayerTable,
  type = 'settings'
}: MobileSettingsDrawerProps) {
  const { 
    layerVisibility, 
    toggleLayer, 
    baseMapType, 
    setBaseMapType, 
    showLabels, 
    setShowLabels,
    isThreeMode 
  } = useMapStore();
  
  const layerData = useLayerData();
  const { loading: earthquakeLoading } = useEarthquakeData();
  const [activeTab, setActiveTab] = useState<'layers' | 'basemap'>('layers');
  const [analysisTab, setAnalysisTab] = useState<'gr-law' | 'dynamic-b' | 'focal-mechanism' | 'coulomb'>('gr-law');



  const layerConfig = [
    {
      key: 'earthquakes' as const,
      label: '地震事件',
      icon: EarthquakeIcon,
      color: 'text-red-600',
      getLoadingState: () => earthquakeLoading,
    },
    {
      key: 'faults' as const,
      label: '断层数据',
      icon: FaultLineIcon,
      color: 'text-orange-600',
      getLoadingState: (layerData: any) => layerData.faultsLoading,
    },
    {
      key: 'wellTrajectories' as const,
      label: '井轨迹',
      icon: WellTrajectoryIcon,
      color: 'text-blue-600',
      getLoadingState: (layerData: any) => layerData.wellsLoading,
    },
    {
      key: 'wellPlatforms' as const,
      label: '井平台',
      icon: WellPlatformIcon,
      color: 'text-emerald-600',
      getLoadingState: (layerData: any) => layerData.platformsLoading,
    },
    {
      key: 'stations' as const,
      label: '监测台站',
      icon: MonitoringStationIcon,
      color: 'text-orange-600',
      getLoadingState: (layerData: any) => layerData.stationsLoading,
    },
  ];

  const mapTypes = [
    {
      key: 'vector' as const,
      label: '矢量图',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      ),
    },
    {
      key: 'satellite' as const,
      label: '卫星图',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      key: 'terrain' as const,
      label: '地形图',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3l4 6H5l5.5 7.5L16 10h-4l4-7H5z" />
        </svg>
      ),
    },
  ];

  return (
    <div
      className={`absolute right-16 top-24 ${type === 'analysis' ? 'w-72' : 'w-52'} backdrop-blur-2xl shadow-2xl border border-white/20 z-50 rounded-2xl overflow-hidden transition-all duration-300 ease-out transform ${
        isOpen
          ? 'translate-x-0 opacity-100 scale-100'
          : 'translate-x-full opacity-0 scale-95 pointer-events-none'
      }`}
      style={{
        maxHeight: 'calc(100vh - 8rem)',
        background: `
          linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 25%,
            rgba(248, 250, 252, 0.1) 50%,
            rgba(241, 245, 249, 0.05) 75%,
            rgba(255, 255, 255, 0.1) 100%
          ),
          radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
          rgba(255, 255, 255, 0.8)
        `,
        backdropFilter: 'blur(20px) saturate(180%)',
        WebkitBackdropFilter: 'blur(20px) saturate(180%)',
        boxShadow: `
          0 8px 32px rgba(0, 0, 0, 0.1),
          0 1px 0 rgba(255, 255, 255, 0.5) inset,
          0 -1px 0 rgba(0, 0, 0, 0.05) inset
        `
      }}
    >
        {/* 紧凑的可点击头部 */}
        <div
          onClick={onClose}
          className="flex items-center justify-between p-2 cursor-pointer hover:bg-white/20 transition-all duration-200 active:scale-[0.98] border-b border-white/10"
          style={{
            background: 'linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)'
          }}
        >
          <div className="flex items-center space-x-1.5">
            <div className="w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></div>
            <h2 className="text-xs font-medium text-slate-700">
              {type === 'analysis' ? '数据分析' : '设置'}
            </h2>
          </div>
          <div className="flex items-center space-x-1">
            <span className="text-[10px] text-slate-400">收起</span>
            <svg className="w-3 h-3 text-slate-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </div>
        </div>

        {/* 超紧凑标签页 */}
        <div className="flex border-b border-white/10">
          {type === 'settings' ? (
            <>
              <button
                onClick={() => setActiveTab('layers')}
                className={`flex-1 py-1.5 px-2 text-xs font-medium transition-all duration-200 relative ${
                  activeTab === 'layers'
                    ? 'text-blue-600 bg-white/20'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/10'
                }`}
              >
                <span className="relative z-10">图层</span>
                {activeTab === 'layers' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400 rounded-t"></div>
                )}
              </button>
              <button
                onClick={() => setActiveTab('basemap')}
                className={`flex-1 py-1.5 px-2 text-xs font-medium transition-all duration-200 relative ${
                  activeTab === 'basemap'
                    ? 'text-blue-600 bg-white/20'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/10'
                }`}
              >
                <span className="relative z-10">底图</span>
                {activeTab === 'basemap' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400 rounded-t"></div>
                )}
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => setAnalysisTab('gr-law')}
                className={`flex-1 py-1.5 px-1 text-xs font-medium transition-all duration-200 relative ${
                  analysisTab === 'gr-law'
                    ? 'text-blue-600 bg-white/20'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/10'
                }`}
              >
                <span className="relative z-10">GR Law</span>
                {analysisTab === 'gr-law' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400 rounded-t"></div>
                )}
              </button>
              <button
                onClick={() => setAnalysisTab('dynamic-b')}
                className={`flex-1 py-1.5 px-1 text-xs font-medium transition-all duration-200 relative ${
                  analysisTab === 'dynamic-b'
                    ? 'text-blue-600 bg-white/20'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/10'
                }`}
              >
                <span className="relative z-10">动态b值</span>
                {analysisTab === 'dynamic-b' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400 rounded-t"></div>
                )}
              </button>
              <button
                onClick={() => setAnalysisTab('focal-mechanism')}
                className={`flex-1 py-1.5 px-1 text-xs font-medium transition-all duration-200 relative ${
                  analysisTab === 'focal-mechanism'
                    ? 'text-blue-600 bg-white/20'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/10'
                }`}
              >
                <span className="relative z-10">震源机制</span>
                {analysisTab === 'focal-mechanism' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400 rounded-t"></div>
                )}
              </button>
              <button
                onClick={() => setAnalysisTab('coulomb')}
                className={`flex-1 py-1.5 px-1 text-xs font-medium transition-all duration-200 relative ${
                  analysisTab === 'coulomb'
                    ? 'text-blue-600 bg-white/20'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-white/10'
                }`}
              >
                <span className="relative z-10">库伦应力</span>
                {analysisTab === 'coulomb' && (
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-400 rounded-t"></div>
                )}
              </button>
            </>
          )}
        </div>

        {/* 超紧凑内容区域 */}
        <div className={`overflow-y-auto p-1.5 ${type === 'analysis' ? 'max-h-96' : 'max-h-80'}`}>
          {type === 'analysis' ? (
            /* 分析模式内容 */
            <div className="space-y-2 text-xs">
              {analysisTab === 'gr-law' && (
                <GRLawAnalysis />
              )}
              {analysisTab === 'dynamic-b' && (
                <DynamicBValueAnalysis />
              )}
              {analysisTab === 'focal-mechanism' && (
                  <FocalMechanismDisplay />
              )}
              {analysisTab === 'coulomb' && (
                <CoulombStressCalculator />
              )}
            </div>
          ) : (
            /* 设置模式内容 */
            <>
              {activeTab === 'layers' && (
            <div className="space-y-1">
              {layerConfig.map((layer) => {
                const isLoading = layer.getLoadingState(layerData);
                const isTableActive = (layer.key === 'earthquakes' && showDataTable) ||
                                      (layer.key !== 'earthquakes' && showLayerTable === layer.key);
                const isLayerVisible = layerVisibility[layer.key];
                const IconComponent = layer.icon;

                return (
                  <div
                    key={layer.key}
                    className="flex items-center justify-between p-1.5 rounded-lg border border-white/20 hover:bg-white/10 transition-all duration-200"
                    style={{
                      background: 'linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)'
                    }}
                  >
                    <div className="flex items-center space-x-1.5 flex-1 min-w-0">
                      <div className={`${layer.color} flex-shrink-0`}>
                        {isLoading ? (
                          <LoadingSpinner size="sm" color="slate" className="w-4 h-4" />
                        ) : (
                          <IconComponent className="w-4 h-4" />
                        )}
                      </div>
                      <span className="text-xs font-medium text-slate-700 truncate">{layer.label}</span>
                    </div>
                    
                    <div className="flex items-center space-x-1 flex-shrink-0">
                      {/* 紧凑表格按钮 */}
                      <button
                        onClick={() => onShowLayerTable?.(layer.key)}
                        className={`w-6 h-6 rounded flex items-center justify-center transition-all duration-200 ${
                          isTableActive
                            ? 'bg-blue-500 text-white'
                            : 'bg-white/50 text-slate-500 hover:bg-white/80 hover:text-slate-700'
                        }`}
                        title={`${isTableActive ? '关闭' : '查看'}${layer.label}表格`}
                      >
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M3 7h18M3 12h18M3 17h18"/>
                        </svg>
                      </button>

                      {/* 紧凑图层开关 */}
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={isLayerVisible}
                          onChange={() => toggleLayer(layer.key)}
                          disabled={isLoading}
                        />
                        <div className={`w-6 h-4 rounded-full transition-all duration-300 ${
                          isLayerVisible
                            ? 'bg-blue-500'
                            : 'bg-slate-300'
                        }`}>
                          <div className={`w-3 h-3 bg-white rounded-full shadow transform transition-all duration-300 ${
                            isLayerVisible ? 'translate-x-3' : 'translate-x-0.5'
                          } mt-0.5`} />
                        </div>
                      </label>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {activeTab === 'basemap' && !isThreeMode && (
            <div className="space-y-1">
              {/* 紧凑底图选择 */}
              <div className="grid grid-cols-1 gap-2">
                {mapTypes.map((type) => (
                  <button
                    key={type.key}
                    onClick={() => setBaseMapType(type.key)}
                    className={`flex items-center space-x-1.5 p-2 rounded-lg transition-all duration-200 ${
                      baseMapType === type.key
                        ? 'bg-blue-500/20 border border-blue-400/30 text-blue-700'
                        : 'border border-white/20 text-slate-600 hover:bg-white/10'
                    }`}
                    style={{
                      background: baseMapType === type.key
                        ? 'linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%)'
                        : 'linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)'
                    }}
                  >
                    <div className={`w-4 h-4 flex-shrink-0 transition-colors ${
                      baseMapType === type.key ? 'text-blue-600' : 'text-slate-500'
                    }`}>
                      {React.cloneElement(type.icon as React.ReactElement, {
                        className: "w-4 h-4"
                      })}
                    </div>
                    <span className="text-xs font-medium flex-1 text-left">{type.label}</span>
                    {baseMapType === type.key && (
                      <div className="w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg className="w-1.5 h-1.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </button>
                ))}
              </div>

              {/* 紧凑注记控制 */}
              <div
                className="flex items-center justify-between p-1.5 rounded-lg border border-white/20 hover:bg-white/10 transition-all duration-200 mt-2"
                style={{
                  background: 'linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%)'
                }}
              >
                <div className="flex items-center space-x-1.5">
                  <div className="w-4 h-4 bg-orange-400 rounded flex items-center justify-center">
                    <svg className="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="3" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                  </div>
                  <span className="text-xs font-medium text-slate-700">地名标注</span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={showLabels}
                    onChange={(e) => setShowLabels(e.target.checked)}
                  />
                  <div className={`w-6 h-3 rounded-full transition-all duration-300 ${
                    showLabels
                      ? 'bg-blue-500'
                      : 'bg-slate-300'
                  }`}>
                    <div className={`w-2 h-2 bg-white rounded-full shadow transform transition-all duration-300 ${
                      showLabels ? 'translate-x-3' : 'translate-x-0.5'
                    } mt-0.5`} />
                  </div>
                </label>
              </div>
            </div>
          )}

              {activeTab === 'basemap' && isThreeMode && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center text-slate-500">
                    <svg className="w-12 h-12 mx-auto mb-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-sm">3D模式下不支持底图切换</p>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
    </div>
  );
}
