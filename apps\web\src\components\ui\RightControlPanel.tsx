
import { useMapStore } from '../../stores/useMapStore';

export function RightControlPanel() {
  const { baseMapType, setBaseMapType, showLabels, setShowLabels, isThreeMode } = useMapStore();

  const mapTypes = [
    {
      key: 'vector' as const,
      label: '矢量图',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
        </svg>
      ),
    },
    {
      key: 'satellite' as const,
      label: '卫星图',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      key: 'terrain' as const,
      label: '地形图',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3l4 6 2-3 8 14H5l4-6 2-3-8-14z" />
        </svg>
      ),
    },
  ];

  return (
    <div className="fixed z-50 right-22 top-4 sm:right-24 fade-in">
      {/* 底图切换面板 - 仅在二维模式下显示 */}
      {!isThreeMode && (
          <div className="floating-panel-unified rounded-lg p-2">
            {/* 第一行：矢量图和卫星图 */}
            <div className="flex space-x-1 mb-1">
              {mapTypes.slice(0, 2).map((type) => (
                <button
                  key={type.key}
                  onClick={() => setBaseMapType(baseMapType === type.key ? null : type.key)}
                  className={`
                    flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200 flex-1
                    ${baseMapType === type.key
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
                    }
                  `}
                  title={baseMapType === type.key ? `取消选中${type.label}` : `切换到${type.label}`}
                >
                  {type.icon}
                  <span className="hidden sm:inline text-xs">{type.label}</span>
                </button>
              ))}
            </div>

            {/* 第二行：地形图和注记开关 */}
            <div className="flex space-x-1 items-center">
              {/* 地形图按钮 */}
              <button
                onClick={() => setBaseMapType(baseMapType === 'terrain' ? null : 'terrain')}
                className={`
                  flex items-center space-x-1 px-2 py-1 rounded-md text-xs font-medium transition-all duration-200 flex-1
                  ${baseMapType === 'terrain'
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
                  }
                `}
                title={baseMapType === 'terrain' ? '取消选中地形图' : '切换到地形图'}
              >
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 3l4 6 2-3 8 14H5l4-6 2-3-8-14z" />
                </svg>
                <span className="hidden sm:inline text-xs">地形图</span>
              </button>

              {/* 注记开关 */}
              <div className="flex items-center space-x-1 px-2 py-1">
                <svg className="w-3 h-3 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <span className="text-xs font-medium text-slate-700 hidden sm:inline">注记</span>
                <button
                  onClick={() => setShowLabels(!showLabels)}
                  className={`
                    relative inline-flex h-4 w-7 items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none
                    ${showLabels ? 'bg-blue-600' : 'bg-slate-300'}
                  `}
                  title={showLabels ? '隐藏注记' : '显示注记'}
                >
                  <span
                    className={`
                      inline-block h-2.5 w-2.5 transform rounded-full bg-white transition-transform duration-200 ease-in-out
                      ${showLabels ? 'translate-x-3.5' : 'translate-x-0.5'}
                    `}
                  />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
  );
}
