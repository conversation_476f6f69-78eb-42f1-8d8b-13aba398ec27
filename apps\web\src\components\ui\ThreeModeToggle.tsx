import { useMapStore } from '../../stores/useMapStore';

// 自定义3D图标组件
const ThreeDIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path d="M12 2L22 8V16L12 22L2 16V8L12 2Z" strokeWidth="2" strokeLinejoin="round"/>
    <path d="M12 22V12" strokeWidth="2" strokeLinecap="round"/>
    <path d="M22 8L12 12L2 8" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

// 自定义2D图标组件
const TwoDIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <rect x="3" y="3" width="18" height="18" rx="2" strokeWidth="2"/>
    <path d="M3 9H21" strokeWidth="2" strokeLinecap="round"/>
    <path d="M9 3V21" strokeWidth="2" strokeLinecap="round"/>
    <circle cx="7" cy="7" r="1" fill="currentColor"/>
    <circle cx="13" cy="7" r="1" fill="currentColor"/>
    <circle cx="17" cy="7" r="1" fill="currentColor"/>
  </svg>
);

export function ThreeModeToggle() {
  const { isThreeMode, setThreeMode } = useMapStore();

  return (
    <div className={`fixed z-50 right-4 top-4 sm:right-6`}>
      {/* 三维模式切换按钮 - 美化版本 */}
      <div className="floating-panel-unified rounded-xl shadow-lg backdrop-blur-md border border-white/20">
        <button
          onClick={() => setThreeMode(!isThreeMode)}
          className={`
            group relative flex flex-col items-center justify-center px-3 py-3 rounded-lg text-sm font-medium
            transition-all duration-300 ease-out transform hover:scale-105 active:scale-95 min-w-[60px]
            ${isThreeMode
              ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg shadow-blue-500/25'
              : 'text-slate-700 hover:bg-gradient-to-r hover:from-slate-50 hover:to-slate-100 hover:text-slate-900 hover:shadow-md'
            }
          `}
          title={isThreeMode ? "切换到二维地图" : "切换到三维视图"}
        >
          {/* 图标和文字容器 - 上下排列 */}
          <div className="relative flex flex-col items-center space-y-1">
            {/* 主图标 */}
            <div className={`
              relative transition-all duration-300 transform
              ${isThreeMode ? 'rotate-0 scale-100' : 'rotate-12 scale-110'}
            `}>
              {isThreeMode ? (
                <TwoDIcon className="w-6 h-6" />
              ) : (
                <ThreeDIcon className="w-6 h-6" />
              )}
            </div>

            {/* 文字标签 */}
            <span className={`
              transition-all duration-300 font-semibold tracking-wide text-xs
              ${isThreeMode ? 'text-white' : 'text-slate-700 group-hover:text-slate-900'}
            `}>
              {isThreeMode ? '2D' : '3D'}
            </span>
          </div>

          {/* 激活状态指示器 */}
          {isThreeMode && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full border-2 border-white shadow-sm">
              <div className="w-full h-full bg-emerald-400 rounded-full animate-ping opacity-75" />
            </div>
          )}

          {/* 悬停效果光晕 */}
          <div className={`
            absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300
            ${isThreeMode
              ? 'bg-gradient-to-r from-blue-400/20 to-blue-500/20'
              : 'bg-gradient-to-r from-slate-100/50 to-slate-200/50'
            }
          `} />

          {/* 内部发光效果 */}
          {isThreeMode && (
            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-400/10 to-blue-500/10 animate-pulse" />
          )}
        </button>
      </div>
    </div>
  );
}
