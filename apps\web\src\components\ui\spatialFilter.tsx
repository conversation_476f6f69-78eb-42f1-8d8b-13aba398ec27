import React, { useState, useRef, useEffect } from 'react';
import { useMapStore } from '../../stores/useMapStore';
import { useMap } from 'react-map-gl/maplibre';
import { platformApi, stationApi } from '../../services/api';
import * as turf from '@turf/turf';
import { TerraDraw } from 'terra-draw';
import { TerraDrawMapLibreGLAdapter } from 'terra-draw-maplibre-gl-adapter';
import { TerraDrawCircleMode } from 'terra-draw';
import { useIsMobileOnly } from '@/hooks/useIsMobile';

interface SearchResult {
  type: 'platform' | 'station';
  id: number;
  name: string;
  latitude: number;
  longitude: number;
}

/**
 * 空间筛选工具条
 * 支持圆形绘制和手动设置功能
 */
export function SpatialFilterToolbar() {
  const { current: map } = useMap();
  const {
    filters,
    setSpatialFilter,
    setDrawingMode,
  } = useMapStore();
  const mapInstance = map?.getMap();

  const isMobile = useIsMobileOnly();

  const [longitude, setLongitude] = useState('');
  const [latitude, setLatitude] = useState('');
  const [radius, setRadius] = useState('10');
  const [isDrawing, setIsDrawing] = useState(false);
  const [showManualFilter, setShowManualFilter] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const [isPickingLocation, setIsPickingLocation] = useState(false);
  const [locationPickSuccess, setLocationPickSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // 搜索相关状态
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // 选中的搜索结果状态
  const [selectedSearchResult, setSelectedSearchResult] = useState<SearchResult | null>(null);

  // Terra Draw 相关
  const terraDrawRef = useRef<TerraDraw | null>(null);
  const popoverRef = useRef<HTMLDivElement>(null);
  const manualFilterButtonRef = useRef<HTMLButtonElement>(null);

  // 点击外部关闭popover - 只在非绘制和非选点状态时关闭
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        // 如果点击的是手动筛选按钮本身，不关闭popover
        if (manualFilterButtonRef.current && manualFilterButtonRef.current.contains(event.target as Node)) {
          return;
        }
        // 如果正在地图选点或绘制，不关闭popover
        if (isPickingLocation || isDrawing) {
          return;
        }
        setShowManualFilter(false);
      }
    }

    if (showManualFilter) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showManualFilter, isPickingLocation, isDrawing]);

  // 初始化 Terra Draw
  useEffect(() => {
    if (!mapInstance) {
      return;
    }

    let handleStyleLoad: (() => void) | null = null;

    // 检查地图样式是否已经加载完成
    if (mapInstance.isStyleLoaded()) {
      initTerraDraw();
    } else {
      // 等待样式加载完成
      setIsInitializing(true);
      handleStyleLoad = () => {
        initTerraDraw();
        if (handleStyleLoad) {
          mapInstance.off('styledata', handleStyleLoad);
        }
      };
      mapInstance.on('styledata', handleStyleLoad);
    }

    function initTerraDraw() {
      setIsInitializing(true);
      
      try {
        const adapter = new TerraDrawMapLibreGLAdapter({
          map: mapInstance,
          coordinatePrecision: 9,
        });

        const draw = new TerraDraw({
          adapter,
          modes: [
            new TerraDrawCircleMode({
              styles: {
                fillColor: '#3b82f6',
                fillOpacity: 0.2,
                outlineColor: '#3b82f6',
                outlineWidth: 3,
              },
            }),
          ],
        });

        // 监听绘制完成事件
        draw.on('finish', (id) => {
          handleDrawComplete();
        });

        // 监听绘制开始事件
        draw.on('change', (ids, type) => {
          // 绘制变化时的处理
        });

        // 启动 Terra Draw
        draw.start();
        
        terraDrawRef.current = draw;
        setIsInitializing(false);
      } catch (error) {
        console.error('Terra Draw 初始化失败:', error);
        terraDrawRef.current = null;
        setIsInitializing(false);
      }
    }

    // 清理函数
    return () => {
      // 清理样式加载监听器
      if (mapInstance && handleStyleLoad) {
        mapInstance.off('styledata', handleStyleLoad);
      }
      
      if (terraDrawRef.current) {
        try {
          // 检查地图实例是否仍然有效
          if (mapInstance && mapInstance.getCanvas()) {
            terraDrawRef.current.stop();
          }
        } catch (error) {
          // 忽略清理错误，因为地图可能已经被销毁
        } finally {
          terraDrawRef.current = null;
        }
      }
    };
  }, [mapInstance]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (terraDrawRef.current) {
        try {
          // 检查地图实例是否仍然有效
          if (mapInstance && mapInstance.getCanvas()) {
            terraDrawRef.current.stop();
          }
        } catch (error) {
          // 忽略清理错误，因为地图可能已经被销毁
        } finally {
          terraDrawRef.current = null;
        }
      }
      
      // 清理地图选点监听器
      if (mapInstance && isPickingLocation) {
        try {
          mapInstance.off('click', handleMapClick);
        } catch (error) {
          // 忽略清理错误
        }
      }
    };
  }, [mapInstance]);

  // 监听空间筛选状态变化，更新手动设置面板的输入框
  useEffect(() => {
    if (filters.spatialFilter.center && filters.spatialFilter.radius) {
      const [lng, lat] = filters.spatialFilter.center;
      setLongitude(lng.toFixed(6));
      setLatitude(lat.toFixed(6));
      setRadius(filters.spatialFilter.radius.toFixed(1));
    } else {
      // 清空输入框，半径保持默认值10公里
      setLongitude('');
      setLatitude('');
      setRadius('10');
      // 清空选中的搜索结果
      setSelectedSearchResult(null);
    }
  }, [filters.spatialFilter]);



  // 处理绘制完成
  const handleDrawComplete = () => {
    if (!terraDrawRef.current) return;

    // 获取绘制的要素
    const features = terraDrawRef.current.getSnapshot();

    if (features.length > 0) {
      const feature = features[features.length - 1]; // 获取最后一个要素

      if (feature.geometry.type === 'Polygon') {
        // 计算圆形的中心点和半径
        const center = turf.centroid(feature as any);
        const centerCoords = center.geometry.coordinates as [number, number];
        
        // 获取圆形的半径（使用第一个点到中心的距离）
        const firstPoint = feature.geometry.coordinates[0][0];
        const radius = turf.distance(centerCoords, firstPoint, { units: 'kilometers' });

        // 设置空间筛选
        setSpatialFilter({
          center: centerCoords,
          radius: radius,
        });

        // 清理绘制的要素
        try {
          terraDrawRef.current.clear();
        } catch (error) {
          // 忽略清理错误
        }
      }
    }

    // 停止绘制模式
    try {
      terraDrawRef.current.setMode('static');
    } catch (error) {
      // 忽略模式设置错误
    } finally {
      setIsDrawing(false);
      setDrawingMode(false); // 禁用绘制模式，恢复地图事件
      
      // 立即重置鼠标样式
      if (map) {
        const canvas = map.getCanvas();
        if (canvas) {
          canvas.style.cursor = '';
        }
      }
    }
  };

  // 搜索功能
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // 并行搜索井平台和监测台站
      const [platformResponse, stationResponse] = await Promise.all([
        platformApi.getPlatforms({ name: query, limit: 5 }),
        stationApi.getStations({ name: query, limit: 5 })
      ]);

      const results: SearchResult[] = [];

      // 添加井平台结果
      if (platformResponse.success && platformResponse.data) {
        platformResponse.data.platforms.forEach(platform => {
          results.push({
            type: 'platform',
            id: platform.id,
            name: platform.name,
            latitude: platform.latitude,
            longitude: platform.longitude,
          });
        });
      }

      // 添加监测台站结果
      if (stationResponse.success && stationResponse.data) {
        stationResponse.data.stations.forEach(station => {
          results.push({
            type: 'station',
            id: station.id,
            name: station.name,
            latitude: station.latitude,
            longitude: station.longitude,
          });
        });
      }

      setSearchResults(results);
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // 选择搜索结果
  const handleSelectSearchResult = (result: SearchResult) => {
    setLongitude(result.longitude.toFixed(6));
    setLatitude(result.latitude.toFixed(6));
    setSearchQuery('');
    setSearchResults([]);
    // 保存选中的搜索结果
    setSelectedSearchResult(result);
  };

  // 手动修改经纬度时清除选中的搜索结果
  const handleLongitudeChange = (value: string) => {
    setLongitude(value);
    // 如果用户手动修改了经纬度，清除选中的搜索结果
    if (selectedSearchResult) {
      setSelectedSearchResult(null);
    }
  };

  const handleLatitudeChange = (value: string) => {
    setLatitude(value);
    // 如果用户手动修改了经纬度，清除选中的搜索结果
    if (selectedSearchResult) {
      setSelectedSearchResult(null);
    }
  };

  // 开始地图选点
  const handleStartLocationPicking = () => {
    if (!mapInstance) {
      alert('地图未加载完成，请稍后再试');
      return;
    }

    if (isPickingLocation) {
      // 取消选点
      setIsPickingLocation(false);
      setDrawingMode(false); // 取消绘制模式
      
      // 重置鼠标样式
      if (mapInstance) {
        const canvas = mapInstance.getCanvas();
        if (canvas) {
          canvas.style.cursor = '';
          canvas.title = '';
        }
        
        // 移除地图点击监听器
        mapInstance.off('click', handleMapClick);
      }
      return;
    }

    // 如果正在绘制，先取消绘制
    if (isDrawing && terraDrawRef.current) {
      try {
        terraDrawRef.current.setMode('static');
        terraDrawRef.current.clear();
        setIsDrawing(false);
        setDrawingMode(false);
      } catch (error) {
        // 忽略清理错误
      }
    }

    // 开始选点
    setIsPickingLocation(true);
    setDrawingMode(true); // 启用绘制模式，禁用地图事件
    
    // 设置鼠标样式
    if (mapInstance) {
      const canvas = mapInstance.getCanvas();
      if (canvas) {
        canvas.style.cursor = 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'24\' height=\'24\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%23007bff\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\'%3E%3Cpath d=\'M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0z\'/%3E%3Ccircle cx=\'12\' cy=\'10\' r=\'3\'/%3E%3C/svg%3E") 12 12, pointer';
        canvas.title = '点击选择位置';
      }
      
      // 添加地图点击监听器
      mapInstance.on('click', handleMapClick);
    }
  };

  // 处理地图点击事件
  const handleMapClick = (e: any) => {
    // 阻止事件冒泡，防止触发其他地图事件
    e.originalEvent.stopPropagation();
    
    const { lng, lat } = e.lngLat;
    
    // 设置坐标
    setLongitude(lng.toFixed(6));
    setLatitude(lat.toFixed(6));
    
    // 结束选点
    setIsPickingLocation(false);
    setDrawingMode(false); // 取消绘制模式，恢复地图事件
    
    // 显示成功提示
    setSuccessMessage('位置选择成功！');
    setLocationPickSuccess(true);
    setTimeout(() => {
      setLocationPickSuccess(false);
      setSuccessMessage('');
    }, 2000);
    
    // 重置鼠标样式
    if (mapInstance) {
      const canvas = mapInstance.getCanvas();
      if (canvas) {
        canvas.style.cursor = '';
        canvas.title = '';
      }
      
      // 移除地图点击监听器
      mapInstance.off('click', handleMapClick);
    }
  };

  // 开始绘制
  const handleStartDrawing = () => {
    if (!map) {
      alert('地图未加载完成，请稍后再试');
      return;
    }

    if (!terraDrawRef.current) {
      alert('绘制工具未初始化，请稍后再试。如果问题持续，请刷新页面');
      return;
    }

    // 如果正在地图选点，先取消选点
    if (isPickingLocation) {
      setIsPickingLocation(false);
      if (mapInstance) {
        const canvas = mapInstance.getCanvas();
        if (canvas) {
          canvas.style.cursor = '';
          canvas.title = '';
        }
        mapInstance.off('click', handleMapClick);
      }
    }

    if (isDrawing) {
      // 取消绘制
      try {
        terraDrawRef.current.setMode('static');
        terraDrawRef.current.clear();
      } catch (error) {
        // 忽略清理错误
      } finally {
        setIsDrawing(false);
        setDrawingMode(false); // 禁用绘制模式，恢复地图事件
        
        // 立即重置鼠标样式
        const canvas = map.getCanvas();
        if (canvas) {
          canvas.style.cursor = '';
          canvas.title = '';
        }
      }
      return;
    }

    try {
      // 立即设置鼠标样式（不等待状态更新）
      const canvas = map.getCanvas();
      if (canvas) {
        canvas.style.cursor = 'crosshair';
      }
      
      // 设置状态
      setIsDrawing(true);
      setDrawingMode(true); // 启用绘制模式，禁用地图事件
      
      // 启动绘制模式
      terraDrawRef.current.setMode('circle');
    } catch (error) {
      console.error('启动绘制模式失败:', error);
      alert('启动绘制模式失败，请刷新页面重试');
      setIsDrawing(false);
      setDrawingMode(false); // 禁用绘制模式，恢复地图事件
      
      // 立即重置鼠标样式
      const canvas = map.getCanvas();
      if (canvas) {
        canvas.style.cursor = '';
      }
    }
  };

  // 手动设置筛选
  const handleManualFilter = () => {
    const lng = parseFloat(longitude);
    const lat = parseFloat(latitude);
    const r = parseFloat(radius || '10'); // 默认10公里

    if (isNaN(lng) || isNaN(lat)) {
      alert('请输入有效的经纬度');
      return;
    }

    if (isNaN(r) || r <= 0) {
      alert('请输入有效的半径（大于0）');
      return;
    }

    setSpatialFilter({
      center: [lng, lat] as [number, number],
      radius: r,
    });

    setShowManualFilter(false);
    
    // 显示成功提示
    setSuccessMessage('筛选应用成功！');
    setLocationPickSuccess(true);
    setTimeout(() => {
      setLocationPickSuccess(false);
      setSuccessMessage('');
    }, 2000);
  };

  // 清除筛选
  const handleClearFilter = () => {
    if (isDrawing && terraDrawRef.current) {
      try {
        terraDrawRef.current.setMode('static');
        terraDrawRef.current.clear();
        setIsDrawing(false);
        setDrawingMode(false); // 禁用绘制模式，恢复地图事件
        
        // 立即重置鼠标样式
        if (map) {
          const canvas = map.getCanvas();
          if (canvas) {
            canvas.style.cursor = '';
          }
        }
      } catch (error) {
        // 忽略清理错误，确保状态被正确重置
        setIsDrawing(false);
        setDrawingMode(false);
        
        if (map) {
          const canvas = map.getCanvas();
          if (canvas) {
            canvas.style.cursor = '';
          }
        }
      }
    }

    // 清理地图选点状态
    if (isPickingLocation && mapInstance) {
      setIsPickingLocation(false);
      setDrawingMode(false); // 取消绘制模式，恢复地图事件
      
      // 重置鼠标样式
      const canvas = mapInstance.getCanvas();
      if (canvas) {
        canvas.style.cursor = '';
        canvas.title = '';
      }
      
      // 移除地图点击监听器
      mapInstance.off('click', handleMapClick);
    }

    setSpatialFilter({
      center: null,
      radius: null,
    });
  };

  // 缩放到筛选区域
  const handleZoomToFilterArea = () => {
    if (!mapInstance || !filters.spatialFilter.center || !filters.spatialFilter.radius) {
      return;
    }

    const [lng, lat] = filters.spatialFilter.center;
    const radiusInKm = filters.spatialFilter.radius;

    // 计算包含圆形区域的边界框
    const center = turf.point([lng, lat]);
    const circle = turf.circle(center, radiusInKm, { units: 'kilometers' });
    const bbox = turf.bbox(circle);

    // 缩放到边界框，添加一些边距
    mapInstance.fitBounds(
      [[bbox[0], bbox[1]], [bbox[2], bbox[3]]], 
      {
        padding: 50,
        duration: 1000,
      }
    );
  };

  const hasActiveFilter = filters.spatialFilter.center && filters.spatialFilter.radius;

  return (
    <>
      <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-40">
        <style>
          {`
            .custom-slider::-webkit-slider-thumb {
              appearance: none;
              width: 20px;
              height: 20px;
              border-radius: 50%;
              background: #3b82f6;
              cursor: pointer;
              box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
              transition: all 0.2s ease;
              border: 2px solid #ffffff;
            }
            
            .custom-slider::-webkit-slider-thumb:hover {
              background: #2563eb;
              transform: scale(1.1);
              box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            
            .custom-slider::-moz-range-thumb {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              background: #3b82f6;
              cursor: pointer;
              border: 2px solid #ffffff;
              box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
              transition: all 0.2s ease;
            }
            
            .custom-slider::-moz-range-thumb:hover {
              background: #2563eb;
              transform: scale(1.1);
              box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            
            .custom-slider::-webkit-slider-track {
              height: 8px;
              border-radius: 4px;
              background: transparent;
            }
            
            .custom-slider::-moz-range-track {
              height: 8px;
              border-radius: 4px;
              background: transparent;
              border: none;
            }
          `}
        </style>
        <div className="bg-white bg-opacity-95 backdrop-blur-sm rounded-lg shadow-xl border border-slate-200 px-3 py-2">
          <div className="flex items-center space-x-2">
            {/* 绘制圆形按钮 */}
            <button
              onClick={handleStartDrawing}
              disabled={isInitializing}
              className={`p-2 rounded-md transition-colors ${
                isInitializing
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : isDrawing
                  ? 'bg-green-100 text-green-600'
                  : isPickingLocation
                  ? 'bg-blue-100 text-blue-600'
                  : 'hover:bg-slate-100 text-slate-600'
              }`}
              title={
                isInitializing
                  ? '绘制工具初始化中...'
                  : isDrawing
                  ? '点击取消绘制'
                  : isPickingLocation
                  ? '正在地图选点，点击切换到绘制模式'
                  : '点击开始绘制筛选圆形'
              }
            >
              {isInitializing ? (
                <div className="w-5 h-5 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
              ) : isDrawing ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              ) : isPickingLocation ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10" strokeWidth="2" strokeDasharray="2 2" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10" strokeWidth="2" strokeDasharray="2 2" />
                </svg>
              )}
            </button>

            {/* 手动筛选按钮 */}
            <div className="relative">
              <button
                ref={manualFilterButtonRef}
                onClick={() => setShowManualFilter(!showManualFilter)}
                className={`p-2 rounded-md transition-colors ${
                  showManualFilter
                    ? 'bg-blue-100 text-blue-600'
                    : 'hover:bg-slate-100 text-slate-600'
                }`}
                title="手动设置筛选"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              </button>
            </div>

            {/* 缩放到筛选区域按钮 */}
            {hasActiveFilter && (
              <button
                onClick={handleZoomToFilterArea}
                className="p-2 rounded-md text-blue-600 hover:bg-blue-50 transition-colors"
                title="缩放到筛选区域"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </button>
            )}

            {/* 清除筛选按钮 */}
            {hasActiveFilter && (
              <button
                onClick={handleClearFilter}
                className="p-2 rounded-md text-red-600 hover:bg-red-50 transition-colors"
                title="清除空间筛选"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}

            {/* 绘制状态显示 */}
            {isInitializing && (
              <div className="text-xs text-gray-600 px-2 py-1 bg-gray-50 rounded-md">
                绘制工具初始化中...
              </div>
            )}

            {isDrawing && !isInitializing && !isPickingLocation && (
              <div className="text-xs text-green-600 px-2 py-1 bg-green-50 rounded-md">
                点击地图绘制圆形筛选区域
              </div>
            )}

            {/* 筛选状态显示 */}
            {hasActiveFilter && !isDrawing && !isInitializing && !isPickingLocation && (
              <div className="text-xs text-slate-600 px-2 py-1 bg-blue-50 rounded-md">
                半径 {filters.spatialFilter.radius?.toFixed(1)}km
              </div>
            )}

            {/* 选中的搜索结果显示 */}
            {selectedSearchResult && !isDrawing && !isInitializing && !isPickingLocation && (
              <div className="flex items-center space-x-1 text-xs px-2 py-1 bg-blue-50 rounded-md">
                <div className={`w-2 h-2 rounded-full ${
                  selectedSearchResult.type === 'platform' ? 'bg-blue-500' : 'bg-green-500'
                }`}></div>
                <span className="text-slate-700 font-medium">{selectedSearchResult.name}</span>
                <span className="text-slate-500">
                  ({selectedSearchResult.type === 'platform' ? '井平台' : '监测台站'})
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 手动筛选Popover */}
      <div
        ref={popoverRef}
        className={`
          fixed z-51 bg-white rounded-lg shadow-xl border border-slate-200 p-4

          md:top-16 md:left-1/2 md:transform md:-translate-x-1/2 md:translate-y-2 md:w-80 md:max-h-[80vh] md:overflow-y-auto

          h-auto max-h-[80vh] overflow-y-auto rounded-t-lg rounded-b-none
          transform transition-transform duration-300 ease-out
          ${showManualFilter ? 'translate-y-0' : 'translate-y-full hidden'}
          ${isMobile && 'bottom-0 left-0 right-0'}
        `}
      >
        <h3 className="text-sm font-semibold text-slate-900 mb-3">空间筛选设置</h3>

        {/* 搜索框 */}
        <div className="mb-3">
          <label className="block text-xs text-slate-600 mb-1">搜索井平台或监测台站</label>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                handleSearch(e.target.value);
              }}
              placeholder="输入名称搜索..."
              className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {isSearching && (
              <div className="absolute right-3 top-2.5">
                <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
            )}
          </div>

          {/* 搜索结果 */}
          {searchResults.length > 0 && (
            <div className="mt-2 max-h-32 overflow-y-auto border border-slate-200 rounded-md">
              {searchResults.map((result) => (
                <button
                  key={`${result.type}-${result.id}`}
                  onClick={() => handleSelectSearchResult(result)}
                  className="w-full px-3 py-2 text-left text-sm hover:bg-slate-50 border-b border-slate-100 last:border-b-0 flex items-center space-x-2"
                >
                  <div className={`w-2 h-2 rounded-full ${
                    result.type === 'platform' ? 'bg-blue-500' : 'bg-green-500'
                  }`}></div>
                  <span className="flex-1">{result.name}</span>
                  <span className="text-xs text-slate-500">
                    {result.type === 'platform' ? '井平台' : '监测台站'}
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 选中的搜索结果显示 */}
        {selectedSearchResult && (
          <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  selectedSearchResult.type === 'platform' ? 'bg-blue-500' : 'bg-green-500'
                }`}></div>
                <div>
                  <div className="text-sm font-medium text-blue-900">
                    {selectedSearchResult.name}
                  </div>
                  <div className="text-xs text-blue-600">
                    {selectedSearchResult.type === 'platform' ? '井平台' : '监测台站'}
                  </div>
                </div>
              </div>
              <button
                onClick={() => setSelectedSearchResult(null)}
                className="text-blue-400 hover:text-blue-600 transition-colors"
                title="清除选择"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        )}

        {/* 坐标输入 */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <label className="text-xs text-slate-600">坐标位置</label>
            <div className="relative">
              <button
                onClick={handleStartLocationPicking}
                className={`px-2 py-1 text-xs rounded-md border transition-colors ${
                  isPickingLocation
                    ? 'bg-green-100 text-green-600 border-green-300'
                    : isDrawing
                    ? 'bg-blue-100 text-blue-600 border-blue-300'
                    : 'bg-white text-slate-600 border-slate-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600'
                }`}
                title={
                  isPickingLocation 
                    ? '取消地图选点' 
                    : isDrawing
                    ? '正在绘制圆形，点击切换到地图选点'
                    : '点击地图选择位置'
                }
              >
                {isPickingLocation ? (
                  <>
                    <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    取消选点
                  </>
                ) : isDrawing ? (
                  <>
                    <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    切换选点
                  </>
                ) : (
                  <>
                    <svg className="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    地图选点
                  </>
                )}
              </button>
              
              {/* 地图选点提示 */}
              {isPickingLocation && (
                <div className="absolute top-full right-0 mt-1 z-50">
                  <div className="bg-blue-600 text-white text-xs px-2 py-1 rounded-md shadow-lg whitespace-nowrap">
                    <div className="relative">
                      点击地图选择中心位置
                      {/* 小三角箭头 */}
                      <div className="absolute bottom-full right-2 w-0 h-0 border-l-[4px] border-r-[4px] border-b-[4px] border-l-transparent border-r-transparent border-b-blue-600"></div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* 成功提示 */}
              {locationPickSuccess && (
                <div className="absolute top-full right-0 mt-1 z-50">
                  <div className="bg-green-600 text-white text-xs px-2 py-1 rounded-md shadow-lg whitespace-nowrap animate-pulse">
                    <div className="relative">
                      ✓ {successMessage}
                      {/* 小三角箭头 */}
                      <div className="absolute bottom-full right-2 w-0 h-0 border-l-[4px] border-r-[4px] border-b-[4px] border-l-transparent border-r-transparent border-b-green-600"></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-slate-600 mb-1">经度</label>
              <input
                type="number"
                value={longitude}
                onChange={(e) => handleLongitudeChange(e.target.value)}
                placeholder="105.45"
                step="0.000001"
                className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-xs text-slate-600 mb-1">纬度</label>
              <input
                type="number"
                value={latitude}
                onChange={(e) => handleLatitudeChange(e.target.value)}
                placeholder="29.17"
                step="0.000001"
                className="w-full px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* 半径输入 */}
        <div className="mb-4">
          <label className="block text-xs text-slate-600 mb-1">半径 (公里)</label>
          
          {/* 滑块和输入框组合 */}
          <div className="space-y-3">
            {/* 滑块 */}
            <div className="relative px-1">
              <input
                type="range"
                min="0.1"
                max="50"
                step="0.1"
                value={radius}
                onChange={(e) => setRadius(e.target.value)}
                className="w-full h-2 bg-gradient-to-r from-slate-200 to-slate-300 rounded-lg appearance-none cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 custom-slider"
                style={{
                  background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${((parseFloat(radius) || 10) / 50) * 100}%, #e2e8f0 ${((parseFloat(radius) || 10) / 50) * 100}%, #e2e8f0 100%)`,
                  boxShadow: 'inset 0 1px 2px rgba(0, 0, 0, 0.1)'
                }}
              />
              
              {/* 滑块标记 */}
              <div className="relative text-xs mt-1 h-4">
                <button
                  onClick={() => setRadius('0.1')}
                  className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
                  style={{ left: '0%', transform: 'translateX(-50%)' }}
                  title="设置为0.1km"
                >
                  0.1
                </button>
                <button
                  onClick={() => setRadius('10')}
                  className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
                  style={{ left: `${((10 - 0.1) / (50 - 0.1)) * 100}%`, transform: 'translateX(-50%)' }}
                  title="设置为10km"
                >
                  10
                </button>
                <button
                  onClick={() => setRadius('20')}
                  className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
                  style={{ left: `${((20 - 0.1) / (50 - 0.1)) * 100}%`, transform: 'translateX(-50%)' }}
                  title="设置为20km"
                >
                  20
                </button>
                <button
                  onClick={() => setRadius('30')}
                  className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
                  style={{ left: `${((30 - 0.1) / (50 - 0.1)) * 100}%`, transform: 'translateX(-50%)' }}
                  title="设置为30km"
                >
                  30
                </button>
                <button
                  onClick={() => setRadius('50')}
                  className="absolute text-slate-400 hover:text-blue-600 hover:font-medium transition-colors cursor-pointer"
                  style={{ left: '100%', transform: 'translateX(-50%)' }}
                  title="设置为50km"
                >
                  50
                </button>
              </div>
              
              {/* 当前值指示器 */}
              <div
                className="absolute top-[-32px] bg-blue-600 text-white text-xs px-2 py-1 rounded-md shadow-lg pointer-events-none"
                style={{
                  left: `${((parseFloat(radius) || 10) - 0.1) / (50 - 0.1) * 100}%`,
                  opacity: 1,
                  transform: 'translateX(-50%) translateY(0)',
                }}
              >
                <div className="relative">
                  {parseFloat(radius).toFixed(1)}km
                  {/* 小三角箭头 */}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[4px] border-r-[4px] border-t-[4px] border-l-transparent border-r-transparent border-t-blue-600"></div>
                </div>
              </div>
            </div>
            
            {/* 输入框 */}
            <div className="flex items-center space-x-2">
              <input
                type="number"
                value={radius}
                onChange={(e) => setRadius(e.target.value)}
                placeholder="10"
                min="0.1"
                max="50"
                step="0.1"
                className="flex-1 px-3 py-2 text-sm border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <span className="text-xs text-slate-500 whitespace-nowrap">公里</span>
            </div>
            
            {/* 快速选择按钮 */}
            <div className="flex space-x-1">
              {[1, 5, 10, 20, 30].map((value) => (
                <button
                  key={value}
                  onClick={() => setRadius(value.toString())}
                  className={`px-3 py-1.5 text-xs rounded-full border transition-all duration-200 font-medium ${
                    parseFloat(radius || '10') === value
                      ? 'bg-blue-600 text-white border-blue-600 shadow-md transform scale-105'
                      : 'bg-white text-slate-600 border-slate-300 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 hover:shadow-sm'
                  }`}
                >
                  {value}km
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          <button
            onClick={handleManualFilter}
            className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            应用筛选
          </button>
          <button
            onClick={() => setShowManualFilter(false)}
            className="px-3 py-2 text-sm text-slate-600 border border-slate-300 rounded-md hover:bg-slate-50 transition-colors"
          >
            取消
          </button>
        </div>
      </div>
            
    </>
  );
}
