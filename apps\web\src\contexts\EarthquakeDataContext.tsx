import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { earthquakeApi } from '../services/api';
import { useMapStore } from '../stores/useMapStore';
import type { Earthquake, EarthquakeQuery, EarthquakeStats, EarthquakeLayerData } from '../types';
import * as turf from '@turf/turf';

interface EarthquakeDataContextType {
  earthquakes: Earthquake[];
  allEarthquakes: Earthquake[]; // 完整的数据集
  layerData: EarthquakeLayerData[];
  earthquakesByMagnitude: {
    small: Earthquake[];
    medium: Earthquake[];
    large: Earthquake[];
  };
  latestEarthquakes: Earthquake[];
  stats: EarthquakeStats | null;
  total: number;
  loading: boolean;
  error: string | null;
  retryCount: number;
  dataTimeRange: { start: Date; end: Date } | null;
  isInitialized: boolean;
  isTimelinePlayback: boolean;
  setTimelinePlayback: (isPlayback: boolean) => void;
  refetch: (params?: EarthquakeQuery, retry?: number, skipTimeFilter?: boolean) => Promise<void>;
  fetchStats: () => Promise<void>;
  updateDataTimeRange: (start: Date, end: Date) => Promise<void>; // 更新数据时间范围
}

const EarthquakeDataContext = createContext<EarthquakeDataContextType | undefined>(undefined);

export function EarthquakeDataProvider({ children }: { children: React.ReactNode }) {
  const [allEarthquakes, setAllEarthquakes] = useState<Earthquake[]>([]); // 完整数据集
  const [earthquakes, setEarthquakes] = useState<Earthquake[]>([]); // 过滤后的数据
  const [stats, setStats] = useState<EarthquakeStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [retryCount, setRetryCount] = useState(0);
  const [dataTimeRange, setDataTimeRange] = useState<{ start: Date, end: Date } | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isTimelinePlayback, setIsTimelinePlayback] = useState(false);

  const { filters, setTimeRange } = useMapStore();

  // 获取地震事件数据（带重试机制）
  const fetchEarthquakes = useCallback(async (params: EarthquakeQuery = {}, retry = 0, skipTimeFilter = false) => {
    setLoading(true);
    setError(null);

    try {
      // 计算时间窗口（默认半年）
      const now = new Date();
      const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);

      // 应用筛选器
      const queryParams: EarthquakeQuery = {
        ...params,
        // 使用时间窗口而不是固定限制
        start_date: skipTimeFilter
          ? sixMonthsAgo.toISOString().split('T')[0] // 初始化时使用半年时间窗口
          : params.start_date || sixMonthsAgo.toISOString().split('T')[0],
        end_date: skipTimeFilter
          ? now.toISOString().split('T')[0] // 初始化时使用当前时间
          : params.end_date || now.toISOString().split('T')[0],
        min_magnitude: params.min_magnitude !== undefined ? params.min_magnitude : -2,
        max_magnitude: params.max_magnitude,
        // 不传limit参数表示返回所有数据
        ...(params.limit !== undefined && { limit: params.limit }),
      };

      const response = await earthquakeApi.getEarthquakes(queryParams);

      if (response.success && response.data) {
        setAllEarthquakes(response.data.earthquakes); // 保存完整数据集
        setEarthquakes(response.data.earthquakes); // 初始时显示全部数据
        setTotal(response.data.total);
        setRetryCount(0); // 重置重试计数

        // 如果是初始化且有数据，计算并设置数据时间范围
        if (!isInitialized && response.data.earthquakes.length > 0) {
          const times = response.data.earthquakes.map(eq => new Date(eq.occurred_at));
          const validTimes = times.filter(t => !isNaN(t.getTime()));

          if (validTimes.length > 0) {
            const start = new Date(Math.min(...validTimes.map(t => t.getTime())));
            const end = new Date(Math.max(...validTimes.map(t => t.getTime())));

            setDataTimeRange({ start, end });

            // 设置初始时间范围为当前请求的时间窗口（半年）
            const now = new Date();
            const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);

            setTimeRange({
              start: sixMonthsAgo,
              end: now
            });
          }
          setIsInitialized(true);
        }
      } else {
        throw new Error(response.error || '获取地震数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络请求失败';

      // 重试机制：最多重试2次
      if (retry < 2) {
        setRetryCount(retry + 1);
        setTimeout(() => {
          fetchEarthquakes(params, retry + 1, skipTimeFilter);
        }, 1000 * (retry + 1));
        return;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [isInitialized, setTimeRange]);

  // 获取统计数据
  const fetchStats = useCallback(async () => {
    try {
      const response = await earthquakeApi.getEarthquakeStats();
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (err) {
    }
  }, []);

  // 更新数据时间范围（重新请求数据）
  const updateDataTimeRange = useCallback(async (start: Date, end: Date) => {
    // 重新请求数据
    await fetchEarthquakes({
      start_date: start.toISOString().split('T')[0],
      end_date: end.toISOString().split('T')[0],
    }, 0, false);

    // 更新数据时间范围状态
    setDataTimeRange({ start, end });

    // 同步更新useMapStore中的时间范围过滤器
    setTimeRange({ start, end });
  }, [fetchEarthquakes, setTimeRange]);

  // 应用所有过滤器到数据（不重新请求）
  const applyAllFilters = useCallback(() => {
    if (!allEarthquakes.length) return;

    const filtered = allEarthquakes.filter(eq => {
      // 时间过滤
      if (filters.timeRange) {
        const eqTime = new Date(eq.occurred_at);
        if (eqTime < filters.timeRange.start || eqTime > filters.timeRange.end) {
          return false;
        }
      }

      // 震级过滤
      if (filters.magnitudeRange) {
        if (eq.magnitude < filters.magnitudeRange[0] || eq.magnitude > filters.magnitudeRange[1]) {
          return false;
        }
      }

      // 深度过滤
      if (filters.depthRange) {
        if (eq.depth < filters.depthRange[0] || eq.depth > filters.depthRange[1]) {
          return false;
        }
      }

      // 区域过滤
      if (filters.selectedRegions && filters.selectedRegions.length > 0) {
        if (!filters.selectedRegions.includes(eq.region)) {
          return false;
        }
      }

      // 空间筛选
      if (filters.spatialFilter && filters.spatialFilter.center && filters.spatialFilter.radius) {
        const distance = turf.distance(
          [eq.longitude, eq.latitude],
          filters.spatialFilter.center,
          { units: 'kilometers' }
        );
        if (distance > filters.spatialFilter.radius) {
          return false;
        }
      }

      return true;
    });

    setEarthquakes(filtered);
  }, [allEarthquakes, filters.timeRange, filters.magnitudeRange, filters.depthRange, filters.selectedRegions, filters.spatialFilter]);

  // 根据时间范围过滤数据（不重新请求）- 保持向后兼容
  const filterEarthquakesByTimeRange = useCallback((timeRange: { start: Date; end: Date }) => {
    applyAllFilters();
  }, [applyAllFilters]);

  // 转换为地图图层数据格式
  const layerData: EarthquakeLayerData[] = earthquakes.map(eq => ({
    id: eq.event_id,
    coordinates: [eq.longitude, eq.latitude] as [number, number],
    magnitude: eq.magnitude,
    depth: eq.depth,
    time: eq.occurred_at,
    eventId: eq.event_id,
  }));

  // 按震级分类的数据
  const earthquakesByMagnitude = {
    small: earthquakes.filter(eq => eq.magnitude < 2.0),
    medium: earthquakes.filter(eq => eq.magnitude >= 2.0 && eq.magnitude < 4.0),
    large: earthquakes.filter(eq => eq.magnitude >= 4.0),
  };

  // 最新地震事件
  const latestEarthquakes = earthquakes
    .sort((a, b) => new Date(b.occurred_at).getTime() - new Date(a.occurred_at).getTime())
    .slice(0, 10);

  // 初始化数据（首次不应用时间筛选）
  useEffect(() => {
    if (!isInitialized) {
      fetchEarthquakes({}, 0, true); // 跳过时间筛选
    }
  }, [isInitialized]); // 只依赖 isInitialized

  // 时间范围筛选器处理（仅在时间轴播放模式下）
  useEffect(() => {
    if (!isInitialized || !isTimelinePlayback || !filters.timeRange) return;

    // 时间轴播放模式：使用过滤器，不重新请求数据
    filterEarthquakesByTimeRange(filters.timeRange);
  }, [filters.timeRange, isInitialized, isTimelinePlayback, filterEarthquakesByTimeRange]);

  // 监听所有过滤器变化（非时间轴播放模式下）
  useEffect(() => {
    if (!isInitialized || isTimelinePlayback) return;

    // 非时间轴播放模式：应用所有过滤器
    applyAllFilters();
  }, [filters.magnitudeRange, filters.depthRange, filters.selectedRegions, filters.spatialFilter, isInitialized, isTimelinePlayback, applyAllFilters]);

  // 获取统计数据（只在初始化时获取一次）
  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const value: EarthquakeDataContextType = {
    earthquakes,
    allEarthquakes,
    layerData,
    earthquakesByMagnitude,
    latestEarthquakes,
    stats,
    total,
    loading,
    error,
    retryCount,
    dataTimeRange,
    isInitialized,
    isTimelinePlayback,
    setTimelinePlayback: setIsTimelinePlayback,
    refetch: fetchEarthquakes,
    fetchStats,
    updateDataTimeRange,
  };

  return (
    <EarthquakeDataContext.Provider value={value}>
      {children}
    </EarthquakeDataContext.Provider>
  );
}

export function useEarthquakeData() {
  const context = useContext(EarthquakeDataContext);
  if (context === undefined) {
    throw new Error('useEarthquakeData must be used within an EarthquakeDataProvider');
  }
  return context;
}
