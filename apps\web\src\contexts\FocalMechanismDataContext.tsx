import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { focalMechanismApi } from '../services/api';
import { useMapStore } from '../stores/useMapStore';
import type { FocalMechanism, FocalMechanismQuery, FocalMechanismStats, FocalMechanismLayerData } from '../types';
import * as turf from '@turf/turf';

interface FocalMechanismDataContextType {
  focalMechanisms: FocalMechanism[];
  allFocalMechanisms: FocalMechanism[]; // 完整的数据集
  layerData: FocalMechanismLayerData[];
  focalMechanismsByMagnitude: {
    small: FocalMechanism[];
    medium: FocalMechanism[];
    large: FocalMechanism[];
  };
  latestFocalMechanisms: FocalMechanism[];
  stats: FocalMechanismStats | null;
  total: number;
  loading: boolean;
  error: string | null;
  retryCount: number;
  dataTimeRange: { start: Date; end: Date } | null;
  isInitialized: boolean;
  isTimelinePlayback: boolean;
  setTimelinePlayback: (isPlayback: boolean) => void;
  refetch: (params?: FocalMechanismQuery, retry?: number, skipTimeFilter?: boolean) => Promise<void>;
  fetchStats: () => Promise<void>;
  updateDataTimeRange: (start: Date, end: Date) => Promise<void>; // 更新数据时间范围
}

const FocalMechanismDataContext = createContext<FocalMechanismDataContextType | undefined>(undefined);

export function FocalMechanismDataProvider({ children }: { children: React.ReactNode }) {
  const [allFocalMechanisms, setAllFocalMechanisms] = useState<FocalMechanism[]>([]); // 完整数据集
  const [focalMechanisms, setFocalMechanisms] = useState<FocalMechanism[]>([]); // 过滤后的数据
  const [stats, setStats] = useState<FocalMechanismStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [retryCount, setRetryCount] = useState(0);
  const [dataTimeRange, setDataTimeRange] = useState<{ start: Date, end: Date } | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isTimelinePlayback, setIsTimelinePlayback] = useState(false);

  const { filters, setTimeRange } = useMapStore();

  // 获取震源机制解数据（带重试机制）
  const fetchFocalMechanisms = useCallback(async (params: FocalMechanismQuery = {}, retry = 0, skipTimeFilter = false) => {
    setLoading(true);
    setError(null);

    try {
      // 计算时间窗口（默认半年）
      const now = new Date();
      const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);

      // 应用筛选器
      const queryParams: FocalMechanismQuery = {
        ...params,
        // 使用时间窗口而不是固定限制
        start_date: skipTimeFilter
          ? sixMonthsAgo.toISOString().split('T')[0] // 初始化时使用半年时间窗口
          : params.start_date || sixMonthsAgo.toISOString().split('T')[0],
        end_date: skipTimeFilter
          ? now.toISOString().split('T')[0] // 初始化时使用当前时间
          : params.end_date || now.toISOString().split('T')[0],
        min_magnitude: params.min_magnitude !== undefined ? params.min_magnitude : -2,
        max_magnitude: params.max_magnitude,
        // 不传limit参数表示返回所有数据
        ...(params.limit !== undefined && { limit: params.limit }),
      };

      const response = await focalMechanismApi.getFocalMechanisms(queryParams);

      if (response.success && response.data) {
        setAllFocalMechanisms(response.data.focalMechanisms); // 保存完整数据集
        setFocalMechanisms(response.data.focalMechanisms); // 初始时显示全部数据
        setTotal(response.data.total);
        setRetryCount(0); // 重置重试计数

        // 如果是初始化且有数据，计算并设置数据时间范围
        if (!isInitialized && response.data.focalMechanisms.length > 0) {
          const times = response.data.focalMechanisms.map(fm => new Date(`${fm.date}T${fm.time}`));
          const validTimes = times.filter(t => !isNaN(t.getTime()));

          if (validTimes.length > 0) {
            const start = new Date(Math.min(...validTimes.map(t => t.getTime())));
            const end = new Date(Math.max(...validTimes.map(t => t.getTime())));

            setDataTimeRange({ start, end });

            // 设置初始时间范围为当前请求的时间窗口（半年）
            const now = new Date();
            const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);

            setTimeRange({
              start: sixMonthsAgo,
              end: now
            });
          }
          setIsInitialized(true);
        }
      } else {
        throw new Error(response.error || '获取震源机制解数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络请求失败';

      // 重试机制：最多重试2次
      if (retry < 2) {
        setRetryCount(retry + 1);
        setTimeout(() => {
          fetchFocalMechanisms(params, retry + 1, skipTimeFilter);
        }, 1000 * (retry + 1));
        return;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [isInitialized, setTimeRange]);

  // 获取统计数据
  const fetchStats = useCallback(async () => {
    try {
      const response = await focalMechanismApi.getFocalMechanismStats();
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (err) {
      // 静默处理错误
    }
  }, []);

  // 更新数据时间范围（重新请求数据）
  const updateDataTimeRange = useCallback(async (start: Date, end: Date) => {
    // 重新请求数据
    await fetchFocalMechanisms({
      start_date: start.toISOString().split('T')[0],
      end_date: end.toISOString().split('T')[0],
    }, 0, false);

    // 更新数据时间范围状态
    setDataTimeRange({ start, end });

    // 同步更新useMapStore中的时间范围过滤器
    setTimeRange({ start, end });
  }, [fetchFocalMechanisms, setTimeRange]);

  // 应用所有过滤器到数据（不重新请求）
  const applyAllFilters = useCallback(() => {
    if (!allFocalMechanisms.length) return;

    const filtered = allFocalMechanisms.filter(fm => {
      // 时间过滤
      if (filters.timeRange) {
        const fmTime = new Date(`${fm.date}T${fm.time}`);
        if (fmTime.getTime() < filters.timeRange.start.getTime() || fmTime.getTime() > filters.timeRange.end.getTime()) {
          return false;
        }
      }

      // 震级过滤
      if (filters.magnitudeRange) {
        if (fm.magnitude < filters.magnitudeRange[0] || fm.magnitude > filters.magnitudeRange[1]) {
          return false;
        }
      }

      // 深度过滤
      if (filters.depthRange) {
        if (fm.depth < filters.depthRange[0] || fm.depth > filters.depthRange[1]) {
          return false;
        }
      }

      // 区域过滤
      if (filters.selectedRegions && filters.selectedRegions.length > 0) {
        if (!filters.selectedRegions.includes(fm.region)) {
          return false;
        }
      }

      // 空间筛选
      if (filters.spatialFilter && filters.spatialFilter.center && filters.spatialFilter.radius) {
        const distance = turf.distance(
          [fm.longitude, fm.latitude],
          filters.spatialFilter.center,
          { units: 'kilometers' }
        );
        if (distance > filters.spatialFilter.radius) {
          return false;
        }
      }
      return true;
    });
    setFocalMechanisms(filtered);
  }, [allFocalMechanisms, filters.timeRange, filters.magnitudeRange, filters.depthRange, filters.selectedRegions, filters.spatialFilter]);

  // 根据时间范围过滤数据（不重新请求）- 保持向后兼容
  const filterFocalMechanismsByTimeRange = useCallback((timeRange: { start: Date; end: Date }) => {
    applyAllFilters();
  }, [applyAllFilters]);

  // 转换为地图图层数据格式
  const layerData: FocalMechanismLayerData[] = focalMechanisms.map(fm => ({
    id: String(fm.id),
    coordinates: [fm.longitude, fm.latitude] as [number, number],
    magnitude: fm.magnitude,
    depth: fm.depth,
    time: `${fm.date}T${fm.time}`,
    strike1: fm.strike1,
    dip1: fm.dip1,
    rake1: fm.rake1,
    strike2: fm.strike2,
    dip2: fm.dip2,
    rake2: fm.rake2,
    misfit: fm.misfit,
  }));

  // 按震级分类的数据
  const focalMechanismsByMagnitude = {
    small: focalMechanisms.filter(fm => fm.magnitude < 2.0),
    medium: focalMechanisms.filter(fm => fm.magnitude >= 2.0 && fm.magnitude < 4.0),
    large: focalMechanisms.filter(fm => fm.magnitude >= 4.0),
  };

  // 最新震源机制解
  const latestFocalMechanisms = focalMechanisms
    .sort((a, b) => {
      const timeA = new Date(`${a.date}T${a.time}`).getTime();
      const timeB = new Date(`${b.date}T${b.time}`).getTime();
      return timeB - timeA;
    })
    .slice(0, 10);

  // 初始化数据（首次不应用时间筛选）
  useEffect(() => {
    if (!isInitialized) {
      fetchFocalMechanisms({}, 0, true); // 跳过时间筛选
    }
  }, [isInitialized]); // 只依赖 isInitialized

  // 时间范围筛选器处理（仅在时间轴播放模式下）
  useEffect(() => {
    if (!isInitialized || !isTimelinePlayback || !filters.timeRange) return;

    // 时间轴播放模式：使用过滤器，不重新请求数据
    filterFocalMechanismsByTimeRange(filters.timeRange);
  }, [filters.timeRange, isInitialized, isTimelinePlayback, filterFocalMechanismsByTimeRange]);

  // 监听所有过滤器变化（非时间轴播放模式下）
  useEffect(() => {
    if (!isInitialized || isTimelinePlayback) return;

    // 非时间轴播放模式：应用所有过滤器
    applyAllFilters();
  }, [filters.magnitudeRange, filters.depthRange, filters.selectedRegions, filters.spatialFilter, isInitialized, isTimelinePlayback, applyAllFilters]);

  // 获取统计数据（只在初始化时获取一次）
  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const value: FocalMechanismDataContextType = {
    focalMechanisms,
    allFocalMechanisms,
    layerData,
    focalMechanismsByMagnitude,
    latestFocalMechanisms,
    stats,
    total,
    loading,
    error,
    retryCount,
    dataTimeRange,
    isInitialized,
    isTimelinePlayback,
    setTimelinePlayback: setIsTimelinePlayback,
    refetch: fetchFocalMechanisms,
    fetchStats,
    updateDataTimeRange,
  };

  return (
    <FocalMechanismDataContext.Provider value={value}>
      {children}
    </FocalMechanismDataContext.Provider>
  );
}

export function useFocalMechanismData() {
  const context = useContext(FocalMechanismDataContext);
  if (context === undefined) {
    throw new Error('useFocalMechanismData must be used within a FocalMechanismDataProvider');
  }
  return context;
} 