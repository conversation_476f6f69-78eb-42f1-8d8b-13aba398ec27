import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { faultApi, wellApi, stationApi, platformApi } from '../services/api';
import type {
  Fault,
  WellTrajectory,
  WellPlatform,
  Station,
  FaultLayerData,
  WellLayerData,
  StationLayerData
} from '../types';

interface LayerDataContextType {
  // 断层数据
  faults: Fault[];
  faultLayerData: FaultLayerData[];
  faultsByLevel: {
    level1: FaultLayerData[];
    level2: FaultLayerData[];
    level3: FaultLayerData[];
  };
  faultsLoading: boolean;
  faultsError: string | null;

  // 井轨迹数据
  wells: WellTrajectory[];
  wellLayerData: WellLayerData[];
  wellsLoading: boolean;
  wellsError: string | null;

  // 井平台数据
  platforms: WellPlatform[];
  platformsLoading: boolean;
  platformsError: string | null;

  // 台站数据
  stations: Station[];
  stationLayerData: StationLayerData[];
  stationsByStatus: {
    active: StationLayerData[];
    inactive: StationLayerData[];
    maintenance: StationLayerData[];
  };
  stationsLoading: boolean;
  stationsError: string | null;

  // 全局状态
  loading: boolean;
  error: string | null;
  isInitialized: boolean;

  // 方法
  refetchFaults: () => Promise<void>;
  refetchWells: () => Promise<void>;
  refetchPlatforms: () => Promise<void>;
  refetchStations: () => Promise<void>;
  refetchAll: () => Promise<void>;
}

const LayerDataContext = createContext<LayerDataContextType | undefined>(undefined);

export function LayerDataProvider({ children }: { children: React.ReactNode }) {
  // 断层数据状态
  const [faults, setFaults] = useState<Fault[]>([]);
  const [faultsLoading, setFaultsLoading] = useState(false);
  const [faultsError, setFaultsError] = useState<string | null>(null);

  // 井轨迹数据状态
  const [wells, setWells] = useState<WellTrajectory[]>([]);
  const [wellsLoading, setWellsLoading] = useState(false);
  const [wellsError, setWellsError] = useState<string | null>(null);

  // 井平台数据状态
  const [platforms, setPlatforms] = useState<WellPlatform[]>([]);
  const [platformsLoading, setPlatformsLoading] = useState(false);
  const [platformsError, setPlatformsError] = useState<string | null>(null);

  // 台站数据状态
  const [stations, setStations] = useState<Station[]>([]);
  const [stationsLoading, setStationsLoading] = useState(false);
  const [stationsError, setStationsError] = useState<string | null>(null);

  // 全局状态
  const [isInitialized, setIsInitialized] = useState(false);

  // 获取断层数据
  const fetchFaults = useCallback(async () => {
    if (faultsLoading) return; // 防止重复请求
    
    setFaultsLoading(true);
    setFaultsError(null);

    try {
      // 获取所有等级的断层数据
      const [level1, level2, level3] = await Promise.all([
        faultApi.getFaultsByLevel(1),
        faultApi.getFaultsByLevel(2),
        faultApi.getFaultsByLevel(3),
      ]);

      const allFaults = [
        ...(level1.success ? level1.data || [] : []),
        ...(level2.success ? level2.data || [] : []),
        ...(level3.success ? level3.data || [] : []),
      ];

      setFaults(allFaults);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取断层数据失败';
      setFaultsError(errorMessage);
      console.error('❌ LayerDataProvider 获取断层数据失败:', errorMessage);
    } finally {
      setFaultsLoading(false);
    }
  }, [faultsLoading]);

  // 获取井轨迹数据
  const fetchWells = useCallback(async () => {
    if (wellsLoading) return; // 防止重复请求
    
    setWellsLoading(true);
    setWellsError(null);

    try {
      const response = await wellApi.getWells({ limit: 1000 });
      if (response.success && response.data) {
        setWells(response.data.wells);
      } else {
        throw new Error(response.error || '获取井轨迹数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取井轨迹数据失败';
      setWellsError(errorMessage);
      console.error('❌ LayerDataProvider 获取井轨迹数据失败:', errorMessage);
    } finally {
      setWellsLoading(false);
    }
  }, [wellsLoading]);

  // 获取井平台数据
  const fetchPlatforms = useCallback(async () => {
    if (platformsLoading) return; // 防止重复请求

    setPlatformsLoading(true);
    setPlatformsError(null);

    try {
      const response = await platformApi.getPlatforms({ limit: 1000 });
      if (response.success && response.data) {
        setPlatforms(response.data.platforms);
        console.log(`✅ LayerDataProvider 成功获取 ${response.data.platforms.length} 条井平台数据`);
      } else {
        throw new Error(response.error || '获取井平台数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取井平台数据失败';
      setPlatformsError(errorMessage);
      console.error('❌ LayerDataProvider 获取井平台数据失败:', errorMessage);
    } finally {
      setPlatformsLoading(false);
    }
  }, [platformsLoading]);

  // 获取台站数据
  const fetchStations = useCallback(async () => {
    if (stationsLoading) return; // 防止重复请求
    
    setStationsLoading(true);
    setStationsError(null);

    try {
      const response = await stationApi.getStations({ limit: 100 });
      if (response.success && response.data) {
        setStations(response.data.stations);
        console.log(`✅ LayerDataProvider 成功获取 ${response.data.stations.length} 条台站数据`);
      } else {
        throw new Error(response.error || '获取台站数据失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取台站数据失败';
      setStationsError(errorMessage);
      console.error('❌ LayerDataProvider 获取台站数据失败:', errorMessage);
    } finally {
      setStationsLoading(false);
    }
  }, [stationsLoading]);

  // 初始化数据加载
  useEffect(() => {
    if (!isInitialized) {
      Promise.all([
        fetchFaults(),
        fetchWells(),
        fetchPlatforms(),
        fetchStations(),
      ]).finally(() => {
        setIsInitialized(true);
      });
    }
  }, [isInitialized, fetchFaults, fetchWells, fetchPlatforms, fetchStations]);

  // 转换为地图图层数据格式
  const faultLayerData: FaultLayerData[] = faults.map(fault => ({
    id: fault.id.toString(),
    coordinates: JSON.parse(fault.coordinates) as Array<[number, number]>,
    level: fault.level,
    name: fault.name,
  }));

  const wellLayerData: WellLayerData[] = wells.map(well => {
    const platform = platforms.find(p => p.id === well.platform_id);
    return {
      id: well.id.toString(),
      coordinates: JSON.parse(well.coordinates) as Array<[number, number]>,
      platformName: platform?.name || '未知井平台',
    };
  });

  const stationLayerData: StationLayerData[] = stations.map(station => ({
    id: station.id.toString(),
    coordinates: [station.longitude, station.latitude] as [number, number],
    name: station.name,
    status: station.status,
  }));

  // 按等级分组的断层数据
  const faultsByLevel = {
    level1: faultLayerData.filter(f => f.level === 1),
    level2: faultLayerData.filter(f => f.level === 2),
    level3: faultLayerData.filter(f => f.level === 3),
  };

  // 按状态分组的台站数据
  const stationsByStatus = {
    active: stationLayerData.filter(s => s.status === 'active'),
    inactive: stationLayerData.filter(s => s.status === 'inactive'),
    maintenance: stationLayerData.filter(s => s.status === 'maintenance'),
  };

  // 全局状态
  const loading = faultsLoading || wellsLoading || platformsLoading || stationsLoading;
  const error = faultsError || wellsError || platformsError || stationsError;

  // 重新获取所有数据
  const refetchAll = useCallback(async () => {
    await Promise.all([
      fetchFaults(),
      fetchWells(),
      fetchPlatforms(),
      fetchStations(),
    ]);
  }, [fetchFaults, fetchWells, fetchPlatforms, fetchStations]);

  const value: LayerDataContextType = {
    // 断层数据
    faults,
    faultLayerData,
    faultsByLevel,
    faultsLoading,
    faultsError,

    // 井轨迹数据
    wells,
    wellLayerData,
    wellsLoading,
    wellsError,

    // 井平台数据
    platforms,
    platformsLoading,
    platformsError,

    // 台站数据
    stations,
    stationLayerData,
    stationsByStatus,
    stationsLoading,
    stationsError,

    // 全局状态
    loading,
    error,
    isInitialized,

    // 方法
    refetchFaults: fetchFaults,
    refetchWells: fetchWells,
    refetchPlatforms: fetchPlatforms,
    refetchStations: fetchStations,
    refetchAll,
  };

  return (
    <LayerDataContext.Provider value={value}>
      {children}
    </LayerDataContext.Provider>
  );
}

export function useLayerData() {
  const context = useContext(LayerDataContext);
  if (context === undefined) {
    throw new Error('useLayerData must be used within a LayerDataProvider');
  }
  return context;
}

// 为了保持向后兼容性，提供单独的hooks
export function useFaultData() {
  const { faults, faultLayerData, faultsByLevel, faultsLoading, faultsError, refetchFaults } = useLayerData();
  return {
    faults,
    layerData: faultLayerData,
    faultsByLevel,
    loading: faultsLoading,
    error: faultsError,
    refetch: refetchFaults,
  };
}

export function useWellData() {
  const { wells, wellLayerData, wellsLoading, wellsError, refetchWells } = useLayerData();
  return {
    wells,
    layerData: wellLayerData,
    loading: wellsLoading,
    error: wellsError,
    refetch: refetchWells,
  };
}

export function useStationData() {
  const { stations, stationLayerData, stationsByStatus, stationsLoading, stationsError, refetchStations } = useLayerData();
  return {
    stations,
    layerData: stationLayerData,
    stationsByStatus,
    loading: stationsLoading,
    error: stationsError,
    refetch: refetchStations,
  };
}

export function usePlatformData() {
  const { platforms, platformsLoading, platformsError, refetchPlatforms } = useLayerData();
  return {
    platforms,
    loading: platformsLoading,
    error: platformsError,
    refetch: refetchPlatforms,
  };
}

export function useAllLayerData() {
  const layerData = useLayerData();

  return {
    faults: {
      faults: layerData.faults,
      layerData: layerData.faultLayerData,
      faultsByLevel: layerData.faultsByLevel,
      loading: layerData.faultsLoading,
      error: layerData.faultsError,
      refetch: layerData.refetchFaults,
    },
    wells: {
      wells: layerData.wells,
      layerData: layerData.wellLayerData,
      loading: layerData.wellsLoading,
      error: layerData.wellsError,
      refetch: layerData.refetchWells,
    },
    stations: {
      stations: layerData.stations,
      layerData: layerData.stationLayerData,
      stationsByStatus: layerData.stationsByStatus,
      loading: layerData.stationsLoading,
      error: layerData.stationsError,
      refetch: layerData.refetchStations,
    },
    loading: layerData.loading,
    error: layerData.error,
    refetchAll: layerData.refetchAll,
  };
}
