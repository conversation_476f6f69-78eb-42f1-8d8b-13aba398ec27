import { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import * as Tone from 'tone';
import { AudioSynthConfig, defaultAudioConfig, InstrumentType } from '../types/audio';

interface AudioPlayerState {
  isPlaying: boolean;
  volume: number;
  progress: number;
  duration: number;
}

export interface UseAudioPlayerReturn {
  state: AudioPlayerState;
  audioConfig: AudioSynthConfig;
  playRealtimeAudio: (
    earthquakes: Array<{ id?: string | number; event_id?: string; occurred_at: string; magnitude: number }>,
    currentTime: Date,
    timeWindow: number,
    volume: number
  ) => void;
  stopAudio: () => void;
  setVolume: (volume: number) => void;
  setAudioConfig: (config: AudioSynthConfig) => void;
  previewInstrument: (instrumentType: InstrumentType) => Promise<void>;
  isSupported: boolean;
  initializeAudio: () => Promise<void>;
}

export function useAudioPlayer(): UseAudioPlayerReturn {
  const [state, setState] = useState<AudioPlayerState>({
    isPlaying: false,
    volume: 0.7,
    progress: 0,
    duration: 0
  });

  const [audioConfig, setAudioConfig] = useState<AudioSynthConfig>(defaultAudioConfig);

  // 移除合成器引用，仅保留采样器

  // Tone.js 采样器引用 - 用于音频样本播放
  const samplerRefs = useRef<{
    piano: Tone.Sampler | null;
    casio: Tone.Sampler | null;
    drums: Tone.Sampler | null;
    analog: Tone.Sampler | null;
    percussion: Tone.Sampler | null;
    strings: Tone.Sampler | null;
  }>({
    piano: null,
    casio: null,
    drums: null,
    analog: null,
    percussion: null,
    strings: null
  });

  const volumeNodeRef = useRef<Tone.Volume | null>(null);
  const reverbRef = useRef<Tone.Reverb | null>(null);
  const delayRef = useRef<Tone.FeedbackDelay | null>(null);
  const compressorRef = useRef<Tone.Compressor | null>(null);
  const playedEventsRef = useRef<Set<string>>(new Set());
  const isInitializedRef = useRef<boolean>(false);

  // 记录上一次的播放时间，用于检测时间重置
  const lastPlayTimeRef = useRef<number>(0);

  // 检查支持
  const isSupported = typeof window !== 'undefined';

  // 初始化 Tone.js 音频系统
  const initializeAudio = useCallback(async () => {
    if (isInitializedRef.current) return;

    try {
      // 启动 Tone.js 音频上下文
      if (Tone.getContext().state !== 'running') {
        await Tone.start();
        console.log('Tone.js audio context started');
      }

      // 创建效果器链
      compressorRef.current = new Tone.Compressor({
        threshold: audioConfig.global.compressor.threshold,
        ratio: audioConfig.global.compressor.ratio,
        attack: audioConfig.global.compressor.attack,
        release: audioConfig.global.compressor.release
      });

      reverbRef.current = new Tone.Reverb(audioConfig.global.reverb.roomSize);
      reverbRef.current.wet.value = audioConfig.global.reverb.wet;

      delayRef.current = new Tone.FeedbackDelay({
        delayTime: audioConfig.global.delay.delayTime,
        feedback: audioConfig.global.delay.feedback,
        wet: audioConfig.global.delay.wet
      });

      // 创建音量控制节点
      volumeNodeRef.current = new Tone.Volume(-3); // 提高基础音量

      // 连接效果器链：合成器 -> 压缩器 -> 混响 -> 延迟 -> 音量 -> 输出
      compressorRef.current.chain(reverbRef.current, delayRef.current, volumeNodeRef.current, Tone.getDestination());

      // 初始化时创建默认乐器的采样器
      await ensureCurrentSamplerLoaded();

      // 设置初始音量
      if (volumeNodeRef.current) {
        volumeNodeRef.current.volume.value = Tone.gainToDb(state.volume);
      }

      isInitializedRef.current = true;
      console.log('Audio synthesizers initialized successfully');
    } catch (error) {
      console.error('Failed to initialize audio:', error);
    }
  }, [audioConfig, state.volume]); // 添加audioConfig依赖，确保配置变化时重新初始化

  // 采样器配置定义
  const samplerConfigs = useMemo(() => ({
    piano: {
      urls: {
        C1: "C1.mp3",
        C2: "C2.mp3",
        C3: "C3.mp3",
        C4: "C4.mp3",
        C5: "C5.mp3",
        C6: "C6.mp3"
      },
      release: 1,
      baseUrl: "/audio/salamander/"
    },
    casio: {
      urls: {
        C1: "C2.mp3",
        C2: "D2.mp3",
        C3: "E2.mp3",
        C4: "F2.mp3",
        C5: "G2.mp3"
      },
      release: audioConfig.sampler.release,
      baseUrl: "/audio/casio/"
    },
    drums: {
      urls: {
        C2: "kick.mp3",
        C3: "snare.mp3",
        C4: "hihat.mp3",
        C5: "tom1.mp3",
        C6: "tom2.mp3"
      },
      release: 0.5,
      baseUrl: "/audio/drums/"
    },
    analog: {
      urls: {
        C3: "Analogsynth_octaves_highmid.mp3",
        C4: "Analogsynth_octaves_lowmid.mp3",
        C5: "Analogsynth_octaves_low.mp3"
      },
      release: audioConfig.sampler.release,
      baseUrl: "/audio/berklee/"
    },
    percussion: {
      urls: {
        C5: "gong_shot1.mp3",
        C6: "gong_shot2.mp3",
        C7: "gong_shot3.mp3",
        D5: "Clap1.mp3",
        D6: "Clap2.mp3"
      },
      release: 2.0,
      baseUrl: "/audio/berklee/"
    },
    strings: {
      urls: {
        G3: "guitar_LowEstring1.mp3",
        G4: "guitar_Gstring.mp3",
        G5: "guitar_highEstring.mp3",
        A3: "guitar_Astring.mp3",
        B3: "guitar_Bstring.mp3"
      },
      release: audioConfig.sampler.release,
      baseUrl: "/audio/berklee/"
    }
  }), [audioConfig.sampler.release]);

  // 创建单个采样器（按需加载）
  const createSampler = useCallback(async (instrumentType: string) => {
    if (!compressorRef.current) return null;

    const config = samplerConfigs[instrumentType as keyof typeof samplerConfigs];
    if (!config) return null;

    try {
      const sampler = new Tone.Sampler(config).connect(compressorRef.current);

      // 等待采样器加载完成
      await new Promise<void>((resolve) => {
        if (sampler.loaded) {
          resolve();
        } else {
          const checkLoaded = () => {
            if (sampler.loaded) {
              resolve();
            } else {
              setTimeout(checkLoaded, 100);
            }
          };
          checkLoaded();
        }
      });

      return sampler;
    } catch (error) {
      console.error(`创建 ${instrumentType} 采样器时出错:`, error);
      return null;
    }
  }, [samplerConfigs]);

  // 确保当前选择的乐器采样器已加载
  const ensureCurrentSamplerLoaded = useCallback(async () => {
    const currentInstrument = audioConfig.selectedInstrument;

    // 如果当前乐器的采样器已存在且已加载，直接返回
    if (samplerRefs.current[currentInstrument] && samplerRefs.current[currentInstrument]?.loaded) {
      return;
    }

    // 创建当前乐器的采样器
    const sampler = await createSampler(currentInstrument);
    if (sampler) {
      samplerRefs.current[currentInstrument] = sampler;
    }
  }, [audioConfig.selectedInstrument, createSampler]);

  // 移除不再需要的批量重新创建函数，改为按需加载

  // 地震震级到音频参数的映射函数
  const getAudioParams = useCallback((magnitude: number) => {
    const selectedInstrument = audioConfig.selectedInstrument;
    const audioMode = audioConfig.audioMode;

    if (audioMode === 'samples') {
      // 采样器模式：返回音符名称 - 适配新的乐器类型
      const getNoteForInstrument = (instrument: string, magnitude: number) => {
        switch (instrument) {
          case 'piano':
            if (magnitude < 1.0) return 'C6';      // 微震：高音
            else if (magnitude < 2.5) return 'C4'; // 中震：中音
            else return 'C2';                      // 大震：低音
          case 'casio':
            if (magnitude < 1.0) return 'C5';      // 微震：最高音 (G2.mp3)
            else if (magnitude < 2.5) return 'C3'; // 中震：中音 (E2.mp3)
            else return 'C1';                      // 大震：低音 (C2.mp3)
          case 'drums':
            if (magnitude < 1.0) return 'C4';      // 微震：hihat
            else if (magnitude < 2.5) return 'C3'; // 中震：snare
            else return 'C2';                      // 大震：kick
          case 'analog':
            if (magnitude < 1.0) return 'C5';      // 微震：高音 (Analogsynth_octaves_low)
            else if (magnitude < 2.5) return 'C4'; // 中震：中音 (Analogsynth_octaves_lowmid)
            else return 'C3';                      // 大震：低音 (Analogsynth_octaves_highmid)
          case 'percussion':
            if (magnitude < 1.0) return 'D6';      // 微震：拍手 (Clap2)
            else if (magnitude < 2.5) return 'C6'; // 中震：锣声 (gong_shot2)
            else return 'C5';                      // 大震：锣声 (gong_shot1)
          case 'strings':
            if (magnitude < 1.0) return 'G5';      // 微震：高音 (guitar_highEstring)
            else if (magnitude < 2.5) return 'G4'; // 中震：中音 (guitar_Gstring)
            else return 'G3';                      // 大震：低音 (guitar_LowEstring1)
          default:
            return 'C4';
        }
      };

      const note = getNoteForInstrument(selectedInstrument, magnitude);
      const volumeMult = audioConfig.sampler.volumeMultiplier;

      if (magnitude < 1.0) {
        return {
          type: 'micro',
          instrument: selectedInstrument,
          note: note,
          frequency: note, // 对于采样器，使用音符名称
          volume: 0.5 * volumeMult,
          duration: '32n',
          description: '微震'
        };
      } else if (magnitude < 2.5) {
        return {
          type: 'moderate',
          instrument: selectedInstrument,
          note: note,
          frequency: note,
          volume: 0.7 * volumeMult,
          duration: '16n',
          description: '中震'
        };
      } else {
        return {
          type: 'major',
          instrument: selectedInstrument,
          note: note,
          frequency: note,
          volume: 1.0 * volumeMult,
          duration: '8n',
          description: '大震'
        };
      }
    }
    // 移除合成器模式，仅保留采样音模式
  }, [audioConfig.sampler, audioConfig.selectedInstrument, audioConfig.audioMode]);

  // 播放单个地震事件的音频 - 仅使用采样器，按需加载
  const playEarthquakeSound = useCallback(async (magnitude: number, volume: number) => {
    if (!isInitializedRef.current) {
      console.warn('Audio not initialized');
      return;
    }

    const selectedInstrument = audioConfig.selectedInstrument;

    try {
      // 确保当前乐器的采样器已加载
      await ensureCurrentSamplerLoaded();

      const params = getAudioParams(magnitude);
      if (!params) {
        console.warn('Failed to get audio parameters');
        return;
      }

      const finalVolume = Math.min(params.volume * volume, 1.0);

      // 使用采样器播放
      const currentSampler = samplerRefs.current[selectedInstrument];

      if (!currentSampler || !currentSampler.loaded) {
        console.warn(`Sampler for ${selectedInstrument} still not available after loading attempt`);
        return;
      }

      // 播放音频
      currentSampler.triggerAttackRelease(params.frequency as string, params.duration, undefined, finalVolume);

    } catch (error) {
      console.error('Failed to play earthquake sound:', error);
      console.error('Error details:', {
        selectedInstrument,
        magnitude,
        volume
      });
    }
  }, [getAudioParams, audioConfig.selectedInstrument, ensureCurrentSamplerLoaded]);

  // 实时播放音频 - 简化逻辑，只播放新出现的地震事件
  const playRealtimeAudio = useCallback(async (
    earthquakes: Array<{ id?: string | number; event_id?: string; occurred_at: string; magnitude: number }>,
    currentTime: Date,
    timeWindow: number = 1000,
    volume: number
  ) => {
    if (!isSupported) {
      return;
    }

    // 确保音频已初始化
    await initializeAudio();

    if (!isInitializedRef.current) {
      return;
    }

    // 检测时间重置（循环播放）
    const currentTimeMs = currentTime.getTime();
    const lastTimeMs = lastPlayTimeRef.current;

    // 如果时间大幅回退（超过1小时），认为是循环重置，清理播放记录
    if (lastTimeMs > 0 && (lastTimeMs - currentTimeMs) > 60 * 60 * 1000) {
      playedEventsRef.current.clear();
    }

    // 更新上次播放时间
    lastPlayTimeRef.current = currentTimeMs;

    // 简化逻辑：找到当前时间窗口内的地震事件
    const windowStart = currentTimeMs - timeWindow / 2;
    const windowEnd = currentTimeMs + timeWindow / 2;

    // 筛选时间窗口内的事件
    const eventsInWindow = earthquakes.filter(eq => {
      const eqTime = new Date(eq.occurred_at).getTime();
      return eqTime >= windowStart && eqTime <= windowEnd;
    });

    // 去重：只播放未播放过的事件
    const newEvents = eventsInWindow.filter(eq => {
      const eventId = eq.id?.toString() || eq.event_id || `eq_${eq.occurred_at}`;

      if (!playedEventsRef.current.has(eventId)) {
        playedEventsRef.current.add(eventId);
        return true;
      }
      return false;
    });

    // 播放新事件
    if (newEvents.length > 0) {
      // 按发生时间排序，严格按照时间顺序播放
      const sortedEvents = newEvents.sort((a, b) => {
        const timeA = new Date(a.occurred_at).getTime();
        const timeB = new Date(b.occurred_at).getTime();
        return timeA - timeB; // 从早到晚排序
      });

      // 立即播放所有地震事件，不使用延迟
      // 因为时间窗口已经确保了正确的时间顺序
      sortedEvents.forEach((eq, index) => {
        // 使用很小的延迟来避免音频冲突，但保持时间顺序
        setTimeout(async () => {
          await playEarthquakeSound(eq.magnitude, volume);
        }, index * 50); // 每个地震间隔50ms，保持快速播放
      });
    }

    // 定期清理播放记录，避免内存泄漏
    if (playedEventsRef.current.size > 2000) {
      const keysArray = Array.from(playedEventsRef.current);
      const toKeep = keysArray.slice(-1000); // 保留最近1000个
      playedEventsRef.current.clear();
      toKeep.forEach(key => playedEventsRef.current.add(key));
    }
  }, [isSupported, initializeAudio, playEarthquakeSound]);



  // 停止音频
  const stopAudio = useCallback(() => {
    // 立即停止所有正在播放的音频
    try {
      // 停止所有采样器
      Object.values(samplerRefs.current).forEach(sampler => {
        if (sampler && 'releaseAll' in sampler) {
          sampler.releaseAll();
        }
      });

      // 释放所有音频资源
      Tone.getTransport().stop();
      Tone.getTransport().cancel();


    } catch (error) {
      console.error('停止音频时出错:', error);
    }

    setState(prev => ({ ...prev, isPlaying: false, progress: 0 }));
  }, []);

  // 设置音量
  const setVolume = useCallback((volume: number) => {
    const clampedVolume = Math.max(0, Math.min(1, volume));

    setState(prev => ({ ...prev, volume: clampedVolume }));

    // 更新 Tone.js 音量
    if (volumeNodeRef.current) {
      volumeNodeRef.current.volume.value = Tone.gainToDb(clampedVolume);
    }
  }, []);

  // 试听乐器方法 - 播放地震从小到大的声音序列
  const previewInstrument = useCallback(async (instrumentType: InstrumentType) => {
    if (!isSupported) {
      console.warn('Audio not supported');
      return;
    }

    // 确保音频已初始化
    await initializeAudio();
    if (!isInitializedRef.current) {
      console.warn('Audio not initialized');
      return;
    }

    try {
      // 确保目标乐器的采样器已加载
      if (!samplerRefs.current[instrumentType]) {
        samplerRefs.current[instrumentType] = await createSampler(instrumentType);
      }

      const sampler = samplerRefs.current[instrumentType];
      if (!sampler || !sampler.loaded) {
        console.warn(`Sampler for ${instrumentType} not available`);
        return;
      }

      // 地震从小到大的音符序列（高音到低音，模拟震级增强）
      const earthquakeSequence: Record<InstrumentType, string[]> = {
        piano: ['C6', 'C5', 'C4', 'C3', 'C2'],
        casio: ['G2', 'F2', 'E2', 'D2', 'C2'],
        drums: ['C4', 'C4', 'C3', 'C3', 'C2'], // hihat -> snare -> kick
        analog: ['C5', 'C4', 'C4', 'C3', 'C3'],
        percussion: ['C7', 'C6', 'C6', 'C5', 'C5'], // 不同锣声
        strings: ['G5', 'G4', 'G4', 'G3', 'A3'] // 从高音弦到低音弦
      };

      const noteSequence = earthquakeSequence[instrumentType];
      const volume = 0.6; // 试听音量
      const noteDuration = 0.4; // 每个音符持续时间（秒）
      const noteInterval = 0.6; // 音符间隔时间（秒）

      // 播放地震序列
      noteSequence.forEach((note, index) => {
        setTimeout(() => {
          // 音量随震级增大而增强
          const dynamicVolume = volume * (0.6 + (index * 0.1)); // 从0.6到1.0
          const duration = `${noteDuration}n`; // 转换为Tone.js格式

          sampler.triggerAttackRelease(note, duration, undefined, dynamicVolume);
        }, index * noteInterval * 1000);
      });

    } catch (error) {
      console.error(`Preview instrument ${instrumentType} failed:`, error);
    }
  }, [isSupported, initializeAudio, createSampler]);

  // 自定义setAudioConfig函数，智能处理配置变化
  const handleSetAudioConfig = useCallback(async (newConfig: AudioSynthConfig) => {
    const oldConfig = audioConfig;
    setAudioConfig(newConfig);

    // 如果音频已初始化
    if (isInitializedRef.current) {
      // 如果只是切换乐器，按需加载新乐器的采样器
      if (oldConfig.selectedInstrument !== newConfig.selectedInstrument) {
        await ensureCurrentSamplerLoaded();
      }
      // 如果是其他配置变化（如音量、释放时间等），重新创建当前采样器
      else if (oldConfig.sampler.release !== newConfig.sampler.release ||
        oldConfig.sampler.volumeMultiplier !== newConfig.sampler.volumeMultiplier) {
        // 释放当前采样器
        if (samplerRefs.current[newConfig.selectedInstrument]) {
          samplerRefs.current[newConfig.selectedInstrument]?.dispose();
          samplerRefs.current[newConfig.selectedInstrument] = null;
        }
        await ensureCurrentSamplerLoaded();
      }
    }
  }, [audioConfig, ensureCurrentSamplerLoaded]);

  return {
    state,
    audioConfig,
    playRealtimeAudio,
    stopAudio,
    setVolume,
    setAudioConfig: handleSetAudioConfig,
    previewInstrument,
    isSupported,
    initializeAudio
  };
}
