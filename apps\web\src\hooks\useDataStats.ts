import { useState, useEffect } from 'react';
import { earthquakeApi, faultApi, stationApi } from '../services/api';
import type { EarthquakeStats, FaultStats, StationStats } from '../types';

interface DataStats {
  earthquakes: {
    total: number;
    magnitudeStats: Array<{ magnitude_range: string; count: number }>;
    loading: boolean;
    error: string | null;
  };
  faults: {
    total: number;
    levelStats: Array<{ level: number; count: number }>;
    loading: boolean;
    error: string | null;
  };
  stations: {
    total: number;
    statusStats: Array<{ status: string; count: number }>;
    loading: boolean;
    error: string | null;
  };
  wells: {
    total: number;
    loading: boolean;
    error: string | null;
  };
  wellPlatforms: {
    total: number;
    loading: boolean;
    error: string | null;
  };
}

export function useDataStats() {
  const [stats, setStats] = useState<DataStats>({
    earthquakes: { total: 0, magnitudeStats: [], loading: true, error: null },
    faults: { total: 0, levelStats: [], loading: true, error: null },
    stations: { total: 0, statusStats: [], loading: true, error: null },
    wells: { total: 0, loading: true, error: null },
    wellPlatforms: { total: 0, loading: true, error: null },
  });

  // 获取地震统计数据
  const fetchEarthquakeStats = async () => {
    try {
      setStats(prev => ({
        ...prev,
        earthquakes: { ...prev.earthquakes, loading: true, error: null }
      }));

      const response = await earthquakeApi.getEarthquakeStats();
      if (response.success && response.data) {
        const data = response.data;
        setStats(prev => ({
          ...prev,
          earthquakes: {
            total: data.total,
            magnitudeStats: data.magnitudeStats || [],
            loading: false,
            error: null
          }
        }));
      }
    } catch (error) {
      console.error('获取地震统计数据失败:', error);
      setStats(prev => ({
        ...prev,
        earthquakes: {
          ...prev.earthquakes,
          loading: false,
          error: '获取地震统计数据失败'
        }
      }));
    }
  };

  // 获取断层统计数据
  const fetchFaultStats = async () => {
    try {
      setStats(prev => ({
        ...prev,
        faults: { ...prev.faults, loading: true, error: null }
      }));

      const response = await faultApi.getFaultStats();
      if (response.success && response.data) {
        const data = response.data;
        setStats(prev => ({
          ...prev,
          faults: {
            total: data.total,
            levelStats: data.levelStats || [],
            loading: false,
            error: null
          }
        }));
      }
    } catch (error) {
      console.error('获取断层统计数据失败:', error);
      setStats(prev => ({
        ...prev,
        faults: {
          ...prev.faults,
          loading: false,
          error: '获取断层统计数据失败'
        }
      }));
    }
  };

  // 获取台站统计数据
  const fetchStationStats = async () => {
    try {
      setStats(prev => ({
        ...prev,
        stations: { ...prev.stations, loading: true, error: null }
      }));

      const response = await stationApi.getStationStats();
      if (response.success && response.data) {
        const data = response.data;
        setStats(prev => ({
          ...prev,
          stations: {
            total: data.total,
            statusStats: data.statusStats || [],
            loading: false,
            error: null
          }
        }));
      }
    } catch (error) {
      console.error('获取台站统计数据失败:', error);
      setStats(prev => ({
        ...prev,
        stations: {
          ...prev.stations,
          loading: false,
          error: '获取台站统计数据失败'
        }
      }));
    }
  };

  // 获取井轨迹统计数据
  const fetchWellStats = async () => {
    try {
      setStats(prev => ({
        ...prev,
        wells: { ...prev.wells, loading: true, error: null }
      }));

      // 使用stats API获取统计数据
      const response = await fetch('/api/wells/stats');
      const data = await response.json();

      if (data.success && data.data) {
        setStats(prev => ({
          ...prev,
          wells: {
            total: data.data.total,
            loading: false,
            error: null
          }
        }));
      }
    } catch (error) {
      console.error('获取井轨迹统计数据失败:', error);
      setStats(prev => ({
        ...prev,
        wells: {
          ...prev.wells,
          loading: false,
          error: '获取井轨迹统计数据失败'
        }
      }));
    }
  };

  // 获取井平台统计数据
  const fetchWellPlatformStats = async () => {
    try {
      setStats(prev => ({
        ...prev,
        wellPlatforms: { ...prev.wellPlatforms, loading: true, error: null }
      }));

      // 获取井平台数据 - 使用platforms API获取总数
      const response = await fetch('/api/platforms?limit=1');
      const data = await response.json();

      if (data.success && data.data) {
        setStats(prev => ({
          ...prev,
          wellPlatforms: {
            total: data.data.total,
            loading: false,
            error: null
          }
        }));
      }
    } catch (error) {
      console.error('获取井平台统计数据失败:', error);
      setStats(prev => ({
        ...prev,
        wellPlatforms: {
          ...prev.wellPlatforms,
          loading: false,
          error: '获取井平台统计数据失败'
        }
      }));
    }
  };

  // 刷新所有统计数据
  const refreshStats = () => {
    fetchEarthquakeStats();
    fetchFaultStats();
    fetchStationStats();
    fetchWellStats();
    fetchWellPlatformStats();
  };

  useEffect(() => {
    refreshStats();
  }, []);

  return {
    stats,
    refreshStats,
    isLoading: Object.values(stats).some(stat => stat.loading),
  };
}
