// 这个文件现在被 EarthquakeDataContext 替代

import { earthquakeApi } from '@/services/api';
import { Earthquake } from '@/types';
import { useState, useEffect } from 'react';

// 为了保持向后兼容，重新导出 context 中的 hook
export { useEarthquakeData } from '../contexts/EarthquakeDataContext';

// 获取单个地震事件详情的 hook
export function useEarthquakeDetail(id: string | null) {
  const [earthquake, setEarthquake] = useState<Earthquake | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setEarthquake(null);
      return;
    }

    const fetchEarthquake = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await earthquakeApi.getEarthquakeById(id);
        if (response.success && response.data) {
          setEarthquake(response.data);
        } else {
          setError(response.error || '获取地震详情失败');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '网络请求失败');
      } finally {
        setLoading(false);
      }
    };

    fetchEarthquake();
  }, [id]);

  return { earthquake, loading, error };
}
