// 向后兼容性封装 - 重新导出新的context中的useFocalMechanismData hook
export { useFocalMechanismData } from '../contexts/FocalMechanismDataContext';

// 保持对原有API的兼容性
import { useState, useEffect, useCallback } from 'react';
import { focalMechanismApi } from '../services/api';
import type { FocalMechanism, FocalMechanismQuery, FocalMechanismStats } from '../types';

interface FocalMechanismDataState {
  focalMechanisms: FocalMechanism[];
  total: number;
  page: number;
  limit: number;
  loading: boolean;
  error: string | null;
  stats: FocalMechanismStats | null;
  statsLoading: boolean;
  statsError: string | null;
}

const initialState: FocalMechanismDataState = {
  focalMechanisms: [],
  total: 0,
  page: 1,
  limit: 50,
  loading: false,
  error: null,
  stats: null,
  statsLoading: false,
  statsError: null,
};

// 为了保持向后兼容，保留原有的hook实现（但建议使用新的context）
export function useFocalMechanismDataLegacy() {
  const [state, setState] = useState<FocalMechanismDataState>(initialState);

  // 获取震源机制解列表
  const fetchFocalMechanisms = useCallback(async (params: FocalMechanismQuery = {}) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await focalMechanismApi.getFocalMechanisms(params);

      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          focalMechanisms: response.data!.focalMechanisms,
          total: response.data!.total,
          page: response.data!.page,
          limit: response.data!.limit,
          loading: false,
          error: null,
        }));
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: response.error || '获取震源机制解数据失败',
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '网络请求失败',
      }));
    }
  }, []);

  // 获取震源机制解统计数据
  const fetchStats = useCallback(async () => {
    setState(prev => ({ ...prev, statsLoading: true, statsError: null }));

    try {
      const response = await focalMechanismApi.getFocalMechanismStats();

      if (response.success && response.data) {
        setState(prev => ({
          ...prev,
          stats: response.data!,
          statsLoading: false,
          statsError: null,
        }));
      } else {
        setState(prev => ({
          ...prev,
          statsLoading: false,
          statsError: response.error || '获取统计数据失败',
        }));
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        statsLoading: false,
        statsError: error instanceof Error ? error.message : '网络请求失败',
      }));
    }
  }, []);

  // 刷新数据
  const refresh = useCallback((params?: FocalMechanismQuery) => {
    fetchFocalMechanisms(params);
    fetchStats();
  }, [fetchFocalMechanisms, fetchStats]);

  // 初始化数据加载
  useEffect(() => {
    refresh();
  }, [refresh]);

  return {
    ...state,
    fetchFocalMechanisms,
    fetchStats,
    refresh,
  };
}

// 获取单个震源机制解详情的 hook
export function useFocalMechanismDetail(id: string | null) {
  const [focalMechanism, setFocalMechanism] = useState<FocalMechanism | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setFocalMechanism(null);
      return;
    }

    const fetchFocalMechanism = async () => {
      setLoading(true);
      setError(null);

      try {
        const response = await focalMechanismApi.getFocalMechanismById(id);
        if (response.success && response.data) {
          setFocalMechanism(response.data);
        } else {
          setError(response.error || '获取震源机制解详情失败');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : '网络请求失败');
      } finally {
        setLoading(false);
      }
    };

    fetchFocalMechanism();
  }, [id]);

  return { focalMechanism, loading, error };
}
