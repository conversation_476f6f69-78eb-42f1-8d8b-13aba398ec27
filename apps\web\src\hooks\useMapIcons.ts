import { useEffect } from 'react';
import { useMap } from 'react-map-gl/maplibre';

/**
 * 加载自定义图标到地图的Hook
 */
export const useMapIcons = () => {
  const { current: map } = useMap();

  useEffect(() => {
    if (!map) return;

    let isLoading = false;
    let isLoaded = false;

    const loadIcons = async () => {
      try {
        // 防止重复加载
        if (isLoading || isLoaded) {
          return;
        }

        isLoading = true;

        // 定义要加载的图标
        const icons = [
          {
            id: 'well-platform-icon',
            url: '/icons/well-platform.png'
          },
          {
            id: 'monitoring-station-icon',
            url: '/icons/monitoring-station.png'
          },
        ];

        // 加载每个图标
        for (const icon of icons) {
          // 检查图标是否已经存在
          if (map.hasImage(icon.id)) {
            continue;
          }

          try {

            // 创建图片元素
            const img = new Image();
            img.crossOrigin = 'anonymous';

            // 等待图片加载完成
            await new Promise<void>((resolve, reject) => {
              img.onload = () => {
                resolve();
              };
              img.onerror = (e) => {
                console.error(`❌ 图片加载失败: ${icon.url}`, e);
                reject(new Error(`Failed to load icon: ${icon.url}`));
              };
              img.src = icon.url;
            });

            // 添加图标到地图（如果不存在的话）
            if (!map.hasImage(icon.id)) {
              map.addImage(icon.id, img);
            }

          } catch (error) {
            console.warn(`⚠️ 加载图标失败: ${icon.id}`, error);
          }
        }

        isLoaded = true;
      } catch (error) {
        console.error('❌ 加载地图图标时出错:', error);
      } finally {
        isLoading = false;
      }
    };

    // 监听缺失图标事件
    const handleMissingImage = (e: any) => {
      console.warn(`🚨 缺失图标: ${e.id}，重置加载状态并重试...`);
      isLoaded = false; // 重置加载状态，允许重新加载
      loadIcons();
    };

    // 监听地图加载完成事件
    const handleMapLoad = () => {
      loadIcons();
    };

    map.on('styleimagemissing', handleMissingImage);
    map.on('load', handleMapLoad);

    // 如果地图已经加载完成，立即加载图标
    if (map.loaded()) {
      loadIcons();
    } else {
      // 如果地图还没加载完成，等待load事件
    }

    // 清理函数
    return () => {
      if (map) {
        map.off('styleimagemissing', handleMissingImage);
        map.off('load', handleMapLoad);
      }
    };
  }, [map]);
};
