import { MapContainer } from '../components/map/MapContainer';
import { EarthquakeDataProvider } from '../contexts/EarthquakeDataContext';
import { FocalMechanismDataProvider } from '../contexts/FocalMechanismDataContext';
import { LayerDataProvider } from '../contexts/LayerDataContext';

export function MapPage() {
  return (
    <EarthquakeDataProvider>
      <FocalMechanismDataProvider>
        <LayerDataProvider>
          <div className="absolute inset-0 overflow-hidden data-grid">
            {/* 地图容器作为父组件，包含所有UI组件 */}
            <MapContainer />
          </div>
        </LayerDataProvider>
      </FocalMechanismDataProvider>
    </EarthquakeDataProvider>
  );
}
