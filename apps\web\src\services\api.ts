import type {
  ApiResponse,
  Earthquake,
  EarthquakeQuery,
  EarthquakeStats,
  FocalMechanism,
  FocalMechanismQuery,
  FocalMechanismStats,
  Fault,
  FaultStats,
  WellTrajectory,
  WellPlatform,
  Station,
  StationStats,
  Region,
  User,
  LoginRequest,
  RegisterRequest,
  PaginationParams
} from '../types';

const API_BASE = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

// 通用请求函数（不带认证）
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const response = await fetch(`${API_BASE}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  const data = await response.json();

  if (!response.ok) {
    // 如果响应包含错误信息，抛出包含错误信息的异常
    const errorMessage = data.error || data.message || `HTTP error! status: ${response.status}`;
    throw new Error(errorMessage);
  }

  return data;
}

// 带认证的请求函数
async function authenticatedApiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const token = localStorage.getItem('auth_token');

  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const response = await fetch(`${API_BASE}${endpoint}`, {
    ...options,
    headers,
  });

  const data = await response.json();

  // 如果返回 401，清除 token
  if (response.status === 401) {
    localStorage.removeItem('auth_token');
  }

  if (!response.ok) {
    // 如果响应包含错误信息，抛出包含错误信息的异常
    const errorMessage = data.error || data.message || `HTTP error! status: ${response.status}`;
    throw new Error(errorMessage);
  }

  return data;
}

// 认证相关 API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<ApiResponse<{ user: User; token: string }>> => {
    const response = await apiRequest<{ user: User; token: string }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data?.token) {
      localStorage.setItem('auth_token', response.data.token);
    }

    return response;
  },

  register: async (credentials: RegisterRequest): Promise<ApiResponse<{ user: User; token: string }>> => {
    const response = await apiRequest<{ user: User; token: string }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data?.token) {
      localStorage.setItem('auth_token', response.data.token);
    }

    return response;
  },

  logout: () => {
    localStorage.removeItem('auth_token');
  },

  getCurrentUser: (): Promise<ApiResponse<User>> => {
    return authenticatedApiRequest<User>('/auth/me');
  },
};

// 地震事件相关 API
export const earthquakeApi = {
  getEarthquakes: (params: EarthquakeQuery = {}): Promise<ApiResponse<{
    earthquakes: Earthquake[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return authenticatedApiRequest(`/earthquakes?${searchParams.toString()}`);
  },

  getEarthquakeById: (id: string): Promise<ApiResponse<Earthquake>> => {
    return authenticatedApiRequest(`/earthquakes/${id}`);
  },

  getEarthquakeStats: (): Promise<ApiResponse<EarthquakeStats>> => {
    return authenticatedApiRequest('/earthquakes/stats');
  },
};

// 断层数据相关 API
export const faultApi = {
  getFaults: (params: PaginationParams & { level?: number; region?: string } = {}): Promise<ApiResponse<{
    faults: Fault[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return authenticatedApiRequest(`/faults?${searchParams.toString()}`);
  },

  getFaultsByLevel: (level: number): Promise<ApiResponse<Fault[]>> => {
    return authenticatedApiRequest(`/faults/level/${level}`);
  },

  getFaultStats: (): Promise<ApiResponse<FaultStats>> => {
    return authenticatedApiRequest('/faults/stats');
  },
};

// 井轨迹相关 API
export const wellApi = {
  getWells: (params: PaginationParams & { region?: string; platform_name?: string } = {}): Promise<ApiResponse<{
    wells: WellTrajectory[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return authenticatedApiRequest(`/wells?${searchParams.toString()}`);
  },

  getWellById: (id: string): Promise<ApiResponse<WellTrajectory>> => {
    return authenticatedApiRequest(`/wells/${id}`);
  },

  getPlatforms: (): Promise<ApiResponse<string[]>> => {
    return authenticatedApiRequest('/wells/platforms');
  },
};

// 井平台相关 API
export const platformApi = {
  getPlatforms: (params: PaginationParams & { region?: string; name?: string } = {}): Promise<ApiResponse<{
    platforms: WellPlatform[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return authenticatedApiRequest(`/platforms?${searchParams.toString()}`);
  },

  getPlatformById: (id: string): Promise<ApiResponse<WellPlatform>> => {
    return authenticatedApiRequest(`/platforms/${id}`);
  },

  getPlatformTrajectories: (id: string, params: PaginationParams = {}): Promise<ApiResponse<{
    trajectories: WellTrajectory[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return authenticatedApiRequest(`/platforms/${id}/trajectories?${searchParams.toString()}`);
  },
};

// 台站相关 API
export const stationApi = {
  getStations: (params: PaginationParams & { status?: string; region?: string; name?: string } = {}): Promise<ApiResponse<{
    stations: Station[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return authenticatedApiRequest(`/stations?${searchParams.toString()}`);
  },

  getActiveStations: (): Promise<ApiResponse<Station[]>> => {
    return authenticatedApiRequest('/stations/active');
  },

  getStationStats: (): Promise<ApiResponse<StationStats>> => {
    return authenticatedApiRequest('/stations/stats');
  },
};

// 区域相关 API
export const regionApi = {
  getRegions: (): Promise<ApiResponse<Region[]>> => {
    return authenticatedApiRequest('/regions');
  },

  getRegionById: (id: string): Promise<ApiResponse<Region>> => {
    return authenticatedApiRequest(`/regions/${id}`);
  },

  getRegionStats: (id: string): Promise<ApiResponse<{
    region: Region;
    earthquakeCount: number;
    faultCount: number;
    wellCount: number;
    stationCount: number;
  }>> => {
    return authenticatedApiRequest(`/regions/${id}/stats`);
  },
};

// 震源机制解相关 API
export const focalMechanismApi = {
  getFocalMechanisms: (params: FocalMechanismQuery = {}): Promise<ApiResponse<{
    focalMechanisms: FocalMechanism[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    const searchParams = new URLSearchParams();

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    return authenticatedApiRequest(`/focal-mechanisms?${searchParams.toString()}`);
  },

  getFocalMechanismById: (id: string): Promise<ApiResponse<FocalMechanism>> => {
    return authenticatedApiRequest(`/focal-mechanisms/${id}`);
  },

  getFocalMechanismStats: (): Promise<ApiResponse<FocalMechanismStats>> => {
    return authenticatedApiRequest('/focal-mechanisms/stats');
  },
};

// 健康检查
export const healthApi = {
  check: (): Promise<{ status: string; timestamp: string }> => {
    const healthUrl = import.meta.env.VITE_HEALTH_URL || 'http://localhost:3001/health';
    return fetch(healthUrl).then(r => r.json());
  },
};
