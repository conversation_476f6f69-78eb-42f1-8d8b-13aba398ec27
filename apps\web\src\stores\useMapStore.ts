import { create } from 'zustand';
import type { MapViewState, LayerVisibility, FilterState, TimeRange, SpatialFilter } from '../types';
import type { MapRef } from 'react-map-gl/maplibre';

type BaseMapType = 'vector' | 'satellite' | 'terrain' | null;

interface MapStore {
  // 地图实例
  mapInstance: MapRef | null;
  setMapInstance: (map: MapRef | null) => void;

  // 地图初始视图状态
  initialViewState: MapViewState;

  // 底图类型
  baseMapType: BaseMapType;
  setBaseMapType: (type: BaseMapType) => void;

  // 注记图层控制
  showLabels: boolean;
  setShowLabels: (show: boolean) => void;

  // 图层可见性
  layerVisibility: LayerVisibility;
  toggleLayer: (layer: keyof LayerVisibility) => void;
  setLayerVisibility: (layer: keyof LayerVisibility, visible: boolean) => void;

  // 行政区划子图层控制
  administrativeSubLayers: {
    countyBoundaries: boolean;
    townshipBoundaries: boolean;
    townshipPoints: boolean;
  };
  toggleAdministrativeSubLayer: (subLayer: 'countyBoundaries' | 'townshipBoundaries' | 'townshipPoints') => void;

  // 行政区划数据加载状态
  administrativeLoading: boolean;
  setAdministrativeLoading: (loading: boolean) => void;

  // 筛选器状态
  filters: FilterState;
  setTimeRange: (timeRange: TimeRange) => void;
  setMagnitudeRange: (range: [number, number]) => void;
  setDepthRange: (range: [number, number]) => void;
  setSelectedRegions: (regions: string[]) => void;
  setSpatialFilter: (filter: SpatialFilter) => void;
  resetFilters: () => void;

  // 空间筛选绘制模式
  isDrawingMode: boolean;
  setDrawingMode: (mode: boolean) => void;

  // 3D视角调整模式
  isAdjustingView: boolean;
  setAdjustingView: (adjusting: boolean) => void;

  // 选中的数据
  selectedEarthquake: string | null;
  selectedFault: string | null;
  selectedWell: string | null;
  selectedStation: string | null;
  selectedFocalMechanism: string | null;
  setSelectedEarthquake: (id: string | null) => void;
  setSelectedFault: (id: string | null) => void;
  setSelectedWell: (id: string | null) => void;
  setSelectedStation: (id: string | null) => void;
  setSelectedFocalMechanism: (id: string | null) => void;

  // 悬停的数据
  hoveredEarthquake: string | null;
  hoveredFault: string | null;
  hoveredWell: string | null;
  hoveredStation: string | null;
  hoveredFocalMechanism: string | null;
  setHoveredEarthquake: (id: string | null) => void;
  setHoveredFault: (id: string | null) => void;
  setHoveredWell: (id: string | null) => void;
  setHoveredStation: (id: string | null) => void;
  setHoveredFocalMechanism: (id: string | null) => void;

  // 震源机制解数据现在由FocalMechanismDataContext管理
  // 这里只保留用于向后兼容的类型定义，实际数据获取应使用useFocalMechanismData hook

  // 详情面板状态
  isPanelPinned: boolean;
  setPanelPinned: (pinned: boolean) => void;

  // 时间轴播放状态
  isPlaying: boolean;
  playbackSpeed: number;
  currentTime: Date;
  setIsPlaying: (playing: boolean) => void;
  setPlaybackSpeed: (speed: number) => void;
  setCurrentTime: (time: Date) => void;

  // 侧边栏状态
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;

  // 三维模式状态
  showThreeModal: boolean;
  setShowThreeModal: (show: boolean) => void;

  // 三维视图模式（替换地图）
  isThreeMode: boolean;
  setThreeMode: (mode: boolean) => void;

  // 震源机制解显示模式
  focalMechanismDisplayMode: 'traditional' | 'tensor';
  setFocalMechanismDisplayMode: (mode: 'traditional' | 'tensor') => void;
}

// 默认时间范围：最近半年
const now = new Date();
const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
const defaultTimeRange: TimeRange = {
  start: sixMonthsAgo,
  end: now,
};

// 默认筛选器状态
const defaultFilters: FilterState = {
  timeRange: defaultTimeRange,
  magnitudeRange: [-2, 6],   // 支持负值震级数据
  depthRange: [0, 50],      // 扩大深度范围
  selectedRegions: [],
  spatialFilter: {
    center: null,
    radius: null,
  },
};

export const useMapStore = create<MapStore>((set) => ({
  // 地图实例
  mapInstance: null,
  setMapInstance: (map) => set({ mapInstance: map }),

  // 地图初始视图状态 - 基于真实数据的中心点
  initialViewState: {
    longitude: 105.45,
    latitude: 29.17,
    zoom: 10,
    pitch: 0,
    bearing: 0,
  },

  // 底图类型
  baseMapType: 'vector',
  setBaseMapType: (type) => set({ baseMapType: type }),

  // 注记图层控制
  showLabels: true,
  setShowLabels: (show) => set({ showLabels: show }),

  // 图层可见性
  layerVisibility: {
    earthquakes: true,
    faults: true,
    wellTrajectories: true,
    wellPlatforms: true,
    stations: true,
    focalMechanisms: false, // 默认隐藏震源机制解图层
    administrative: true, // 行政区划图层
  },
  toggleLayer: (layer) =>
    set((state) => ({
      layerVisibility: {
        ...state.layerVisibility,
        [layer]: !state.layerVisibility[layer],
      },
    })),
  setLayerVisibility: (layer, visible) =>
    set((state) => ({
      layerVisibility: {
        ...state.layerVisibility,
        [layer]: visible,
      },
    })),

  // 行政区划子图层控制
  administrativeSubLayers: {
    countyBoundaries: true, // 县级边界默认显示
    townshipBoundaries: true, // 乡镇边界默认隐藏
    townshipPoints: true, // 乡镇点位默认隐藏
  },
  toggleAdministrativeSubLayer: (subLayer) =>
    set((state) => ({
      administrativeSubLayers: {
        ...state.administrativeSubLayers,
        [subLayer]: !state.administrativeSubLayers[subLayer],
      },
    })),

  // 行政区划数据加载状态
  administrativeLoading: false,
  setAdministrativeLoading: (loading) =>
    set(() => ({
      administrativeLoading: loading,
    })),

  // 筛选器状态
  filters: defaultFilters,
  setTimeRange: (timeRange) => {
    set((state) => ({
      filters: { ...state.filters, timeRange },
    }));
  },
  setMagnitudeRange: (range) =>
    set((state) => ({
      filters: { ...state.filters, magnitudeRange: range },
    })),
  setDepthRange: (range) =>
    set((state) => ({
      filters: { ...state.filters, depthRange: range },
    })),
  setSelectedRegions: (regions) =>
    set((state) => ({
      filters: { ...state.filters, selectedRegions: regions },
    })),
  setSpatialFilter: (spatialFilter) =>
    set((state) => ({
      filters: { ...state.filters, spatialFilter },
    })),
  resetFilters: () => set({ filters: defaultFilters }),

  // 空间筛选绘制模式
  isDrawingMode: false,
  setDrawingMode: (mode) => set({ isDrawingMode: mode }),

  // 3D视角调整模式
  isAdjustingView: false,
  setAdjustingView: (adjusting) => set({ isAdjustingView: adjusting }),

  // 选中的数据
  selectedEarthquake: null,
  selectedFault: null,
  selectedWell: null,
  selectedStation: null,
  selectedFocalMechanism: null,
  setSelectedEarthquake: (id) => set({ selectedEarthquake: id }),
  setSelectedFault: (id) => set({ selectedFault: id }),
  setSelectedWell: (id) => set({ selectedWell: id }),
  setSelectedStation: (id) => set({ selectedStation: id }),
  setSelectedFocalMechanism: (id) => set({ selectedFocalMechanism: id }),

  // 悬停的数据
  hoveredEarthquake: null,
  hoveredFault: null,
  hoveredWell: null,
  hoveredStation: null,
  hoveredFocalMechanism: null,
  setHoveredEarthquake: (id) => set({ hoveredEarthquake: id }),
  setHoveredFault: (id) => set({ hoveredFault: id }),
  setHoveredWell: (id) => set({ hoveredWell: id }),
  setHoveredStation: (id) => set({ hoveredStation: id }),
  setHoveredFocalMechanism: (id) => set({ hoveredFocalMechanism: id }),

  // 震源机制解数据现在由FocalMechanismDataContext管理
  // 移除了 focalMechanisms, focalMechanismsLoading, focalMechanismsError, fetchFocalMechanisms
  // 请使用 useFocalMechanismData hook 来获取数据

  // 详情面板状态
  isPanelPinned: false,
  setPanelPinned: (pinned) => set({ isPanelPinned: pinned }),

  // 时间轴播放状态
  isPlaying: false,
  playbackSpeed: 1,
  currentTime: new Date(),
  setIsPlaying: (playing) => set({ isPlaying: playing }),
  setPlaybackSpeed: (speed) => set({ playbackSpeed: speed }),
  setCurrentTime: (time) => set({ currentTime: time }),

  // 侧边栏状态
  sidebarOpen: true,
  setSidebarOpen: (open) => set({ sidebarOpen: open }),

  // 三维模式状态
  showThreeModal: false,
  setShowThreeModal: (show) => set({ showThreeModal: show }),

  // 三维视图模式（替换地图）
  isThreeMode: false,
  setThreeMode: (mode) => set({ isThreeMode: mode }),

  // 震源机制解显示模式
  focalMechanismDisplayMode: 'traditional',
  setFocalMechanismDisplayMode: (mode) => set({ focalMechanismDisplayMode: mode }),
}));
