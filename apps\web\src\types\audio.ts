// 音频配置类型定义 - 基于 Tone.js 音频库重新设计

// 根据 Tone.js 音频库实际可用文件重新分类的乐器类型
export type InstrumentType =
  | 'piano'        // Salamander 钢琴采样
  | 'casio'        // Casio 电子音色
  | 'drums'        // 鼓组采样
  | 'analog'       // 模拟合成器
  | 'percussion'   // 打击乐器（锣、拍手等）
  | 'strings';     // 弦乐器（吉他、大提琴等）

// 仅保留采样音模式
export type AudioMode = 'samples';

// 音频样本配置
export interface SampleConfig {
  [key: string]: string; // 音符名称 -> 文件路径的映射
}

export interface InstrumentSamples {
  piano: SampleConfig;
  casio: SampleConfig;
  drums: SampleConfig;
  analog: SampleConfig;
  percussion: SampleConfig;
  strings: SampleConfig;
}

export interface AudioSynthConfig {
  // 音频模式：仅支持采样音
  audioMode: AudioMode;

  // 当前选择的乐器类型
  selectedInstrument: InstrumentType;

  // 音频样本配置
  samples: InstrumentSamples;

  // 采样器设置
  sampler: {
    volumeMultiplier: number; // 音量倍数
    release: number; // 释放时间
  };

  // 全局音频设置
  global: {
    volume: number;
    reverb: {
      roomSize: number;
      wet: number;
    };
    delay: {
      delayTime: number;
      feedback: number;
      wet: number;
    };
    compressor: {
      threshold: number;
      ratio: number;
      attack: number;
      release: number;
    };
  };
}

// 默认音频样本配置 - 基于 Tone.js 音频库实际文件
export const defaultSamples: InstrumentSamples = {
  // Salamander 钢琴采样
  piano: {
    'C1': 'C1.mp3',
    'C2': 'C2.mp3',
    'C3': 'C3.mp3',
    'C4': 'C4.mp3',
    'C5': 'C5.mp3',
    'C6': 'C6.mp3'
  },
  // Casio 电子音色
  casio: {
    'C1': 'C2.mp3',    // 低音
    'C2': 'D2.mp3',    // 中音
    'C3': 'E2.mp3',    // 高音
    'C4': 'F2.mp3',    // 更高音
    'C5': 'G2.mp3'     // 最高音
  },
  // 鼓组采样
  drums: {
    'C2': 'kick.mp3',
    'C3': 'snare.mp3',
    'C4': 'hihat.mp3',
    'C5': 'tom1.mp3',
    'C6': 'tom2.mp3'
  },
  // 模拟合成器
  analog: {
    'C3': 'Analogsynth_octaves_highmid.mp3',
    'C4': 'Analogsynth_octaves_lowmid.mp3',
    'C5': 'Analogsynth_octaves_low.mp3'
  },
  // 打击乐器
  percussion: {
    'C5': 'gong_shot1.mp3',
    'C6': 'gong_shot2.mp3',
    'C7': 'gong_shot3.mp3',
    'D5': 'Clap1.mp3',
    'D6': 'Clap2.mp3'
  },
  // 弦乐器
  strings: {
    'G3': 'guitar_LowEstring1.mp3',
    'G4': 'guitar_Gstring.mp3',
    'G5': 'guitar_highEstring.mp3',
    'A3': 'guitar_Astring.mp3',
    'B3': 'guitar_Bstring.mp3'
  }
};

// 默认音频配置
export const defaultAudioConfig: AudioSynthConfig = {
  audioMode: 'samples', // 仅使用采样音
  selectedInstrument: 'strings', // 默认选择弦乐器
  samples: defaultSamples,
  sampler: {
    volumeMultiplier: 1.3, // 弦乐器音量倍数
    release: 1.8 // 弦乐器释放时间
  },
  global: {
    volume: 0.7,
    reverb: {
      roomSize: 0.7,
      wet: 0.1
    },
    delay: {
      delayTime: 0.1,
      feedback: 0.2,
      wet: 0.05
    },
    compressor: {
      threshold: -24,
      ratio: 12,
      attack: 0.003,
      release: 0.25
    }
  }
};

// 预设配置 - 基于不同乐器类型
export const audioPresets: Record<string, AudioSynthConfig> = {
  default: defaultAudioConfig,

  // 钢琴预设
  piano: {
    ...defaultAudioConfig,
    selectedInstrument: 'piano',
    sampler: {
      volumeMultiplier: 1.2,
      release: 1.5
    }
  },

  // Casio 电子音色预设
  casio: {
    ...defaultAudioConfig,
    selectedInstrument: 'casio',
    sampler: {
      volumeMultiplier: 1.8,
      release: 0.8
    }
  },

  // 鼓组预设
  drums: {
    ...defaultAudioConfig,
    selectedInstrument: 'drums',
    sampler: {
      volumeMultiplier: 2.0,
      release: 0.5
    }
  },

  // 模拟合成器预设
  analog: {
    ...defaultAudioConfig,
    selectedInstrument: 'analog',
    sampler: {
      volumeMultiplier: 1.6,
      release: 1.2
    }
  },

  // 打击乐预设
  percussion: {
    ...defaultAudioConfig,
    selectedInstrument: 'percussion',
    sampler: {
      volumeMultiplier: 1.4,
      release: 2.0
    }
  },

  // 弦乐预设
  strings: {
    ...defaultAudioConfig,
    selectedInstrument: 'strings',
    sampler: {
      volumeMultiplier: 1.3,
      release: 1.8
    }
  }
};
