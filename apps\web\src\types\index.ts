// API 响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页参数类型
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 地震事件类型
export interface Earthquake {
  id: number;
  event_id: string;
  occurred_at: string;
  latitude: number;
  longitude: number;
  depth: number;
  magnitude: number;
  region: string;
  created_at: string;
}

// 地震事件查询参数
export interface EarthquakeQuery extends PaginationParams {
  start_date?: string;
  end_date?: string;
  min_magnitude?: number;
  max_magnitude?: number;
  region?: string;
}

// 震源机制解数据类型
export interface FocalMechanism {
  id: number;
  date: string;
  time: string;
  latitude: number;
  longitude: number;
  depth: number;
  magnitude: number;
  strike1: number;
  dip1: number;
  rake1: number;
  strike2: number;
  dip2: number;
  rake2: number;
  misfit: number;
  region: string;
  created_at: string;
  // 矩阵张量参数
  mxx: number;
  myy: number;
  mzz: number;
  mxy: number;
  mxz: number;
  myz: number;
  // 矩阵张量元数据（可选）
  mt_magnitude?: number;  // 矩张量震级
  mt_type?: string;       // 震源类型
  dc_percentage?: number; // 双偶分量百分比
  clvd_percentage?: number; // CLVD分量百分比
  iso_percentage?: number;  // 各向同性分量百分比
}

// 震源机制解查询参数
export interface FocalMechanismQuery extends PaginationParams {
  start_date?: string;
  end_date?: string;
  min_magnitude?: number;
  max_magnitude?: number;
  region?: string;
}

// 震源机制解统计数据
export interface FocalMechanismStats {
  total: number;
  byMagnitude: Array<{ magnitude_range: string; count: number }>;
  byRegion: Array<{ region: string; count: number }>;
}

// 断层数据类型
export interface Fault {
  id: number;
  name: string;
  level: number;
  coordinates: string; // JSON 字符串存储坐标数组
  region: string;
  created_at: string;
}

// 井平台数据类型
export interface WellPlatform {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  region: string;
  created_at: string;
}

// 井轨迹数据类型
export interface WellTrajectory {
  id: number;
  name: string;
  platform_id: number;
  coordinates: string; // JSON 字符串存储坐标数组
  region: string;
  created_at: string;
}

// 监测台站数据类型
export interface Station {
  id: number;
  name: string;
  latitude: number;
  longitude: number;
  status: 'active' | 'inactive' | 'maintenance';
  region: string;
  created_at: string;
}

// 区域数据类型
export interface Region {
  id: number;
  name: string;
  bounds: string; // JSON 字符串存储边界坐标
  description: string;
  created_at: string;
}

// 用户数据类型
export interface User {
  id: number;
  email: string;
  role: 'admin' | 'user' | 'viewer';
  created_at: string;
}

// 登录请求类型
export interface LoginRequest {
  email: string;
  password: string;
}

// 注册请求类型
export interface RegisterRequest {
  email: string;
  password: string;
  role?: 'user' | 'viewer';
}

// 地图视图状态
export interface MapViewState {
  longitude: number;
  latitude: number;
  zoom: number;
  pitch?: number;
  bearing?: number;
}

// 图层可见性状态
export interface LayerVisibility {
  earthquakes: boolean;
  faults: boolean;
  wellTrajectories: boolean;
  wellPlatforms: boolean;
  stations: boolean;
  focalMechanisms: boolean;
  administrative: boolean;
}

// 时间范围
export interface TimeRange {
  start: Date;
  end: Date;
}

// 空间筛选状态
export interface SpatialFilter {
  center: [number, number] | null; // [longitude, latitude]
  radius: number | null; // 半径，单位：公里
}

// 筛选器状态
export interface FilterState {
  timeRange: TimeRange;
  magnitudeRange: [number, number];
  depthRange: [number, number];
  selectedRegions: string[];
  spatialFilter: SpatialFilter;
}

// 统计数据类型
export interface EarthquakeStats {
  total: number;
  magnitudeStats: Array<{ magnitude_range: string; count: number }>;
  regionStats: Array<{ region: string; count: number }>;
  recentStats: Array<{ date: string; count: number }>;
}

export interface FaultStats {
  total: number;
  levelStats: Array<{ level: number; count: number }>;
  regionStats: Array<{ region: string; count: number }>;
}

export interface StationStats {
  total: number;
  statusStats: Array<{ status: string; count: number }>;
  regionStats: Array<{ region: string; count: number }>;
}

// 地图图层数据类型
export interface EarthquakeLayerData {
  id: string;
  coordinates: [number, number];
  magnitude: number;
  depth: number;
  time: string;
  eventId: string;
}

export interface FaultLayerData {
  id: string;
  coordinates: Array<[number, number]>;
  level: number;
  name: string;
}

export interface WellLayerData {
  id: string;
  coordinates: Array<[number, number]>;
  platformName: string;
}

export interface StationLayerData {
  id: string;
  coordinates: [number, number];
  name: string;
  status: string;
}

export interface FocalMechanismLayerData {
  id: string;
  coordinates: [number, number];
  magnitude: number;
  depth: number;
  time: string;
  strike1: number;
  dip1: number;
  rake1: number;
  strike2: number;
  dip2: number;
  rake2: number;
  misfit: number;
}

// 行政区划图层数据类型
export interface AdministrativeBoundaryData {
  id: string;
  name?: string;
  level: 'county' | 'township';
  coordinates: Array<[number, number]>;
}

export interface AdministrativePointData {
  id: string;
  name: string;
  level: 'township';
  coordinates: [number, number];
}
