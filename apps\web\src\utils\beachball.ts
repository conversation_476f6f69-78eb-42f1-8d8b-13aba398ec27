/**
 * 基于震源机制解物理原理的沙滩球绘制
 * 参考 Pyrocko 的实现，正确计算压缩和拉张象限
 * 使用立体投影和精确的数学计算
 * 支持高质量PNG转换和缓存
 * 支持矩阵张量输入
 */

// SVG缓存结构
interface BeachballCacheEntry {
  svg: string;
  timestamp: number;
}

// PNG缓存结构
interface PNGCacheEntry {
  png: string;
  timestamp: number;
}

// 矩阵张量类型定义
export interface MomentTensor {
  mxx: number;  // 或 mrr
  myy: number;  // 或 mtt
  mzz: number;  // 或 mpp
  mxy: number;  // 或 mrt
  mxz: number;  // 或 mrp
  myz: number;  // 或 mtp
}

// 主轴分解结果
interface PrincipalAxes {
  t: number[];  // T轴 (最大拉张)
  n: number[];  // N轴 (中间轴)
  p: number[];  // P轴 (最大压缩)
  tVal: number; // T轴特征值
  nVal: number; // N轴特征值
  pVal: number; // P轴特征值
}

const beachballCache = new Map<string, BeachballCacheEntry>();
const pngCache = new Map<string, PNGCacheEntry>();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

// 清理过期缓存
function cleanExpiredCache() {
  const now = Date.now();

  // 清理SVG缓存
  for (const [key, entry] of beachballCache.entries()) {
    if (now - entry.timestamp > CACHE_TTL) {
      beachballCache.delete(key);
    }
  }

  // 清理PNG缓存
  for (const [key, entry] of pngCache.entries()) {
    if (now - entry.timestamp > CACHE_TTL) {
      pngCache.delete(key);
    }
  }
}

// 数学常量
const PI = Math.PI;
const d2r = PI / 180;

// 向量运算
function cross(a: number[], b: number[]): number[] {
  return [
    a[1] * b[2] - a[2] * b[1],
    a[2] * b[0] - a[0] * b[2],
    a[0] * b[1] - a[1] * b[0]
  ];
}

function normalize(v: number[]): number[] {
  const length = Math.sqrt(v[0] * v[0] + v[1] * v[1] + v[2] * v[2]);
  if (length < 1e-10) return [0, 0, 0];
  return [v[0] / length, v[1] / length, v[2] / length];
}

function dot(a: number[], b: number[]): number {
  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];
}

// 矩阵运算
function matrixMultiply(A: number[][], B: number[][]): number[][] {
  const result: number[][] = [];
  for (let i = 0; i < A.length; i++) {
    result[i] = [];
    for (let j = 0; j < B[0].length; j++) {
      let sum = 0;
      for (let k = 0; k < A[0].length; k++) {
        sum += A[i][k] * B[k][j];
      }
      result[i][j] = sum;
    }
  }
  return result;
}

function matrixVectorMultiply(A: number[][], v: number[]): number[] {
  const result: number[] = [];
  for (let i = 0; i < A.length; i++) {
    let sum = 0;
    for (let j = 0; j < v.length; j++) {
      sum += A[i][j] * v[j];
    }
    result[i] = sum;
  }
  return result;
}

// 简化的特征值计算（适用于3x3对称矩阵）
function eigenDecomposition3x3(matrix: number[][]): { values: number[]; vectors: number[][] } {
  // 使用雅可比迭代法求解特征值和特征向量
  const m = matrix.map(row => [...row]); // 深拷贝
  const n = 3;
  const V = [[1, 0, 0], [0, 1, 0], [0, 0, 1]]; // 特征向量矩阵

  const maxIterations = 100;
  const tolerance = 1e-10;

  for (let iter = 0; iter < maxIterations; iter++) {
    // 找到最大的非对角元素
    let maxVal = 0;
    let p = 0, q = 1;

    for (let i = 0; i < n; i++) {
      for (let j = i + 1; j < n; j++) {
        if (Math.abs(m[i][j]) > maxVal) {
          maxVal = Math.abs(m[i][j]);
          p = i;
          q = j;
        }
      }
    }

    if (maxVal < tolerance) break;

    // 计算旋转角
    const theta = 0.5 * Math.atan2(2 * m[p][q], m[q][q] - m[p][p]);
    const c = Math.cos(theta);
    const s = Math.sin(theta);

    // 应用旋转
    const App = c * c * m[p][p] + s * s * m[q][q] - 2 * c * s * m[p][q];
    const Aqq = s * s * m[p][p] + c * c * m[q][q] + 2 * c * s * m[p][q];
    const Apq = 0;

    m[p][p] = App;
    m[q][q] = Aqq;
    m[p][q] = m[q][p] = Apq;

    // 更新其他元素
    for (let i = 0; i < n; i++) {
      if (i !== p && i !== q) {
        const Aip = c * m[i][p] - s * m[i][q];
        const Aiq = s * m[i][p] + c * m[i][q];
        m[i][p] = m[p][i] = Aip;
        m[i][q] = m[q][i] = Aiq;
      }
    }

    // 更新特征向量
    for (let i = 0; i < n; i++) {
      const Vip = c * V[i][p] - s * V[i][q];
      const Viq = s * V[i][p] + c * V[i][q];
      V[i][p] = Vip;
      V[i][q] = Viq;
    }
  }

  // 提取特征值和特征向量
  const eigenValues = [m[0][0], m[1][1], m[2][2]];
  const eigenVectors = [
    [V[0][0], V[1][0], V[2][0]],
    [V[0][1], V[1][1], V[2][1]],
    [V[0][2], V[1][2], V[2][2]]
  ];

  return { values: eigenValues, vectors: eigenVectors };
}

// 从矩阵张量计算主轴
function momentTensorToPrincipalAxes(mt: MomentTensor): PrincipalAxes {
  // 构建矩阵张量矩阵（NED坐标系）
  const M = [
    [mt.mxx, mt.mxy, mt.mxz],
    [mt.mxy, mt.myy, mt.myz],
    [mt.mxz, mt.myz, mt.mzz]
  ];

  // 特征值分解
  const { values, vectors } = eigenDecomposition3x3(M);

  // 按特征值排序（T > N > P，即从大到小）
  const sorted = values.map((val, idx) => ({ val, vec: vectors[idx] }))
    .sort((a, b) => b.val - a.val);

  return {
    t: normalize(sorted[0].vec),      // T轴（最大拉张）
    n: normalize(sorted[1].vec),      // N轴（中间轴）
    p: normalize(sorted[2].vec),      // P轴（最大压缩）
    tVal: sorted[0].val,
    nVal: sorted[1].val,
    pVal: sorted[2].val
  };
}

// 从主轴计算节面参数
function principalAxesToNodalPlanes(axes: PrincipalAxes): {
  strike1: number; dip1: number; rake1: number;
  strike2: number; dip2: number; rake2: number;
} {
  // 节面1的法向量 = (P + T) / sqrt(2)
  const n1 = normalize([
    (axes.p[0] + axes.t[0]) / Math.sqrt(2),
    (axes.p[1] + axes.t[1]) / Math.sqrt(2),
    (axes.p[2] + axes.t[2]) / Math.sqrt(2)
  ]);

  // 节面2的法向量 = (P - T) / sqrt(2)
  const n2 = normalize([
    (axes.p[0] - axes.t[0]) / Math.sqrt(2),
    (axes.p[1] - axes.t[1]) / Math.sqrt(2),
    (axes.p[2] - axes.t[2]) / Math.sqrt(2)
  ]);

  // 从法向量计算走向、倾角、滑动角
  function normalToStrikeDipRake(normal: number[]): { strike: number; dip: number; rake: number } {
    // 确保法向量指向下半球
    const n = normal[2] > 0 ? [-normal[0], -normal[1], -normal[2]] : normal;

    // 计算倾角
    const dip = Math.acos(Math.max(-n[2], 0)) * 180 / PI;

    // 计算走向
    let strike = Math.atan2(-n[0], n[1]) * 180 / PI;
    if (strike < 0) strike += 360;

    // 计算滑动角（使用N轴作为滑动方向）
    const slipDir = normalize(axes.n);
    const strikeVec = normalize([-Math.sin(strike * d2r), Math.cos(strike * d2r), 0]);
    const dipVec = normalize(cross(n, strikeVec));

    const rakeRad = Math.atan2(dot(slipDir, dipVec), dot(slipDir, strikeVec));
    let rake = rakeRad * 180 / PI;

    return { strike, dip, rake };
  }

  const plane1 = normalToStrikeDipRake(n1);
  const plane2 = normalToStrikeDipRake(n2);

  return {
    strike1: plane1.strike,
    dip1: plane1.dip,
    rake1: plane1.rake,
    strike2: plane2.strike,
    dip2: plane2.dip,
    rake2: plane2.rake
  };
}

// 计算节面法向量（NED坐标系）
function calculateNormal(strike: number, dip: number): number[] {
  const strikeRad = strike * d2r;
  const dipRad = dip * d2r;

  return [
    -Math.sin(dipRad) * Math.sin(strikeRad),
    Math.sin(dipRad) * Math.cos(strikeRad),
    -Math.cos(dipRad)
  ];
}

// 计算滑动向量
function calculateSlipVector(strike: number, dip: number, rake: number): number[] {
  const strikeRad = strike * d2r;
  const dipRad = dip * d2r;
  const rakeRad = rake * d2r;

  return [
    Math.cos(rakeRad) * Math.cos(strikeRad) + Math.sin(rakeRad) * Math.cos(dipRad) * Math.sin(strikeRad),
    Math.cos(rakeRad) * Math.sin(strikeRad) - Math.sin(rakeRad) * Math.cos(dipRad) * Math.cos(strikeRad),
    -Math.sin(rakeRad) * Math.sin(dipRad)
  ];
}

// 计算P轴、T轴和B轴
function calculatePTBAxes(strike: number, dip: number, rake: number): {
  pAxis: number[];
  tAxis: number[];
  bAxis: number[];
} {
  const normal = calculateNormal(strike, dip);
  const slip = calculateSlipVector(strike, dip, rake);

  // P轴 = normalize(normal - slip)
  const pAxis = normalize([
    normal[0] - slip[0],
    normal[1] - slip[1],
    normal[2] - slip[2]
  ]);

  // T轴 = normalize(normal + slip)
  const tAxis = normalize([
    normal[0] + slip[0],
    normal[1] + slip[1],
    normal[2] + slip[2]
  ]);

  // B轴 = P × T
  const bAxis = normalize(cross(pAxis, tAxis));

  return { pAxis, tAxis, bAxis };
}

// 计算辅助节面
function calculateAuxiliaryPlane(strike: number, dip: number, rake: number): {
  strike2: number;
  dip2: number;
  rake2: number;
} {
  const n1 = calculateNormal(strike, dip);
  const s1 = calculateSlipVector(strike, dip, rake);

  // 辅助节面的法向量应垂直于滑动向量且与主节面法向量不同，
  // 正确做法是取 n2 = s1 × n1（两节面的交线为滑动向量）
  let n2 = normalize(cross(s1, n1));

  // 保证法向量指向上半球（up 为负），否则取反
  if (n2[2] > 0) {
    n2 = [-n2[0], -n2[1], -n2[2]];
  }

  // 计算辅助节面的走向和倾角
  const dip2 = Math.acos(Math.max(-n2[2], -1)) * 180 / PI; // n2[2]≤0 保证 acos 输入≥0
  let strike2 = Math.atan2(-n2[0], n2[1]) * 180 / PI;
  if (strike2 < 0) strike2 += 360;

  // 计算辅助节面的滑动角
  const s2 = normalize(n1);
  const h2 = normalize(cross([0, 0, -1], n2));
  const cosRake2 = dot(s2, h2);
  const sinRake2 = -dot(s2, cross(n2, h2));
  let rake2 = Math.atan2(sinRake2, cosRake2) * 180 / PI;

  return { strike2, dip2, rake2 };
}

// 立体投影（下半球投影）
function stereographicProjection(x: number, y: number, z: number): { x: number; y: number } | null {
  // 只投影下半球的点
  if (z > 0) return null;

  const denom = 1 - z;
  if (Math.abs(denom) < 1e-10) return { x: 0, y: 0 };

  return {
    x: x / denom,
    y: -y / denom  // 负号是因为SVG的y轴向下
  };
}

// 判断点是否在压缩象限
function isCompression(x: number, y: number, z: number, pAxis: number[], tAxis: number[]): boolean {
  const v = [x, y, z];
  const pDot = dot(v, pAxis);
  const tDot = dot(v, tAxis);

  // 如果更接近P轴，则是压缩区域
  return Math.abs(pDot) > Math.abs(tDot);
}

// 使用矩阵张量判断点是否在压缩象限
function isCompressionMT(x: number, y: number, z: number, mt: MomentTensor): boolean {
  const v = [x, y, z]; // NED坐标
  // 计算 v^T * M * v
  const Mv = [
    mt.mxx * v[0] + mt.mxy * v[1] + mt.mxz * v[2],
    mt.mxy * v[0] + mt.myy * v[1] + mt.myz * v[2],
    mt.mxz * v[0] + mt.myz * v[1] + mt.mzz * v[2]
  ];
  const amplitude = v[0] * Mv[0] + v[1] * Mv[1] + v[2] * Mv[2];
  return amplitude > 0; // 正值表示压缩
}

// 生成圆弧路径
function generateArcPath(
  center: { x: number; y: number },
  radius: number,
  startAngle: number,
  endAngle: number,
  clockwise: boolean = true
): string {
  const start = {
    x: center.x + radius * Math.cos(startAngle),
    y: center.y + radius * Math.sin(startAngle)
  };

  const end = {
    x: center.x + radius * Math.cos(endAngle),
    y: center.y + radius * Math.sin(endAngle)
  };

  const largeArc = Math.abs(endAngle - startAngle) > PI ? 1 : 0;
  const sweep = clockwise ? 1 : 0;

  return `M ${start.x} ${start.y} A ${radius} ${radius} 0 ${largeArc} ${sweep} ${end.x} ${end.y}`;
}

// 生成节面在立体投影中的路径
function generateNodalPath(
  normal: number[],
  center: { x: number; y: number },
  radius: number
): string {
  // 新实现：在可见/不可见区间切换时使用多个子路径，避免错误连线
  let path = '';
  let inSegment = false;

  // 构建节面上的两个正交向量
  let v1: number[], v2: number[];

  if (Math.abs(normal[2]) > 0.9) {
    v1 = normalize(cross(normal, [1, 0, 0]));
  } else {
    v1 = normalize(cross(normal, [0, 0, 1]));
  }
  v2 = normalize(cross(normal, v1));

  const nPoints = 360; // 使用更高分辨率并遍历完整圆周
  for (let i = 0; i <= nPoints; i++) {
    const theta = (i / nPoints) * 2 * PI;
    const cosT = Math.cos(theta);
    const sinT = Math.sin(theta);

    const p = [
      cosT * v1[0] + sinT * v2[0],
      cosT * v1[1] + sinT * v2[1],
      cosT * v1[2] + sinT * v2[2]
    ];

    // stereographicProjection 期望 z 为"up"正向，而 p[2] 为"down"正向，因此取 -p[2]
    const proj = stereographicProjection(p[1], p[0], -p[2]);

    if (proj) {
      const x = center.x + proj.x * radius;
      const y = center.y + proj.y * radius;

      if (!inSegment) {
        // 开启新子路径
        path += `M ${x} ${y}`;
        inSegment = true;
      } else {
        path += ` L ${x} ${y}`;
      }
    } else {
      // 离开可见区，结束当前子路径
      inSegment = false;
    }
  }

  return path;
}

// 新增：生成双偶震源矩张量（NED坐标）
function momentTensorDC(strike: number, dip: number, rake: number): number[][] {
  const strikeRad = strike * d2r;
  const dipRad = dip * d2r;
  const rakeRad = rake * d2r;

  const sinS = Math.sin(strikeRad);
  const cosS = Math.cos(strikeRad);
  const sinD = Math.sin(dipRad);
  const cosD = Math.cos(dipRad);
  const sin2S = Math.sin(2 * strikeRad);
  const cos2S = Math.cos(2 * strikeRad);
  const sin2D = Math.sin(2 * dipRad);
  const cos2D = Math.cos(2 * dipRad);
  const sinL = Math.sin(rakeRad);
  const cosL = Math.cos(rakeRad);

  // 矩张量分量 (n, e, d)
  const Mnn = - (sinD * cosL * sin2S + sin2D * sinL * sinS * sinS);
  const Mee = (sinD * cosL * sin2S - sin2D * sinL * cosS * cosS);
  const Mdd = (sin2D * sinL);
  const Mne = (sinD * cosL * cos2S + 0.5 * sin2D * sinL * sin2S);
  const Mnd = - (cosD * cosL * cosS + cos2D * sinL * sinS);
  const Med = - (cosD * cosL * sinS - cos2D * sinL * cosS);

  return [
    [Mnn, Mne, Mnd],
    [Mne, Mee, Med],
    [Mnd, Med, Mdd]
  ];
}

// 更新压缩区域路径生成（使用矩张量）
function generateCompressionPaths(
  strike: number,
  dip: number,
  rake: number,
  center: { x: number; y: number },
  radius: number,
  compressColor: string = '#dc2626',
  resolution: number = 120
): string[] {
  const paths: string[] = [];
  const M = momentTensorDC(strike, dip, rake);

  function amplitude(v: number[]): number {
    // v 按 [north, east, down]
    return v[0] * (M[0][0] * v[0] + M[0][1] * v[1] + M[0][2] * v[2]) +
      v[1] * (M[1][0] * v[0] + M[1][1] * v[1] + M[1][2] * v[2]) +
      v[2] * (M[2][0] * v[0] + M[2][1] * v[1] + M[2][2] * v[2]);
  }

  for (let i = 0; i < resolution; i++) {
    for (let j = 0; j < resolution; j++) {
      const x = -1 + (i + 0.5) * 2 / resolution;
      const y = -1 + (j + 0.5) * 2 / resolution;
      const r2 = x * x + y * y;
      if (r2 > 1) continue;

      // 下半球立体反投影
      const denom = 1 + r2;
      const xe = (2 * x) / denom; // east
      const yn = (2 * y) / denom; // north (注意 y 对应 north)
      const zd = (1 - r2) / denom; // up 正向，下半球 zd 为负
      const v: number[] = [yn, xe, zd < 0 ? -zd : zd]; // 转为 [north, east, down]

      if (amplitude(v) > 0) {
        const px = center.x + x * radius;
        const py = center.y - y * radius;
        const cellSize = radius * 2 / resolution;
        paths.push(`<rect x="${px - cellSize / 2}" y="${py - cellSize / 2}" width="${cellSize}" height="${cellSize}" fill="${compressColor}"/>`);
      }
    }
  }

  return paths;
}

// 基于矩阵张量生成压缩区域
function generateCompressionPathsMT(
  mt: MomentTensor,
  center: { x: number; y: number },
  radius: number,
  compressColor: string = '#dc2626',
  resolution: number = 120
): string[] {
  const paths: string[] = [];

  for (let i = 0; i < resolution; i++) {
    for (let j = 0; j < resolution; j++) {
      const x = -1 + (i + 0.5) * 2 / resolution;
      const y = -1 + (j + 0.5) * 2 / resolution;
      const r2 = x * x + y * y;
      if (r2 > 1) continue;

      // 下半球立体反投影
      const denom = 1 + r2;
      const xe = (2 * x) / denom; // east
      const yn = (2 * y) / denom; // north
      const zd = (1 - r2) / denom; // up 正向，下半球 zd 为负
      const v: number[] = [yn, xe, zd < 0 ? -zd : zd]; // 转为 [north, east, down]

      if (isCompressionMT(v[0], v[1], v[2], mt)) {
        const px = center.x + x * radius;
        const py = center.y - y * radius;
        const cellSize = radius * 2 / resolution;
        paths.push(`<rect x="${px - cellSize / 2}" y="${py - cellSize / 2}" width="${cellSize}" height="${cellSize}" fill="${compressColor}"/>`);
      }
    }
  }

  return paths;
}

// 基于矩阵张量的节面路径生成（改进版）
function generateNodalPathsMT(
  mt: MomentTensor,
  center: { x: number; y: number },
  radius: number
): string[] {

  // 计算辐射幅度函数
  function getAmplitude(x: number, y: number): number {
    const r2 = x * x + y * y;
    if (r2 > 1) return 0;

    // 立体反投影到3D空间
    const denom = 1 + r2;
    const xe = (2 * x) / denom; // east
    const yn = (2 * y) / denom; // north
    const zd = (1 - r2) / denom; // down
    const v = [yn, xe, zd];

    // 计算真实振幅 v^T * M * v
    const Mv = [
      mt.mxx * v[0] + mt.mxy * v[1] + mt.mxz * v[2],
      mt.mxy * v[0] + mt.myy * v[1] + mt.myz * v[2],
      mt.mxz * v[0] + mt.myz * v[1] + mt.mzz * v[2]
    ];
    return v[0] * Mv[0] + v[1] * Mv[1] + v[2] * Mv[2];
  }

  // 使用 Marching Squares 算法生成连续等值线
  const gridSize = 150;
  const paths: string[] = [];

  // 构建振幅网格
  const grid: number[][] = [];
  for (let i = 0; i <= gridSize; i++) {
    grid[i] = [];
    for (let j = 0; j <= gridSize; j++) {
      const x = -1 + (i / gridSize) * 2;
      const y = -1 + (j / gridSize) * 2;
      const r2 = x * x + y * y;

      if (r2 > 1) {
        grid[i][j] = NaN;
      } else {
        grid[i][j] = getAmplitude(x, y);
      }
    }
  }

  // 等值线追踪 - 寻找amplitude = 0的边界
  const contourSegments: { start: { x: number, y: number }, end: { x: number, y: number } }[] = [];

  for (let i = 0; i < gridSize; i++) {
    for (let j = 0; j < gridSize; j++) {
      const v00 = grid[i][j];
      const v10 = grid[i + 1][j];
      const v01 = grid[i][j + 1];
      const v11 = grid[i + 1][j + 1];

      if (isNaN(v00) || isNaN(v10) || isNaN(v01) || isNaN(v11)) continue;

      const x1 = -1 + (i / gridSize) * 2;
      const y1 = -1 + (j / gridSize) * 2;
      const x2 = -1 + ((i + 1) / gridSize) * 2;
      const y2 = -1 + ((j + 1) / gridSize) * 2;

      const edges: { x: number, y: number }[] = [];

      // 检查四条边是否与零值线相交
      // 左边
      if ((v00 > 0 && v01 < 0) || (v00 < 0 && v01 > 0)) {
        const t = Math.abs(v00) / (Math.abs(v00) + Math.abs(v01));
        edges.push({ x: x1, y: y1 + t * (y2 - y1) });
      }

      // 右边
      if ((v10 > 0 && v11 < 0) || (v10 < 0 && v11 > 0)) {
        const t = Math.abs(v10) / (Math.abs(v10) + Math.abs(v11));
        edges.push({ x: x2, y: y1 + t * (y2 - y1) });
      }

      // 下边
      if ((v00 > 0 && v10 < 0) || (v00 < 0 && v10 > 0)) {
        const t = Math.abs(v00) / (Math.abs(v00) + Math.abs(v10));
        edges.push({ x: x1 + t * (x2 - x1), y: y1 });
      }

      // 上边
      if ((v01 > 0 && v11 < 0) || (v01 < 0 && v11 > 0)) {
        const t = Math.abs(v01) / (Math.abs(v01) + Math.abs(v11));
        edges.push({ x: x1 + t * (x2 - x1), y: y2 });
      }

      // 如果有两个交点，连接它们
      if (edges.length >= 2) {
        contourSegments.push({
          start: edges[0],
          end: edges[1]
        });
      }
    }
  }

  // 连接线段形成连续路径
  if (contourSegments.length === 0) {
    return [];
  }

  const tolerance = 0.02; // 连接容差
  const usedSegments = new Set<number>();

  // 构建连续路径
  for (let startIdx = 0; startIdx < contourSegments.length; startIdx++) {
    if (usedSegments.has(startIdx)) continue;

    const pathPoints: { x: number, y: number }[] = [];
    let currentSegment = contourSegments[startIdx];
    usedSegments.add(startIdx);

    pathPoints.push(currentSegment.start);
    pathPoints.push(currentSegment.end);

    // 尝试连接更多线段
    let foundConnection = true;
    while (foundConnection) {
      foundConnection = false;

      for (let i = 0; i < contourSegments.length; i++) {
        if (usedSegments.has(i)) continue;

        const segment = contourSegments[i];
        const lastPoint = pathPoints[pathPoints.length - 1];

        // 检查是否可以连接到当前路径的末端
        const distToStart = Math.sqrt(
          (lastPoint.x - segment.start.x) ** 2 + (lastPoint.y - segment.start.y) ** 2
        );
        const distToEnd = Math.sqrt(
          (lastPoint.x - segment.end.x) ** 2 + (lastPoint.y - segment.end.y) ** 2
        );

        if (distToStart < tolerance) {
          pathPoints.push(segment.end);
          usedSegments.add(i);
          foundConnection = true;
          break;
        } else if (distToEnd < tolerance) {
          pathPoints.push(segment.start);
          usedSegments.add(i);
          foundConnection = true;
          break;
        }
      }
    }

    // 生成SVG路径
    if (pathPoints.length >= 2) {
      let pathString = '';
      for (let i = 0; i < pathPoints.length; i++) {
        const px = center.x + pathPoints[i].x * radius;
        const py = center.y - pathPoints[i].y * radius;

        if (i === 0) {
          pathString += `M ${px.toFixed(2)} ${py.toFixed(2)}`;
        } else {
          pathString += ` L ${px.toFixed(2)} ${py.toFixed(2)}`;
        }
      }

      // 检查是否应该闭合路径
      const firstPoint = pathPoints[0];
      const lastPoint = pathPoints[pathPoints.length - 1];
      const closeDist = Math.sqrt(
        (firstPoint.x - lastPoint.x) ** 2 + (firstPoint.y - lastPoint.y) ** 2
      );

      if (closeDist < tolerance * 2) {
        pathString += ' Z';
      }

      paths.push(pathString);
    }
  }

  // 如果没有找到连续路径，使用简化方法
  if (paths.length === 0) {
    // 径向扫描备用方法
    const radialPath: { x: number, y: number }[] = [];

    for (let angle = 0; angle < 2 * Math.PI; angle += 0.05) {
      for (let r = 0.1; r <= 0.95; r += 0.02) {
        const x = r * Math.cos(angle);
        const y = r * Math.sin(angle);

        const amp1 = getAmplitude(x, y);
        const nextR = r + 0.02;
        const amp2 = getAmplitude(nextR * Math.cos(angle), nextR * Math.sin(angle));

        if ((amp1 > 0 && amp2 < 0) || (amp1 < 0 && amp2 > 0)) {
          // 找到零值点
          const t = Math.abs(amp1) / (Math.abs(amp1) + Math.abs(amp2));
          const zeroR = r + t * 0.02;
          const zeroX = zeroR * Math.cos(angle);
          const zeroY = zeroR * Math.sin(angle);

          radialPath.push({ x: zeroX, y: zeroY });
          break;
        }
      }
    }

    if (radialPath.length > 0) {
      let pathString = '';
      for (let i = 0; i < radialPath.length; i++) {
        const px = center.x + radialPath[i].x * radius;
        const py = center.y - radialPath[i].y * radius;

        if (i === 0) {
          pathString += `M ${px.toFixed(2)} ${py.toFixed(2)}`;
        } else {
          pathString += ` L ${px.toFixed(2)} ${py.toFixed(2)}`;
        }
      }
      pathString += ' Z'; // 闭合路径
      paths.push(pathString);
    }
  }

  return paths;
}

// SVG转PNG的函数
function svgToPng(svgString: string, size: number): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject('Canvas context not available');
      return;
    }

    // 使用3倍分辨率提高图片质量
    const scale = 3;
    const actualSize = size * scale;

    canvas.width = actualSize;
    canvas.height = actualSize;

    // 设置高质量渲染
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    const img = new Image();
    img.onload = () => {
      URL.revokeObjectURL(img.src);

      // 清空画布，保持透明背景
      ctx.clearRect(0, 0, actualSize, actualSize);

      // 绘制SVG到高分辨率canvas
      ctx.drawImage(img, 0, 0, actualSize, actualSize);

      // 转换为PNG数据URI（保持透明背景）
      const pngDataUri = canvas.toDataURL('image/png');
      resolve(pngDataUri);
    };

    img.onerror = () => {
      reject('Failed to load SVG');
    };

    // 创建SVG blob URL
    const svgBlob = new Blob([svgString], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(svgBlob);
    img.src = url;
  });
}

// 生成缓存键
function generateCacheKey(strike: number, dip: number, rake: number, size: number, compressColor?: string, dilatColor?: string): string {
  return `${strike.toFixed(0)}-${dip.toFixed(0)}-${rake.toFixed(0)}-${size}${compressColor ? `-${compressColor}` : ''}${dilatColor ? `-${dilatColor}` : ''}`;
}

// 基于矩阵张量的缓存键生成
function generateCacheKeyMT(mt: MomentTensor, size: number, compressColor?: string, dilatColor?: string): string {
  const mtStr = `${mt.mxx.toExponential(2)}-${mt.myy.toExponential(2)}-${mt.mzz.toExponential(2)}-${mt.mxy.toExponential(2)}-${mt.mxz.toExponential(2)}-${mt.myz.toExponential(2)}`;
  return `MT-${mtStr}-${size}${compressColor ? `-${compressColor}` : ''}${dilatColor ? `-${dilatColor}` : ''}`;
}

// 基础SVG生成函数
export function generatePyrockoBeachBallSVG(
  strike: number,
  dip: number,
  rake: number,
  size: number = 40,
  compressColor: string = '#dc2626',
  dilatColor: string = '#fff'
): string {
  // 清理过期缓存
  if (Math.random() < 0.01) {
    cleanExpiredCache();
  }

  const cacheKey = generateCacheKey(strike, dip, rake, size, compressColor, dilatColor);
  const cached = beachballCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.svg;
  }

  const radius = size / 2;
  const center = { x: radius, y: radius };

  // 计算法向量、滑动向量
  const normal1 = calculateNormal(strike, dip);
  const slip1 = calculateSlipVector(strike, dip, rake);
  // 辅助节面法向量 = slip × normal1
  let normal2 = cross(slip1, normal1);
  normal2 = normalize(normal2);
  // 确保向下半球
  if (normal2[2] > 0) {
    normal2 = [-normal2[0], -normal2[1], -normal2[2]];
  }

  // 第二节面法向量直接使用滑动向量
  normal2 = normalize(slip1);
  if (normal2[2] > 0) {
    normal2 = [-normal2[0], -normal2[1], -normal2[2]];
  }

  // 生成节面路径
  const nodePath1 = generateNodalPath(normal1, center, radius * 0.95);
  const nodePath2 = generateNodalPath(normal2, center, radius * 0.95);

  // 生成压缩区域
  const compressionPaths = generateCompressionPaths(strike, dip, rake, center, radius * 0.95, compressColor);

  const svg = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <clipPath id="clip-${cacheKey.replace(/[^a-zA-Z0-9]/g, '_')}">
          <circle cx="${center.x}" cy="${center.y}" r="${radius * 0.95}"/>
        </clipPath>
      </defs>
      
      <!-- 背景圆 -->
      <circle cx="${center.x}" cy="${center.y}" r="${radius * 0.95}" 
              fill="${dilatColor}" stroke="black" stroke-width="1"/>
      
      <!-- 压缩区域 -->
      <g clip-path="url(#clip-${cacheKey.replace(/[^a-zA-Z0-9]/g, '_')})">
        ${compressionPaths.join('')}
      </g>
      
      <!-- 节面线 -->
      <g clip-path="url(#clip-${cacheKey.replace(/[^a-zA-Z0-9]/g, '_')})">
        ${nodePath1 ? `<path d="${nodePath1}" fill="none" stroke="black" stroke-width="1.5"/>` : ''}
        ${nodePath2 ? `<path d="${nodePath2}" fill="none" stroke="black" stroke-width="1.5"/>` : ''}
      </g>
    </svg>
  `;

  const result = svg.trim();
  beachballCache.set(cacheKey, { svg: result, timestamp: Date.now() });
  return result;
}

// 基于矩阵张量的SVG生成函数
export function generateBeachBallSVGFromMT(
  mt: MomentTensor,
  size: number = 40,
  compressColor: string = '#dc2626',
  dilatColor: string = '#fff'
): string {
  // 清理过期缓存
  if (Math.random() < 0.01) {
    cleanExpiredCache();
  }

  const cacheKey = generateCacheKeyMT(mt, size, compressColor, dilatColor);
  const cached = beachballCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.svg;
  }

  const radius = size / 2;
  const center = { x: radius, y: radius };

  // 生成节面路径
  const nodalPaths = generateNodalPathsMT(mt, center, radius * 0.95);

  // 生成压缩区域
  const compressionPaths = generateCompressionPathsMT(mt, center, radius * 0.95, compressColor);

  const svg = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <clipPath id="clip-${cacheKey.replace(/[^a-zA-Z0-9]/g, '_')}">
          <circle cx="${center.x}" cy="${center.y}" r="${radius * 0.95}"/>
        </clipPath>
      </defs>
      
      <!-- 背景圆 -->
      <circle cx="${center.x}" cy="${center.y}" r="${radius * 0.95}" 
              fill="${dilatColor}" stroke="none"/>
      
      <!-- 压缩区域 -->
      <g clip-path="url(#clip-${cacheKey.replace(/[^a-zA-Z0-9]/g, '_')})">
        ${compressionPaths.join('')}
      </g>
      
      <!-- 节面线 - 放在最上层 -->
      <g clip-path="url(#clip-${cacheKey.replace(/[^a-zA-Z0-9]/g, '_')})">
        ${nodalPaths.map(path => `<path d="${path}" fill="none" stroke="black" stroke-width="1.5"/>`).join('')}
      </g>
      
      <!-- 外圆边框 - 确保圆形边界清晰 -->
      <circle cx="${center.x}" cy="${center.y}" r="${radius * 0.95}" 
              fill="none" stroke="black" stroke-width="1"/>
    </svg>
  `;

  const result = svg.trim();
  beachballCache.set(cacheKey, { svg: result, timestamp: Date.now() });
  return result;
}

// 获取或创建PNG
export async function getOrCreatePNG(
  strike: number,
  dip: number,
  rake: number,
  size: number,
  compressColor: string = '#dc2626',
  dilatColor: string = '#fff'
): Promise<string> {
  // 清理过期缓存
  if (Math.random() < 0.01) {
    cleanExpiredCache();
  }

  const cacheKey = generateCacheKey(strike, dip, rake, size, compressColor, dilatColor);
  const cached = pngCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.png;
  }

  try {
    const svgString = generatePyrockoBeachBallSVG(strike, dip, rake, size, compressColor, dilatColor);
    const pngDataUri = await svgToPng(svgString, size);
    pngCache.set(cacheKey, { png: pngDataUri, timestamp: Date.now() });
    return pngDataUri;
  } catch (error) {
    console.error('Failed to convert SVG to PNG:', error);
    // 降级到SVG
    const svgString = generatePyrockoBeachBallSVG(strike, dip, rake, size, compressColor, dilatColor);
    const svgDataUri = `data:image/svg+xml;base64,${btoa(svgString)}`;
    pngCache.set(cacheKey, { png: svgDataUri, timestamp: Date.now() });
    return svgDataUri;
  }
}

// 基于矩阵张量获取或创建PNG
export async function getOrCreatePNGFromMT(
  mt: MomentTensor,
  size: number,
  compressColor: string = '#dc2626',
  dilatColor: string = '#fff'
): Promise<string> {
  // 清理过期缓存
  if (Math.random() < 0.01) {
    cleanExpiredCache();
  }

  const cacheKey = generateCacheKeyMT(mt, size, compressColor, dilatColor);
  const cached = pngCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.png;
  }

  try {
    const svgString = generateBeachBallSVGFromMT(mt, size, compressColor, dilatColor);
    const pngDataUri = await svgToPng(svgString, size);
    pngCache.set(cacheKey, { png: pngDataUri, timestamp: Date.now() });
    return pngDataUri;
  } catch (error) {
    console.error('Failed to convert SVG to PNG:', error);
    // 降级到SVG
    const svgString = generateBeachBallSVGFromMT(mt, size, compressColor, dilatColor);
    const svgDataUri = `data:image/svg+xml;base64,${btoa(svgString)}`;
    pngCache.set(cacheKey, { png: svgDataUri, timestamp: Date.now() });
    return svgDataUri;
  }
}

// 从矩阵张量数组创建MomentTensor对象
export function createMomentTensorFromArray(mt: number[]): MomentTensor {
  if (mt.length !== 6) {
    throw new Error('矩阵张量数组必须包含6个分量');
  }
  return {
    mxx: mt[0],
    myy: mt[1],
    mzz: mt[2],
    mxy: mt[3],
    mxz: mt[4],
    myz: mt[5]
  };
}

// 清理缓存的公共函数
export function clearBeachballCache() {
  beachballCache.clear();
  pngCache.clear();
}

// 导出一个优化的版本，支持样式参数
export function generateStyledBeachBallSVG(
  strike: number,
  dip: number,
  rake: number,
  size: number = 40,
  compressColor: string = '#dc2626',
  dilatColor: string = '#fff',
  opacity: number = 1,
  strokeWidth: number = 1
): string {
  return generatePyrockoBeachBallSVG(strike, dip, rake, size, compressColor, dilatColor);
}

// 导出主要函数和类型
export { momentTensorToPrincipalAxes, principalAxesToNodalPlanes };
export type { PrincipalAxes };

// 三参数与矩阵张量的转换函数

// 从三参数转换到矩阵张量
export function strikesDipRakeToMomentTensor(
  strike: number,
  dip: number,
  rake: number,
  moment: number = 1.0
): MomentTensor {
  const strikeRad = strike * d2r;
  const dipRad = dip * d2r;
  const rakeRad = rake * d2r;

  const sinS = Math.sin(strikeRad);
  const cosS = Math.cos(strikeRad);
  const sinD = Math.sin(dipRad);
  const cosD = Math.cos(dipRad);
  const sin2S = Math.sin(2 * strikeRad);
  const cos2S = Math.cos(2 * strikeRad);
  const sin2D = Math.sin(2 * dipRad);
  const cos2D = Math.cos(2 * dipRad);
  const sinL = Math.sin(rakeRad);
  const cosL = Math.cos(rakeRad);

  // 矩张量分量 (NED坐标系)
  const Mnn = - (sinD * cosL * sin2S + sin2D * sinL * sinS * sinS);
  const Mee = (sinD * cosL * sin2S - sin2D * sinL * cosS * cosS);
  const Mdd = (sin2D * sinL);
  const Mne = (sinD * cosL * cos2S + 0.5 * sin2D * sinL * sin2S);
  const Mnd = - (cosD * cosL * cosS + cos2D * sinL * sinS);
  const Med = - (cosD * cosL * sinS - cos2D * sinL * cosS);

  return {
    mxx: Mnn * moment,
    myy: Mee * moment,
    mzz: Mdd * moment,
    mxy: Mne * moment,
    mxz: Mnd * moment,
    myz: Med * moment
  };
}

// 从矩阵张量转换到三参数
export function momentTensorToStrikeDipRake(mt: MomentTensor): {
  strike1: number; dip1: number; rake1: number;
  strike2: number; dip2: number; rake2: number;
  magnitude: number;
} {
  // 计算主轴
  const axes = momentTensorToPrincipalAxes(mt);

  // 计算节面参数
  const planes = principalAxesToNodalPlanes(axes);

  // 计算标量地震矩
  const M0 = Math.sqrt(axes.tVal * axes.tVal + axes.nVal * axes.nVal + axes.pVal * axes.pVal) / Math.sqrt(2);

  // 计算矩震级（粗略估算）
  const magnitude = (Math.log10(M0) - 16.1) / 1.5;

  return {
    strike1: planes.strike1,
    dip1: planes.dip1,
    rake1: planes.rake1,
    strike2: planes.strike2,
    dip2: planes.dip2,
    rake2: planes.rake2,
    magnitude: magnitude
  };
}

// 验证转换精度的函数
export function verifyConversion(
  originalStrike: number,
  originalDip: number,
  originalRake: number,
  tolerance: number = 5.0
): {
  isValid: boolean;
  results: {
    original: { strike: number; dip: number; rake: number };
    converted: { strike1: number; dip1: number; rake1: number; strike2: number; dip2: number; rake2: number };
    differences: { strike: number; dip: number; rake: number };
  };
} {
  // 转换：三参数 → 矩阵张量 → 三参数
  const mt = strikesDipRakeToMomentTensor(originalStrike, originalDip, originalRake);
  const converted = momentTensorToStrikeDipRake(mt);

  // 计算差异（考虑两个节面）
  const diff1 = {
    strike: Math.abs(normalizeAngle(originalStrike - converted.strike1)),
    dip: Math.abs(originalDip - converted.dip1),
    rake: Math.abs(normalizeAngle(originalRake - converted.rake1))
  };

  const diff2 = {
    strike: Math.abs(normalizeAngle(originalStrike - converted.strike2)),
    dip: Math.abs(originalDip - converted.dip2),
    rake: Math.abs(normalizeAngle(originalRake - converted.rake2))
  };

  // 选择更小的差异
  const bestDiff = (diff1.strike + diff1.dip + diff1.rake) < (diff2.strike + diff2.dip + diff2.rake) ? diff1 : diff2;

  // 检查是否在容差范围内
  const isValid = bestDiff.strike < tolerance && bestDiff.dip < tolerance && bestDiff.rake < tolerance;

  return {
    isValid,
    results: {
      original: { strike: originalStrike, dip: originalDip, rake: originalRake },
      converted: converted,
      differences: bestDiff
    }
  };
}

// 角度标准化函数
function normalizeAngle(angle: number): number {
  while (angle > 180) angle -= 360;
  while (angle < -180) angle += 360;
  return angle;
}

// 矩阵张量类型判断
export function classifyMomentTensor(mt: MomentTensor): {
  type: 'double-couple' | 'clvd' | 'isotropic' | 'mixed';
  dcPercentage: number;
  clvdPercentage: number;
  isoPercentage: number;
  description: string;
} {
  const axes = momentTensorToPrincipalAxes(mt);
  const { tVal, nVal, pVal } = axes;

  // 计算迹（各向同性分量）
  const trace = tVal + nVal + pVal;
  const iso = trace / 3;

  // 减去各向同性分量
  const devT = tVal - iso;
  const devN = nVal - iso;
  const devP = pVal - iso;

  // 计算CLVD分量
  let clvd = 2 * Math.min(Math.abs(devT), Math.abs(devP));
  if (devN > 0) clvd = -clvd;

  // 计算DC分量
  const dcT = devT - clvd / 2;
  const dcP = devP - clvd / 2;

  // 计算各分量的相对大小
  const totalMoment = Math.sqrt(tVal * tVal + nVal * nVal + pVal * pVal);
  const dcMoment = Math.sqrt(dcT * dcT + dcP * dcP);
  const clvdMoment = Math.abs(clvd) * Math.sqrt(3) / 2;
  const isoMoment = Math.abs(iso) * Math.sqrt(3);

  const dcPercentage = (dcMoment / totalMoment) * 100;
  const clvdPercentage = (clvdMoment / totalMoment) * 100;
  const isoPercentage = (isoMoment / totalMoment) * 100;

  // 分类
  let type: 'double-couple' | 'clvd' | 'isotropic' | 'mixed';
  let description: string;

  if (dcPercentage > 90) {
    type = 'double-couple';
    description = '双偶震源（纯剪切断层）';
  } else if (clvdPercentage > 50) {
    type = 'clvd';
    description = '补偿线性向量偶极子（CLVD）';
  } else if (isoPercentage > 50) {
    type = 'isotropic';
    description = '各向同性震源（爆炸或收缩）';
  } else {
    type = 'mixed';
    description = '混合震源类型';
  }

  return {
    type,
    dcPercentage,
    clvdPercentage,
    isoPercentage,
    description
  };
}
