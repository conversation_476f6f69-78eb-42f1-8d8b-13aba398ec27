/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        }
      },
      animation: {
        'slide-in-right': 'slideInRight 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
        'slide-out-right': 'slideOutRight 0.25s cubic-bezier(0.4, 0, 1, 1)',
        'bounce-in': 'bounceIn 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        'bounce-out': 'bounceOut 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53)',
      },
      keyframes: {
        slideInRight: {
          '0%': {
            transform: 'translateX(100%) scale(0.9)',
            opacity: '0'
          },
          '100%': {
            transform: 'translateX(0) scale(1)',
            opacity: '1'
          },
        },
        slideOutRight: {
          '0%': {
            transform: 'translateX(0) scale(1)',
            opacity: '1'
          },
          '100%': {
            transform: 'translateX(100%) scale(0.9)',
            opacity: '0'
          },
        },
        bounceIn: {
          '0%': {
            transform: 'translateX(100%) scale(0.3)',
            opacity: '0'
          },
          '50%': {
            transform: 'translateX(-10%) scale(1.05)',
            opacity: '0.8'
          },
          '70%': {
            transform: 'translateX(5%) scale(0.98)',
            opacity: '0.9'
          },
          '100%': {
            transform: 'translateX(0) scale(1)',
            opacity: '1'
          },
        },
        bounceOut: {
          '0%': {
            transform: 'translateX(0) scale(1)',
            opacity: '1'
          },
          '30%': {
            transform: 'translateX(-5%) scale(1.02)',
            opacity: '0.8'
          },
          '100%': {
            transform: 'translateX(100%) scale(0.8)',
            opacity: '0'
          },
        },
      }
    },
  },
  plugins: [],
}