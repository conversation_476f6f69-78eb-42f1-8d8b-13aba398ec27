import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import tailwindcss from '@tailwindcss/vite';
import svgr from 'vite-plugin-svgr'
import { autoSpaRoutes } from './vite-plugins/auto-spa-routes'

export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    svgr(),
    // 🚀 智能SPA路由生成器 - 自动检测路由并生成对应HTML文件
    autoSpaRoutes(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    }
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true
      }
    }
  }
})