version: '3.8'

services:
  # RiseMap 应用服务
  risemap:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: risemap-app
    restart: unless-stopped
    ports:
      - "3001:3001"  # 统一服务端口（前端通过后端静态文件服务）
    environment:
      - NODE_ENV=production
      - PORT=3001
      - STATIC_PATH=/app/apps/api/static
      - DB_PATH=/app/data/earthquakes.db
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    volumes:
      # 持久化数据库文件
      - risemap_data:/app/data
      # 持久化日志文件
      - risemap_logs:/app/logs
    networks:
      - risemap-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: risemap-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - risemap
    networks:
      - risemap-network
    profiles:
      - nginx

# 网络配置
networks:
  risemap-network:
    driver: bridge

# 数据卷配置
volumes:
  risemap_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  risemap_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
