module.exports = {
  apps: [
    {
      name: 'rise-map-unified',
      script: './dist/server.js',
      cwd: './apps/api',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        STATIC_PATH: '../web/dist'
      },
      // 日志配置
      log_file: './logs/api-combined.log',
      out_file: './logs/api-out.log',
      error_file: './logs/api-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

      // 进程管理
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      max_memory_restart: '1G',

      // 自动重启配置
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',

      // 其他配置
      merge_logs: true,
      time: true
    },
    {
      name: 'cloudflared-tunnel',
      script: 'cloudflared',
      args: 'tunnel --config cloudflared.yml run',
      cwd: './',
      instances: 1,
      exec_mode: 'fork',
      env_file: './.env',
      // 日志配置
      log_file: './logs/cloudflared-combined.log',
      out_file: './logs/cloudflared-out.log',
      error_file: './logs/cloudflared-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // 进程管理
      watch: false,
      max_memory_restart: '200M',
      
      // 自动重启配置
      autorestart: true,
      max_restarts: 10,
      min_uptime: '10s',
      
      // 其他配置
      merge_logs: true,
      time: true
    },
  ],

  // 部署配置
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server-ip'],
      ref: 'origin/main',
      repo: 'https://github.com/your-username/rise-map-fullstack.git',
      path: '/var/www/rise-map',
      'pre-deploy-local': '',
      'post-deploy': 'pnpm install:all && pnpm build:prod && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    staging: {
      user: 'deploy',
      host: ['your-staging-server-ip'],
      ref: 'origin/develop',
      repo: 'https://github.com/your-username/rise-map-fullstack.git',
      path: '/var/www/rise-map-staging',
      'pre-deploy-local': '',
      'post-deploy': 'pnpm install:all && pnpm build:prod && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
