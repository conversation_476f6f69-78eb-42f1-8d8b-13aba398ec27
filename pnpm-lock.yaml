lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      dotenv:
        specifier: ^17.0.0
        version: 17.2.0
    devDependencies:
      concurrently:
        specifier: ^8.2.0
        version: 8.2.2
      serve:
        specifier: ^14.2.1
        version: 14.2.4
      typescript:
        specifier: ^5.3.0
        version: 5.8.3

  apps/api:
    dependencies:
      '@fastify/cors':
        specifier: ^9.0.1
        version: 9.0.1
      '@fastify/jwt':
        specifier: ^8.0.1
        version: 8.0.1
      '@fastify/static':
        specifier: ^7.0.0
        version: 7.0.4
      '@fastify/swagger':
        specifier: ^8.15.0
        version: 8.15.0
      '@fastify/swagger-ui':
        specifier: ^4.1.0
        version: 4.2.0
      bcryptjs:
        specifier: ^2.4.3
        version: 2.4.3
      better-sqlite3:
        specifier: ^12.2.0
        version: 12.2.0
      fastify:
        specifier: ^4.28.1
        version: 4.29.1
    devDependencies:
      '@types/bcryptjs':
        specifier: ^2.4.6
        version: 2.4.6
      '@types/better-sqlite3':
        specifier: ^7.6.13
        version: 7.6.13
      '@types/node':
        specifier: ^20.14.10
        version: 20.19.7
      tsx:
        specifier: ^4.16.2
        version: 4.20.3
      typescript:
        specifier: ^5.5.3
        version: 5.8.3

  apps/web:
    dependencies:
      '@deck.gl/core':
        specifier: ^9.1.13
        version: 9.1.13
      '@deck.gl/geo-layers':
        specifier: ^9.1.13
        version: 9.1.13(@deck.gl/core@9.1.13)(@deck.gl/extensions@9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/mesh-layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/layers':
        specifier: ^9.1.13
        version: 9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/mapbox':
        specifier: ^9.1.13
        version: 9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)
      '@deck.gl/react':
        specifier: ^9.1.13
        version: 9.1.13(@deck.gl/core@9.1.13)(@deck.gl/widgets@9.1.13(@deck.gl/core@9.1.13))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@headlessui/react':
        specifier: ^2.2.6
        version: 2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-three/drei':
        specifier: 9.88.13
        version: 9.88.13(@react-three/fiber@8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0))(@types/three@0.152.0)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0)
      '@react-three/fiber':
        specifier: 8.15.19
        version: 8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0)
      '@tanstack/react-query':
        specifier: ^5.83.0
        version: 5.83.0(react@18.3.1)
      '@turf/circle':
        specifier: ^7.2.0
        version: 7.2.0
      '@turf/distance':
        specifier: ^7.2.0
        version: 7.2.0
      '@turf/turf':
        specifier: ^7.2.0
        version: 7.2.0
      '@types/three':
        specifier: 0.152.0
        version: 0.152.0
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      d3:
        specifier: ^7.9.0
        version: 7.9.0
      date-fns:
        specifier: ^2.30.0
        version: 2.30.0
      deck.gl:
        specifier: ^9.1.13
        version: 9.1.13(@arcgis/core@4.33.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))(@luma.gl/webgl@9.1.9(@luma.gl/core@9.1.9))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      echarts:
        specifier: ^5.6.0
        version: 5.6.0
      echarts-for-react:
        specifier: ^3.0.2
        version: 3.0.2(echarts@5.6.0)(react@18.3.1)
      lucide-react:
        specifier: ^0.292.0
        version: 0.292.0(react@18.3.1)
      maplibre-gl:
        specifier: ^5.6.1
        version: 5.6.1
      react:
        specifier: ^18.3.1
        version: 18.3.1
      react-dnd:
        specifier: ^16.0.1
        version: 16.0.1(@types/node@20.19.9)(@types/react@18.3.23)(react@18.3.1)
      react-dnd-html5-backend:
        specifier: ^16.0.1
        version: 16.0.1
      react-dom:
        specifier: ^18.3.1
        version: 18.3.1(react@18.3.1)
      react-hook-form:
        specifier: ^7.61.1
        version: 7.61.1(react@18.3.1)
      react-map-gl:
        specifier: ^8.0.4
        version: 8.0.4(maplibre-gl@5.6.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-router-dom:
        specifier: ^6.30.1
        version: 6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      tailwindcss:
        specifier: ^4.1.11
        version: 4.1.11
      terra-draw:
        specifier: ^1.10.0
        version: 1.10.0
      terra-draw-maplibre-gl-adapter:
        specifier: ^1.1.1
        version: 1.1.1(maplibre-gl@5.6.1)(terra-draw@1.10.0)
      three:
        specifier: 0.152.0
        version: 0.152.0
      tone:
        specifier: ^15.1.22
        version: 15.1.22
      zod:
        specifier: ^3.25.76
        version: 3.25.76
      zustand:
        specifier: ^5.0.6
        version: 5.0.6(@types/react@18.3.23)(react@18.3.1)(use-sync-external-store@1.5.0(react@18.3.1))
    devDependencies:
      '@tailwindcss/vite':
        specifier: ^4.1.11
        version: 4.1.11(vite@7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))
      '@types/d3':
        specifier: ^7.4.3
        version: 7.4.3
      '@types/node':
        specifier: ^20.19.9
        version: 20.19.9
      '@types/react':
        specifier: ^18.3.23
        version: 18.3.23
      '@types/react-dom':
        specifier: ^18.3.7
        version: 18.3.7(@types/react@18.3.23)
      '@vitejs/plugin-react':
        specifier: ^4.7.0
        version: 4.7.0(vite@7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))
      typescript:
        specifier: ^5.8.3
        version: 5.8.3
      vite:
        specifier: ^7.0.6
        version: 7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
      vite-plugin-svgr:
        specifier: ^4.3.0
        version: 4.3.0(rollup@4.45.1)(typescript@5.8.3)(vite@7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))

packages:

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@arcgis/components-utils@4.33.11':
    resolution: {integrity: sha512-NOez9VqrPj8Aewpp+QOhysnKxd4Fq77pLlixwjoasX7jYwblaeO92AgZHRHyu8VTYnXE2t4zepPndmQRevEUhg==}

  '@arcgis/core@4.33.9':
    resolution: {integrity: sha512-I6O43kuo53MhDl9ACcc+RFa//vol650XoFjmo6lwqga47lFMULZfHkh5tf9UCEVu8RXPaeQGb2mrrZhXJk6a7Q==}

  '@arcgis/lumina@4.33.11':
    resolution: {integrity: sha512-jjMBlPU1JLSf1FlG1gRsowb5He3xLSnfuY8Ytm8yUN0sK9XAC/4oiJ4c8mHGedScN0Bi3plJsqYcfRsLV3O6jg==}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.28.0':
    resolution: {integrity: sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.28.0':
    resolution: {integrity: sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.28.0':
    resolution: {integrity: sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-globals@7.28.0':
    resolution: {integrity: sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.3':
    resolution: {integrity: sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.28.2':
    resolution: {integrity: sha512-/V9771t+EgXz62aCcyofnQhGM8DQACbRhvzKFsXKC9QM+5MadF8ZmIm0crDMaz3+o0h0zXfJnd4EhbYbxsrcFw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.28.0':
    resolution: {integrity: sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-transform-react-jsx-self@7.27.1':
    resolution: {integrity: sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.27.1':
    resolution: {integrity: sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.28.2':
    resolution: {integrity: sha512-KHp2IflsnGywDjBWDkR9iEqiWSpc8GIi0lgTT3mOElT0PP1tG26P4tmFI2YvAdzgq9RGyoHZQEIEdZy6Ec5xCA==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.28.0':
    resolution: {integrity: sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.28.2':
    resolution: {integrity: sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ==}
    engines: {node: '>=6.9.0'}

  '@carto/api-client@0.4.9':
    resolution: {integrity: sha512-W0kjEmWgRA7Mqh7jdvHZ0/WHz3ACFsmnUkGLnPT7BJjkIBS/WtCsx2YXjV4lGi/eA1NZtZ7pU+sq3Z7OPgb/fw==}

  '@deck.gl/aggregation-layers@9.1.13':
    resolution: {integrity: sha512-eDuT4S7GRx8LWdPuxGIiK8MfBynfvj3PgNB5mB1uiXcp1OR2eZ17wr3QBp1Rdk4LUsx1P1CkDyyIvi5mn4+aQA==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@deck.gl/layers': ^9.1.0
      '@luma.gl/core': ^9.1.5
      '@luma.gl/engine': ^9.1.5

  '@deck.gl/arcgis@9.1.13':
    resolution: {integrity: sha512-Ow592W5O968M0e/b0AOiHWWfSBJmiaAQoeW/9xYKgbkwy+hRsv7NArpHOmhKvT+X0w+jlzpu1LJLmTHnS/lQBA==}
    peerDependencies:
      '@arcgis/core': ^4.0.0
      '@deck.gl/core': ^9.1.0
      '@luma.gl/core': ^9.1.5
      '@luma.gl/engine': ^9.1.5
      '@luma.gl/webgl': ^9.1.5

  '@deck.gl/carto@9.1.13':
    resolution: {integrity: sha512-azB5AiZlSJXm1wa1PzNPrXaESVFQkzLk5ysGFXpmS1f+W/aBY626N1cstyUSsofqSVzKh/AseMZ6ti6PdQ5Ckg==}
    peerDependencies:
      '@deck.gl/aggregation-layers': ^9.1.0
      '@deck.gl/core': ^9.1.0
      '@deck.gl/extensions': ^9.1.0
      '@deck.gl/geo-layers': ^9.1.0
      '@deck.gl/layers': ^9.1.0
      '@loaders.gl/core': ^4.2.0
      '@luma.gl/core': ^9.1.5

  '@deck.gl/core@9.1.13':
    resolution: {integrity: sha512-c15DpwUEvDjmt3+/azSjcfhVQ5L5HiIj6LJob1KAwQOnB5zgVdKWukN/21ELQ7ekppEkfT0x4byRv5k4QVocqQ==}

  '@deck.gl/extensions@9.1.13':
    resolution: {integrity: sha512-Y6XCjXckcXyU+NhaDW4GA6nw9BAanFKNtltHcR+GUivGiK+QuBXlIggl+QLkWXD1EKp3os/DOM8kO0FmtAaC9A==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@luma.gl/core': ^9.1.5
      '@luma.gl/engine': ^9.1.5

  '@deck.gl/geo-layers@9.1.13':
    resolution: {integrity: sha512-+6GLQacUzQHcGraKCuDV6z1U44mJ08eg2/gaQGDJYUwh+YUCiyW1uWey5GvV9nRcaS47UApYxEDQZGpSamZT+A==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@deck.gl/extensions': ^9.1.0
      '@deck.gl/layers': ^9.1.0
      '@deck.gl/mesh-layers': ^9.1.0
      '@loaders.gl/core': ^4.2.0
      '@luma.gl/core': ^9.1.5
      '@luma.gl/engine': ^9.1.5

  '@deck.gl/google-maps@9.1.13':
    resolution: {integrity: sha512-flVQkWaotaChvB5yiK3ANlo4ErinrRF9Ph2y3EOvt18ljEKhUE7vwdK7yU1Vorq0GurvDhL6vZvov9xasaULEw==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@luma.gl/core': ^9.1.5
      '@luma.gl/webgl': ^9.1.5

  '@deck.gl/json@9.1.13':
    resolution: {integrity: sha512-tDmo4t5DwDVInh6IqBa6YD9kpTh6CJWriYIomUIqsX9U3YHJfBbdMw5XqR8dU6oBn/gqgkYP9bDjxVAJJzwR9w==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0

  '@deck.gl/layers@9.1.13':
    resolution: {integrity: sha512-2eD2uARmObtCXrc1Q051fqy+LS2w6a700qPerqtqz+J/bOWTHSEZxAdIoHawDU7g+fi4/1lti0m8bdp2X/kZLA==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@loaders.gl/core': ^4.2.0
      '@luma.gl/core': ^9.1.5
      '@luma.gl/engine': ^9.1.5

  '@deck.gl/mapbox@9.1.13':
    resolution: {integrity: sha512-Ab2U36YyOUmxfglaBDpudkJJO1EaVchxcrNpA0yAaelLMdkFwaHS4Hu1TMm5iT3TzhiLyMrAViaV4R47ZgZ78g==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@luma.gl/core': ^9.1.5

  '@deck.gl/mesh-layers@9.1.13':
    resolution: {integrity: sha512-ujhe9FtB4qRRCXH/hY5p+IQ5VO/AC+/dtern6CTzYzjGnUnAvsbIgBZ3jxSlb1B/D3wlVE778W2cmv7MIToJJg==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@luma.gl/core': ^9.1.5
      '@luma.gl/engine': ^9.1.5

  '@deck.gl/react@9.1.13':
    resolution: {integrity: sha512-9tu5roGzvy4plLvUVUxDpEy3KHDxpAEBRdM/qC06Ijognfl/A9bECdQt4cqqGflkvg6VzQaw2Cy7eIRvzhXJbA==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0
      '@deck.gl/widgets': ^9.1.0
      react: '>=16.3.0'
      react-dom: '>=16.3.0'

  '@deck.gl/widgets@9.1.13':
    resolution: {integrity: sha512-6nriLKNzXovWrm4Lj9MAdYf2W9/bSwJ1Rlq4jc8WvrOr1wtIJ7j6NdHlfGUs2Vv1PLt72M0jSqMwHQQevLvsqQ==}
    peerDependencies:
      '@deck.gl/core': ^9.1.0

  '@esbuild/aix-ppc64@0.25.6':
    resolution: {integrity: sha512-ShbM/3XxwuxjFiuVBHA+d3j5dyac0aEVVq1oluIDf71hUw0aRF59dV/efUsIwFnR6m8JNM2FjZOzmaZ8yG61kw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/aix-ppc64@0.25.8':
    resolution: {integrity: sha512-urAvrUedIqEiFR3FYSLTWQgLu5tb+m0qZw0NBEasUeo6wuqatkMDaRT+1uABiGXEu5vqgPd7FGE1BhsAIy9QVA==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.6':
    resolution: {integrity: sha512-hd5zdUarsK6strW+3Wxi5qWws+rJhCCbMiC9QZyzoxfk5uHRIE8T287giQxzVpEvCwuJ9Qjg6bEjcRJcgfLqoA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm64@0.25.8':
    resolution: {integrity: sha512-OD3p7LYzWpLhZEyATcTSJ67qB5D+20vbtr6vHlHWSQYhKtzUYrETuWThmzFpZtFsBIxRvhO07+UgVA9m0i/O1w==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.6':
    resolution: {integrity: sha512-S8ToEOVfg++AU/bHwdksHNnyLyVM+eMVAOf6yRKFitnwnbwwPNqKr3srzFRe7nzV69RQKb5DgchIX5pt3L53xg==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-arm@0.25.8':
    resolution: {integrity: sha512-RONsAvGCz5oWyePVnLdZY/HHwA++nxYWIX1atInlaW6SEkwq6XkP3+cb825EUcRs5Vss/lGh/2YxAb5xqc07Uw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.6':
    resolution: {integrity: sha512-0Z7KpHSr3VBIO9A/1wcT3NTy7EB4oNC4upJ5ye3R7taCc2GUdeynSLArnon5G8scPwaU866d3H4BCrE5xLW25A==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/android-x64@0.25.8':
    resolution: {integrity: sha512-yJAVPklM5+4+9dTeKwHOaA+LQkmrKFX96BM0A/2zQrbS6ENCmxc4OVoBs5dPkCCak2roAD+jKCdnmOqKszPkjA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.6':
    resolution: {integrity: sha512-FFCssz3XBavjxcFxKsGy2DYK5VSvJqa6y5HXljKzhRZ87LvEi13brPrf/wdyl/BbpbMKJNOr1Sd0jtW4Ge1pAA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-arm64@0.25.8':
    resolution: {integrity: sha512-Jw0mxgIaYX6R8ODrdkLLPwBqHTtYHJSmzzd+QeytSugzQ0Vg4c5rDky5VgkoowbZQahCbsv1rT1KW72MPIkevw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.6':
    resolution: {integrity: sha512-GfXs5kry/TkGM2vKqK2oyiLFygJRqKVhawu3+DOCk7OxLy/6jYkWXhlHwOoTb0WqGnWGAS7sooxbZowy+pK9Yg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.8':
    resolution: {integrity: sha512-Vh2gLxxHnuoQ+GjPNvDSDRpoBCUzY4Pu0kBqMBDlK4fuWbKgGtmDIeEC081xi26PPjn+1tct+Bh8FjyLlw1Zlg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.6':
    resolution: {integrity: sha512-aoLF2c3OvDn2XDTRvn8hN6DRzVVpDlj2B/F66clWd/FHLiHaG3aVZjxQX2DYphA5y/evbdGvC6Us13tvyt4pWg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-arm64@0.25.8':
    resolution: {integrity: sha512-YPJ7hDQ9DnNe5vxOm6jaie9QsTwcKedPvizTVlqWG9GBSq+BuyWEDazlGaDTC5NGU4QJd666V0yqCBL2oWKPfA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.6':
    resolution: {integrity: sha512-2SkqTjTSo2dYi/jzFbU9Plt1vk0+nNg8YC8rOXXea+iA3hfNJWebKYPs3xnOUf9+ZWhKAaxnQNUf2X9LOpeiMQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.8':
    resolution: {integrity: sha512-MmaEXxQRdXNFsRN/KcIimLnSJrk2r5H8v+WVafRWz5xdSVmWLoITZQXcgehI2ZE6gioE6HirAEToM/RvFBeuhw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.6':
    resolution: {integrity: sha512-b967hU0gqKd9Drsh/UuAm21Khpoh6mPBSgz8mKRq4P5mVK8bpA+hQzmm/ZwGVULSNBzKdZPQBRT3+WuVavcWsQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm64@0.25.8':
    resolution: {integrity: sha512-WIgg00ARWv/uYLU7lsuDK00d/hHSfES5BzdWAdAig1ioV5kaFNrtK8EqGcUBJhYqotlUByUKz5Qo6u8tt7iD/w==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.6':
    resolution: {integrity: sha512-SZHQlzvqv4Du5PrKE2faN0qlbsaW/3QQfUUc6yO2EjFcA83xnwm91UbEEVx4ApZ9Z5oG8Bxz4qPE+HFwtVcfyw==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-arm@0.25.8':
    resolution: {integrity: sha512-FuzEP9BixzZohl1kLf76KEVOsxtIBFwCaLupVuk4eFVnOZfU+Wsn+x5Ryam7nILV2pkq2TqQM9EZPsOBuMC+kg==}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.6':
    resolution: {integrity: sha512-aHWdQ2AAltRkLPOsKdi3xv0mZ8fUGPdlKEjIEhxCPm5yKEThcUjHpWB1idN74lfXGnZ5SULQSgtr5Qos5B0bPw==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-ia32@0.25.8':
    resolution: {integrity: sha512-A1D9YzRX1i+1AJZuFFUMP1E9fMaYY+GnSQil9Tlw05utlE86EKTUA7RjwHDkEitmLYiFsRd9HwKBPEftNdBfjg==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.6':
    resolution: {integrity: sha512-VgKCsHdXRSQ7E1+QXGdRPlQ/e08bN6WMQb27/TMfV+vPjjTImuT9PmLXupRlC90S1JeNNW5lzkAEO/McKeJ2yg==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-loong64@0.25.8':
    resolution: {integrity: sha512-O7k1J/dwHkY1RMVvglFHl1HzutGEFFZ3kNiDMSOyUrB7WcoHGf96Sh+64nTRT26l3GMbCW01Ekh/ThKM5iI7hQ==}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.6':
    resolution: {integrity: sha512-WViNlpivRKT9/py3kCmkHnn44GkGXVdXfdc4drNmRl15zVQ2+D2uFwdlGh6IuK5AAnGTo2qPB1Djppj+t78rzw==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.8':
    resolution: {integrity: sha512-uv+dqfRazte3BzfMp8PAQXmdGHQt2oC/y2ovwpTteqrMx2lwaksiFZ/bdkXJC19ttTvNXBuWH53zy/aTj1FgGw==}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.6':
    resolution: {integrity: sha512-wyYKZ9NTdmAMb5730I38lBqVu6cKl4ZfYXIs31Baf8aoOtB4xSGi3THmDYt4BTFHk7/EcVixkOV2uZfwU3Q2Jw==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.8':
    resolution: {integrity: sha512-GyG0KcMi1GBavP5JgAkkstMGyMholMDybAf8wF5A70CALlDM2p/f7YFE7H92eDeH/VBtFJA5MT4nRPDGg4JuzQ==}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.6':
    resolution: {integrity: sha512-KZh7bAGGcrinEj4qzilJ4hqTY3Dg2U82c8bv+e1xqNqZCrCyc+TL9AUEn5WGKDzm3CfC5RODE/qc96OcbIe33w==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.8':
    resolution: {integrity: sha512-rAqDYFv3yzMrq7GIcen3XP7TUEG/4LK86LUPMIz6RT8A6pRIDn0sDcvjudVZBiiTcZCY9y2SgYX2lgK3AF+1eg==}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.6':
    resolution: {integrity: sha512-9N1LsTwAuE9oj6lHMyyAM+ucxGiVnEqUdp4v7IaMmrwb06ZTEVCIs3oPPplVsnjPfyjmxwHxHMF8b6vzUVAUGw==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-s390x@0.25.8':
    resolution: {integrity: sha512-Xutvh6VjlbcHpsIIbwY8GVRbwoviWT19tFhgdA7DlenLGC/mbc3lBoVb7jxj9Z+eyGqvcnSyIltYUrkKzWqSvg==}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.6':
    resolution: {integrity: sha512-A6bJB41b4lKFWRKNrWoP2LHsjVzNiaurf7wyj/XtFNTsnPuxwEBWHLty+ZE0dWBKuSK1fvKgrKaNjBS7qbFKig==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/linux-x64@0.25.8':
    resolution: {integrity: sha512-ASFQhgY4ElXh3nDcOMTkQero4b1lgubskNlhIfJrsH5OKZXDpUAKBlNS0Kx81jwOBp+HCeZqmoJuihTv57/jvQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.6':
    resolution: {integrity: sha512-IjA+DcwoVpjEvyxZddDqBY+uJ2Snc6duLpjmkXm/v4xuS3H+3FkLZlDm9ZsAbF9rsfP3zeA0/ArNDORZgrxR/Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-arm64@0.25.8':
    resolution: {integrity: sha512-d1KfruIeohqAi6SA+gENMuObDbEjn22olAR7egqnkCD9DGBG0wsEARotkLgXDu6c4ncgWTZJtN5vcgxzWRMzcw==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.6':
    resolution: {integrity: sha512-dUXuZr5WenIDlMHdMkvDc1FAu4xdWixTCRgP7RQLBOkkGgwuuzaGSYcOpW4jFxzpzL1ejb8yF620UxAqnBrR9g==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.8':
    resolution: {integrity: sha512-nVDCkrvx2ua+XQNyfrujIG38+YGyuy2Ru9kKVNyh5jAys6n+l44tTtToqHjino2My8VAY6Lw9H7RI73XFi66Cg==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.6':
    resolution: {integrity: sha512-l8ZCvXP0tbTJ3iaqdNf3pjaOSd5ex/e6/omLIQCVBLmHTlfXW3zAxQ4fnDmPLOB1x9xrcSi/xtCWFwCZRIaEwg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-arm64@0.25.8':
    resolution: {integrity: sha512-j8HgrDuSJFAujkivSMSfPQSAa5Fxbvk4rgNAS5i3K+r8s1X0p1uOO2Hl2xNsGFppOeHOLAVgYwDVlmxhq5h+SQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.6':
    resolution: {integrity: sha512-hKrmDa0aOFOr71KQ/19JC7az1P0GWtCN1t2ahYAf4O007DHZt/dW8ym5+CUdJhQ/qkZmI1HAF8KkJbEFtCL7gw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.8':
    resolution: {integrity: sha512-1h8MUAwa0VhNCDp6Af0HToI2TJFAn1uqT9Al6DJVzdIBAd21m/G0Yfc77KDM3uF3T/YaOgQq3qTJHPbTOInaIQ==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/openharmony-arm64@0.25.6':
    resolution: {integrity: sha512-+SqBcAWoB1fYKmpWoQP4pGtx+pUUC//RNYhFdbcSA16617cchuryuhOCRpPsjCblKukAckWsV+aQ3UKT/RMPcA==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]

  '@esbuild/openharmony-arm64@0.25.8':
    resolution: {integrity: sha512-r2nVa5SIK9tSWd0kJd9HCffnDHKchTGikb//9c7HX+r+wHYCpQrSgxhlY6KWV1nFo1l4KFbsMlHk+L6fekLsUg==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openharmony]

  '@esbuild/sunos-x64@0.25.6':
    resolution: {integrity: sha512-dyCGxv1/Br7MiSC42qinGL8KkG4kX0pEsdb0+TKhmJZgCUDBGmyo1/ArCjNGiOLiIAgdbWgmWgib4HoCi5t7kA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/sunos-x64@0.25.8':
    resolution: {integrity: sha512-zUlaP2S12YhQ2UzUfcCuMDHQFJyKABkAjvO5YSndMiIkMimPmxA+BYSBikWgsRpvyxuRnow4nS5NPnf9fpv41w==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.6':
    resolution: {integrity: sha512-42QOgcZeZOvXfsCBJF5Afw73t4veOId//XD3i+/9gSkhSV6Gk3VPlWncctI+JcOyERv85FUo7RxuxGy+z8A43Q==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-arm64@0.25.8':
    resolution: {integrity: sha512-YEGFFWESlPva8hGL+zvj2z/SaK+pH0SwOM0Nc/d+rVnW7GSTFlLBGzZkuSU9kFIGIo8q9X3ucpZhu8PDN5A2sQ==}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.6':
    resolution: {integrity: sha512-4AWhgXmDuYN7rJI6ORB+uU9DHLq/erBbuMoAuB4VWJTu5KtCgcKYPynF0YI1VkBNuEfjNlLrFr9KZPJzrtLkrQ==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-ia32@0.25.8':
    resolution: {integrity: sha512-hiGgGC6KZ5LZz58OL/+qVVoZiuZlUYlYHNAmczOm7bs2oE1XriPFi5ZHHrS8ACpV5EjySrnoCKmcbQMN+ojnHg==}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.6':
    resolution: {integrity: sha512-NgJPHHbEpLQgDH2MjQu90pzW/5vvXIZ7KOnPyNBm92A6WgZ/7b6fJyUBjoumLqeOQQGqY2QjQxRo97ah4Sj0cA==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@esbuild/win32-x64@0.25.8':
    resolution: {integrity: sha512-cn3Yr7+OaaZq1c+2pe+8yxC8E144SReCQjN6/2ynubzYjvyqZjTXfQJpAcQpsdJq3My7XADANiYGHoFC69pLQw==}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@esri/arcgis-html-sanitizer@4.1.0':
    resolution: {integrity: sha512-einEveDJ/k1180NOp78PB/4Hje9eBy3dyOGLLtLn6bSkizpUfCwuYBIXOA7Y3F/k/BsTQXgKqUVwQ0eiscWMdA==}
    engines: {node: '>=18.0.0'}

  '@esri/calcite-components@3.2.1':
    resolution: {integrity: sha512-NRBW/bhT4sQM5RAZF7W8/VymaOLgIzaR6yTZFI270Ig4NiQ4DRexjdeo/SwDMBlswFtBw5iya+/h+fAur2+Hlg==}

  '@esri/calcite-ui-icons@4.2.0':
    resolution: {integrity: sha512-GS41gUt1tgnqG+U1a6yDRiKOHmuLST2uyOI7+cJ83JtLJ7CGduH9K6RERabTE2vRYbudaebI8jZgKNSNOHdGzw==}
    hasBin: true

  '@fastify/accept-negotiator@1.1.0':
    resolution: {integrity: sha512-OIHZrb2ImZ7XG85HXOONLcJWGosv7sIvM2ifAPQVhg9Lv7qdmMBNVaai4QTdyuaqbKM5eO6sLSQOYI7wEQeCJQ==}
    engines: {node: '>=14'}

  '@fastify/ajv-compiler@3.6.0':
    resolution: {integrity: sha512-LwdXQJjmMD+GwLOkP7TVC68qa+pSSogeWWmznRJ/coyTcfe9qA05AHFSe1eZFwK6q+xVRpChnvFUkf1iYaSZsQ==}

  '@fastify/cors@9.0.1':
    resolution: {integrity: sha512-YY9Ho3ovI+QHIL2hW+9X4XqQjXLjJqsU+sMV/xFsxZkE8p3GNnYVFpoOxF7SsP5ZL76gwvbo3V9L+FIekBGU4Q==}

  '@fastify/error@3.4.1':
    resolution: {integrity: sha512-wWSvph+29GR783IhmvdwWnN4bUxTD01Vm5Xad4i7i1VuAOItLvbPAb69sb0IQ2N57yprvhNIwAP5B6xfKTmjmQ==}

  '@fastify/fast-json-stringify-compiler@4.3.0':
    resolution: {integrity: sha512-aZAXGYo6m22Fk1zZzEUKBvut/CIIQe/BapEORnxiD5Qr0kPHqqI69NtEMCme74h+at72sPhbkb4ZrLd1W3KRLA==}

  '@fastify/jwt@8.0.1':
    resolution: {integrity: sha512-295bd7V6bDCnZOu8MAQgM6r7V1KILB+kdEq1q6nbHfXCnML569n7NSo3WzeLDG6IAqDl+Rhzi1vjxwaNHhRCBA==}

  '@fastify/merge-json-schemas@0.1.1':
    resolution: {integrity: sha512-fERDVz7topgNjtXsJTTW1JKLy0rhuLRcquYqNR9rF7OcVpCa2OVW49ZPDIhaRRCaUuvVxI+N416xUoF76HNSXA==}

  '@fastify/send@2.1.0':
    resolution: {integrity: sha512-yNYiY6sDkexoJR0D8IDy3aRP3+L4wdqCpvx5WP+VtEU58sn7USmKynBzDQex5X42Zzvw2gNzzYgP90UfWShLFA==}

  '@fastify/static@7.0.4':
    resolution: {integrity: sha512-p2uKtaf8BMOZWLs6wu+Ihg7bWNBdjNgCwDza4MJtTqg+5ovKmcbgbR9Xs5/smZ1YISfzKOCNYmZV8LaCj+eJ1Q==}

  '@fastify/swagger-ui@4.2.0':
    resolution: {integrity: sha512-pVutmTm49Pn98FS01E2m+eUH0WGhsHlImowWr9PXQt3rQPArSsocON8qF/8mm0dNLmilwtJZJqdsvFTnCUcapw==}

  '@fastify/swagger@8.15.0':
    resolution: {integrity: sha512-zy+HEEKFqPMS2sFUsQU5X0MHplhKJvWeohBwTCkBAJA/GDYGLGUWQaETEhptiqxK7Hs0fQB9B4MDb3pbwIiCwA==}

  '@floating-ui/core@1.7.2':
    resolution: {integrity: sha512-wNB5ooIKHQc+Kui96jE/n69rHFWAVoxn5CAzL1Xdd8FG03cgY3MLO+GF9U3W737fYDSgPWA6MReKhBQBop6Pcw==}

  '@floating-ui/dom@1.7.2':
    resolution: {integrity: sha512-7cfaOQuCS27HD7DX+6ib2OrnW+b4ZBwDNnCcT0uTyidcmyWb03FnQqJybDBoCnpdxwBSfA94UAYlRCt7mV+TbA==}

  '@floating-ui/react-dom@2.1.4':
    resolution: {integrity: sha512-JbbpPhp38UmXDDAu60RJmbeme37Jbgsm7NrHGgzYYFKmblzRUh6Pa641dII6LsjwF4XlScDrde2UAzDo/b9KPw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/react@0.26.28':
    resolution: {integrity: sha512-yORQuuAtVpiRjpMhdc0wJj06b9JFjrYF4qp96j++v2NBpbi6SEGF7donUJ3TMieerQ6qVkAv1tgr7L4r5roTqw==}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.10':
    resolution: {integrity: sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ==}

  '@headlessui/react@2.2.6':
    resolution: {integrity: sha512-gN5CT8Kf4IWwL04GQOjZ/ZnHMFoeFHZmVSFoDKeTmbtmy9oFqQqJMthdBiO3Pl5LXk2w03fGFLpQV6EW84vjjQ==}
    engines: {node: '>=10'}
    peerDependencies:
      react: ^18 || ^19 || ^19.0.0-rc
      react-dom: ^18 || ^19 || ^19.0.0-rc

  '@interactjs/types@1.10.27':
    resolution: {integrity: sha512-BUdv0cvs4H5ODuwft2Xp4eL8Vmi3LcihK42z0Ft/FbVJZoRioBsxH+LlsBdK4tAie7PqlKGy+1oyOncu1nQ6eA==}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@isaacs/fs-minipass@4.0.1':
    resolution: {integrity: sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==}
    engines: {node: '>=18.0.0'}

  '@jridgewell/gen-mapping@0.3.12':
    resolution: {integrity: sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg==}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.4':
    resolution: {integrity: sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==}

  '@jridgewell/trace-mapping@0.3.29':
    resolution: {integrity: sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ==}

  '@lit-labs/ssr-client@1.1.7':
    resolution: {integrity: sha512-VvqhY/iif3FHrlhkzEPsuX/7h/NqnfxLwVf0p8ghNIlKegRyRqgeaJevZ57s/u/LiFyKgqksRP5n+LmNvpxN+A==}

  '@lit-labs/ssr-dom-shim@1.4.0':
    resolution: {integrity: sha512-ficsEARKnmmW5njugNYKipTm4SFnbik7CXtoencDZzmzo/dQ+2Q0bgkzJuoJP20Aj0F+izzJjOqsnkd6F/o1bw==}

  '@lit-labs/ssr@3.3.1':
    resolution: {integrity: sha512-JlF1PempxvzrGEpRFrF+Ki0MHzR3HA51SK8Zv0cFpW9p0bPW4k0FeCwrElCu371UEpXF7RcaE2wgYaE1az0XKg==}
    engines: {node: '>=13.9.0'}

  '@lit/context@1.1.6':
    resolution: {integrity: sha512-M26qDE6UkQbZA2mQ3RjJ3Gzd8TxP+/0obMgE5HfkfLhEEyYE3Bui4A5XHiGPjy0MUGAyxB3QgVuw2ciS0kHn6A==}

  '@lit/reactive-element@2.1.1':
    resolution: {integrity: sha512-N+dm5PAYdQ8e6UlywyyrgI2t++wFGXfHx+dSJ1oBrg6FAxUj40jId++EaRm80MKX5JnlH1sBsyZ5h0bcZKemCg==}

  '@loaders.gl/3d-tiles@4.3.4':
    resolution: {integrity: sha512-JQ3y3p/KlZP7lfobwON5t7H9WinXEYTvuo3SRQM8TBKhM+koEYZhvI2GwzoXx54MbBbY+s3fm1dq5UAAmaTsZw==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/compression@4.3.4':
    resolution: {integrity: sha512-+o+5JqL9Sx8UCwdc2MTtjQiUHYQGJALHbYY/3CT+b9g/Emzwzez2Ggk9U9waRfdHiBCzEgRBivpWZEOAtkimXQ==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/core@4.3.4':
    resolution: {integrity: sha512-cG0C5fMZ1jyW6WCsf4LoHGvaIAJCEVA/ioqKoYRwoSfXkOf+17KupK1OUQyUCw5XoRn+oWA1FulJQOYlXnb9Gw==}

  '@loaders.gl/crypto@4.3.4':
    resolution: {integrity: sha512-3VS5FgB44nLOlAB9Q82VOQnT1IltwfRa1miE0mpHCe1prYu1M/dMnEyynusbrsp+eDs3EKbxpguIS9HUsFu5dQ==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/draco@4.3.4':
    resolution: {integrity: sha512-4Lx0rKmYENGspvcgV5XDpFD9o+NamXoazSSl9Oa3pjVVjo+HJuzCgrxTQYD/3JvRrolW/QRehZeWD/L/cEC6mw==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/gis@4.3.4':
    resolution: {integrity: sha512-8xub38lSWW7+ZXWuUcggk7agRHJUy6RdipLNKZ90eE0ZzLNGDstGD1qiBwkvqH0AkG+uz4B7Kkiptyl7w2Oa6g==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/gltf@4.3.4':
    resolution: {integrity: sha512-EiUTiLGMfukLd9W98wMpKmw+hVRhQ0dJ37wdlXK98XPeGGB+zTQxCcQY+/BaMhsSpYt/OOJleHhTfwNr8RgzRg==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/images@4.3.4':
    resolution: {integrity: sha512-qgc33BaNsqN9cWa/xvcGvQ50wGDONgQQdzHCKDDKhV2w/uptZoR5iofJfuG8UUV2vUMMd82Uk9zbopRx2rS4Ag==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/loader-utils@4.3.4':
    resolution: {integrity: sha512-tjMZvlKQSaMl2qmYTAxg+ySR6zd6hQn5n3XaU8+Ehp90TD3WzxvDKOMNDqOa72fFmIV+KgPhcmIJTpq4lAdC4Q==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/math@4.3.4':
    resolution: {integrity: sha512-UJrlHys1fp9EUO4UMnqTCqvKvUjJVCbYZ2qAKD7tdGzHJYT8w/nsP7f/ZOYFc//JlfC3nq+5ogvmdpq2pyu3TA==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/mvt@4.3.4':
    resolution: {integrity: sha512-9DrJX8RQf14htNtxsPIYvTso5dUce9WaJCWCIY/79KYE80Be6dhcEYMknxBS4w3+PAuImaAe66S5xo9B7Erm5A==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/schema@4.3.4':
    resolution: {integrity: sha512-1YTYoatgzr/6JTxqBLwDiD3AVGwQZheYiQwAimWdRBVB0JAzych7s1yBuE0CVEzj4JDPKOzVAz8KnU1TiBvJGw==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/terrain@4.3.4':
    resolution: {integrity: sha512-JszbRJGnxL5Fh82uA2U8HgjlsIpzYoCNNjy3cFsgCaxi4/dvjz3BkLlBilR7JlbX8Ka+zlb4GAbDDChiXLMJ/g==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/textures@4.3.4':
    resolution: {integrity: sha512-arWIDjlE7JaDS6v9by7juLfxPGGnjT9JjleaXx3wq/PTp+psLOpGUywHXm38BNECos3MFEQK3/GFShWI+/dWPw==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/tiles@4.3.4':
    resolution: {integrity: sha512-oC0zJfyvGox6Ag9ABF8fxOkx9yEFVyzTa9ryHXl2BqLiQoR1v3p+0tIJcEbh5cnzHfoTZzUis1TEAZluPRsHBQ==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/wms@4.3.4':
    resolution: {integrity: sha512-yXF0wuYzJUdzAJQrhLIua6DnjOiBJusaY1j8gpvuH1VYs3mzvWlIRuZKeUd9mduQZKK88H2IzHZbj2RGOauq4w==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/worker-utils@4.3.4':
    resolution: {integrity: sha512-EbsszrASgT85GH3B7jkx7YXfQyIYo/rlobwMx6V3ewETapPUwdSAInv+89flnk5n2eu2Lpdeh+2zS6PvqbL2RA==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/xml@4.3.4':
    resolution: {integrity: sha512-p+y/KskajsvyM3a01BwUgjons/j/dUhniqd5y1p6keLOuwoHlY/TfTKd+XluqfyP14vFrdAHCZTnFCWLblN10w==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@loaders.gl/zip@4.3.4':
    resolution: {integrity: sha512-bHY4XdKYJm3vl9087GMoxnUqSURwTxPPh6DlAGOmz6X9Mp3JyWuA2gk3tQ1UIuInfjXKph3WAUfGe6XRIs1sfw==}
    peerDependencies:
      '@loaders.gl/core': ^4.3.0

  '@lukeed/ms@2.0.2':
    resolution: {integrity: sha512-9I2Zn6+NJLfaGoz9jN3lpwDgAYvfGeNYdbAIjJOqzs4Tpc+VU3Jqq4IofSUBKajiDS8k9fZIg18/z13mpk1bsA==}
    engines: {node: '>=8'}

  '@luma.gl/constants@9.1.9':
    resolution: {integrity: sha512-yc9fml04OeTTcwK+7gmDMxoLQ67j4ZiAFXjmYvPomYyBVzS0NZxTDuwcCBmnxjLOiroOZW8FRRrVc/yOiFug2w==}

  '@luma.gl/core@9.1.9':
    resolution: {integrity: sha512-1i9N7+I/UbFjx3axSMlc3/NufA+C2iBv/7mw51gRE/ypQPgvFmY/QqXBVZRe+nthF+OhlUMhO19TBndzYFTWhA==}

  '@luma.gl/engine@9.1.9':
    resolution: {integrity: sha512-n1GLK1sUMFkWxdb+aZYn6ZBFltFEMi7X+6ZPxn2pBsNT6oeF4AyvH5AyqhOpvHvUnCLDt3Zsf1UIfx3MI//YSw==}
    peerDependencies:
      '@luma.gl/core': ^9.1.0
      '@luma.gl/shadertools': ^9.1.0

  '@luma.gl/gltf@9.1.9':
    resolution: {integrity: sha512-KgVBIFCtRO1oadgMDycMJA5s+q519l/fQBGAZpUcLfWsaEDQfdHW2NLdrK/00VDv46Ng8tN/O6uyH6E40uLcLw==}
    peerDependencies:
      '@luma.gl/core': ^9.1.0
      '@luma.gl/engine': ^9.1.0
      '@luma.gl/shadertools': ^9.1.0

  '@luma.gl/shadertools@9.1.9':
    resolution: {integrity: sha512-Uqp2xfgIEunRMLXTeCJ4uEMlWcUGcYMZGJ8GAOrAeDzn4bMKVRKmZDC71vkuTctnaodM3UdrI9W6s1sJlrXsxw==}
    peerDependencies:
      '@luma.gl/core': ^9.1.0

  '@luma.gl/webgl@9.1.9':
    resolution: {integrity: sha512-jecHjhNSWkXH0v62rM6G5fIIkOmsrND27099iKgdutFvHIvd4QS4UzGWEEa9AEPlP0rTLqXkA6y6YL7f42ZkVg==}
    peerDependencies:
      '@luma.gl/core': ^9.1.0

  '@mapbox/geojson-rewind@0.5.2':
    resolution: {integrity: sha512-tJaT+RbYGJYStt7wI3cq4Nl4SXxG8W7JDG5DMJu97V25RnbNg3QtQtf+KD+VLjNpWKYsRvXDNmNrBgEETr1ifA==}
    hasBin: true

  '@mapbox/jsonlint-lines-primitives@2.0.2':
    resolution: {integrity: sha512-rY0o9A5ECsTQRVhv7tL/OyDpGAoUB4tTvLiW1DSzQGq4bvTPhNw1VpSNjDJc5GFZ2XuyOtSWSVN05qOtcD71qQ==}
    engines: {node: '>= 0.6'}

  '@mapbox/martini@0.2.0':
    resolution: {integrity: sha512-7hFhtkb0KTLEls+TRw/rWayq5EeHtTaErgm/NskVoXmtgAQu/9D299aeyj6mzAR/6XUnYRp2lU+4IcrYRFjVsQ==}

  '@mapbox/point-geometry@0.1.0':
    resolution: {integrity: sha512-6j56HdLTwWGO0fJPlrZtdU/B13q8Uwmo18Ck2GnGgN9PCFyKTZ3UbXeEdRFh18i9XQ92eH2VdtpJHpBD3aripQ==}

  '@mapbox/tiny-sdf@2.0.6':
    resolution: {integrity: sha512-qMqa27TLw+ZQz5Jk+RcwZGH7BQf5G/TrutJhspsca/3SHwmgKQ1iq+d3Jxz5oysPVYTGP6aXxCo5Lk9Er6YBAA==}

  '@mapbox/unitbezier@0.0.1':
    resolution: {integrity: sha512-nMkuDXFv60aBr9soUG5q+GvZYL+2KZHVvsqFCzqnkGEf46U2fvmytHaEVc1/YZbiLn8X+eR3QzX1+dwDO1lxlw==}

  '@mapbox/vector-tile@1.3.1':
    resolution: {integrity: sha512-MCEddb8u44/xfQ3oD+Srl/tNcQoqTw3goGk2oLsrFxOTc3dUp+kAnby3PvAeeBYSMSjSPD1nd1AJA6W49WnoUw==}

  '@mapbox/whoots-js@3.1.0':
    resolution: {integrity: sha512-Es6WcD0nO5l+2BOQS4uLfNPYQaNDfbot3X1XUoloz+x0mPDS3eeORZJl06HXjwBG1fOGwCRnzK88LMdxKRrd6Q==}
    engines: {node: '>=6.0.0'}

  '@maplibre/maplibre-gl-style-spec@19.3.3':
    resolution: {integrity: sha512-cOZZOVhDSulgK0meTsTkmNXb1ahVvmTmWmfx9gRBwc6hq98wS9JP35ESIoNq3xqEan+UN+gn8187Z6E4NKhLsw==}
    hasBin: true

  '@maplibre/maplibre-gl-style-spec@23.3.0':
    resolution: {integrity: sha512-IGJtuBbaGzOUgODdBRg66p8stnwj9iDXkgbYKoYcNiiQmaez5WVRfXm4b03MCDwmZyX93csbfHFWEJJYHnn5oA==}
    hasBin: true

  '@math.gl/core@4.1.0':
    resolution: {integrity: sha512-FrdHBCVG3QdrworwrUSzXIaK+/9OCRLscxI2OUy6sLOHyHgBMyfnEGs99/m3KNvs+95BsnQLWklVfpKfQzfwKA==}

  '@math.gl/culling@4.1.0':
    resolution: {integrity: sha512-jFmjFEACnP9kVl8qhZxFNhCyd47qPfSVmSvvjR0/dIL6R9oD5zhR1ub2gN16eKDO/UM7JF9OHKU3EBIfeR7gtg==}

  '@math.gl/geospatial@4.1.0':
    resolution: {integrity: sha512-BzsUhpVvnmleyYF6qdqJIip6FtIzJmnWuPTGhlBuPzh7VBHLonCFSPtQpbkRuoyAlbSyaGXcVt6p6lm9eK2vtg==}

  '@math.gl/polygon@4.1.0':
    resolution: {integrity: sha512-YA/9PzaCRHbIP5/0E9uTYrqe+jsYTQoqoDWhf6/b0Ixz8bPZBaGDEafLg3z7ffBomZLacUty9U3TlPjqMtzPjA==}

  '@math.gl/sun@4.1.0':
    resolution: {integrity: sha512-i3q6OCBLSZ5wgZVhXg+X7gsjY/TUtuFW/2KBiq/U1ypLso3S4sEykoU/MGjxUv1xiiGtr+v8TeMbO1OBIh/HmA==}

  '@math.gl/types@4.1.0':
    resolution: {integrity: sha512-clYZdHcmRvMzVK5fjeDkQlHUzXQSNdZ7s4xOqC3nJPgz4C/TZkUecTo9YS4PruZqtDda/ag4erndP0MIn40dGA==}

  '@math.gl/web-mercator@4.1.0':
    resolution: {integrity: sha512-HZo3vO5GCMkXJThxRJ5/QYUYRr3XumfT8CzNNCwoJfinxy5NtKUd7dusNTXn7yJ40UoB8FMIwkVwNlqaiRZZAw==}

  '@mediapipe/tasks-vision@0.10.2':
    resolution: {integrity: sha512-d8Q9uRK89ZRWmED2JLI9/blpJcfdbh0iEUuMo8TgkMzNfQBY1/GC0FEJWrairTwHkxIf6Oud1vFBP+aHicWqJA==}

  '@open-wc/dedupe-mixin@1.4.0':
    resolution: {integrity: sha512-Sj7gKl1TLcDbF7B6KUhtvr+1UCxdhMbNY5KxdU5IfMFWqL8oy1ZeAcCANjoB1TL0AJTcPmcCFsCbHf8X2jGDUA==}

  '@parse5/tools@0.3.0':
    resolution: {integrity: sha512-zxRyTHkqb7WQMV8kTNBKWb1BeOFUKXBXTBWuxg9H9hfvQB3IwP6Iw2U75Ia5eyRxPNltmY7E8YAlz6zWwUnjKg==}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@polymer/polymer@3.5.2':
    resolution: {integrity: sha512-fWwImY/UH4bb2534DVSaX+Azs2yKg8slkMBHOyGeU2kKx7Xmxp6Lee0jP8p6B3d7c1gFUPB2Z976dTUtX81pQA==}

  '@probe.gl/env@4.1.0':
    resolution: {integrity: sha512-5ac2Jm2K72VCs4eSMsM7ykVRrV47w32xOGMvcgqn8vQdEMF9PRXyBGYEV9YbqRKWNKpNKmQJVi4AHM/fkCxs9w==}

  '@probe.gl/log@4.1.0':
    resolution: {integrity: sha512-r4gRReNY6f+OZEMgfWEXrAE2qJEt8rX0HsDJQXUBMoc+5H47bdB7f/5HBHAmapK8UydwPKL9wCDoS22rJ0yq7Q==}

  '@probe.gl/stats@4.1.0':
    resolution: {integrity: sha512-EI413MkWKBDVNIfLdqbeNSJTs7ToBz/KVGkwi3D+dQrSIkRI2IYbWGAU3xX+D6+CI4ls8ehxMhNpUVMaZggDvQ==}

  '@react-aria/focus@3.21.0':
    resolution: {integrity: sha512-7NEGtTPsBy52EZ/ToVKCu0HSelE3kq9qeis+2eEq90XSuJOMaDHUQrA7RC2Y89tlEwQB31bud/kKRi9Qme1dkA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/interactions@3.25.4':
    resolution: {integrity: sha512-HBQMxgUPHrW8V63u9uGgBymkMfj6vdWbB0GgUJY49K9mBKMsypcHeWkWM6+bF7kxRO728/IK8bWDV6whDbqjHg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/ssr@3.9.10':
    resolution: {integrity: sha512-hvTm77Pf+pMBhuBm760Li0BVIO38jv1IBws1xFm1NoL26PU+fe+FMW5+VZWyANR6nYL65joaJKZqOdTQMkO9IQ==}
    engines: {node: '>= 12'}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-aria/utils@3.30.0':
    resolution: {integrity: sha512-ydA6y5G1+gbem3Va2nczj/0G0W7/jUVo/cbN10WA5IizzWIwMP5qhFr7macgbKfHMkZ+YZC3oXnt2NNre5odKw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1
      react-dom: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-dnd/asap@5.0.2':
    resolution: {integrity: sha512-WLyfoHvxhs0V9U+GTsGilGgf2QsPl6ZZ44fnv0/b8T3nQyvzxidxsg/ZltbWssbsRDlYW8UKSQMTGotuTotZ6A==}

  '@react-dnd/invariant@4.0.2':
    resolution: {integrity: sha512-xKCTqAK/FFauOM9Ta2pswIyT3D8AQlfrYdOi/toTPEhqCuAs1v5tcJ3Y08Izh1cJ5Jchwy9SeAXmMg6zrKs2iw==}

  '@react-dnd/shallowequal@4.0.2':
    resolution: {integrity: sha512-/RVXdLvJxLg4QKvMoM5WlwNR9ViO9z8B/qPcc+C0Sa/teJY7QG7kJ441DwzOjMYEY7GmU4dj5EcGHIkKZiQZCA==}

  '@react-spring/animated@9.6.1':
    resolution: {integrity: sha512-ls/rJBrAqiAYozjLo5EPPLLOb1LM0lNVQcXODTC1SMtS6DbuBCPaKco5svFUQFMP2dso3O+qcC4k9FsKc0KxMQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  '@react-spring/core@9.6.1':
    resolution: {integrity: sha512-3HAAinAyCPessyQNNXe5W0OHzRfa8Yo5P748paPcmMowZ/4sMfaZ2ZB6e5x5khQI8NusOHj8nquoutd6FRY5WQ==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  '@react-spring/rafz@9.6.1':
    resolution: {integrity: sha512-v6qbgNRpztJFFfSE3e2W1Uz+g8KnIBs6SmzCzcVVF61GdGfGOuBrbjIcp+nUz301awVmREKi4eMQb2Ab2gGgyQ==}

  '@react-spring/shared@9.6.1':
    resolution: {integrity: sha512-PBFBXabxFEuF8enNLkVqMC9h5uLRBo6GQhRMQT/nRTnemVENimgRd+0ZT4yFnAQ0AxWNiJfX3qux+bW2LbG6Bw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  '@react-spring/three@9.6.1':
    resolution: {integrity: sha512-Tyw2YhZPKJAX3t2FcqvpLRb71CyTe1GvT3V+i+xJzfALgpk10uPGdGaQQ5Xrzmok1340DAeg2pR/MCfaW7b8AA==}
    peerDependencies:
      '@react-three/fiber': '>=6.0'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0
      three: '>=0.126'

  '@react-spring/types@9.6.1':
    resolution: {integrity: sha512-POu8Mk0hIU3lRXB3bGIGe4VHIwwDsQyoD1F394OK7STTiX9w4dG3cTLljjYswkQN+hDSHRrj4O36kuVa7KPU8Q==}

  '@react-stately/flags@3.1.2':
    resolution: {integrity: sha512-2HjFcZx1MyQXoPqcBGALwWWmgFVUk2TuKVIQxCbRq7fPyWXIl6VHcakCLurdtYC2Iks7zizvz0Idv48MQ38DWg==}

  '@react-stately/utils@3.10.8':
    resolution: {integrity: sha512-SN3/h7SzRsusVQjQ4v10LaVsDc81jyyR0DD5HnsQitm/I5WDpaSr2nRHtyloPFU48jlql1XX/S04T2DLQM7Y3g==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@react-three/drei@9.88.13':
    resolution: {integrity: sha512-oxXqHnt3SXHm2ICh1CChBGLhwqHOooxibPYm4x0Yk+L1JopqdWVBSZIw+MjsUsQeDj0Tl5fizAXnS594NQxAYQ==}
    peerDependencies:
      '@react-three/fiber': '>=8.0'
      react: '>=18.0'
      react-dom: '>=18.0'
      three: '>=0.137'
    peerDependenciesMeta:
      react-dom:
        optional: true

  '@react-three/fiber@8.15.19':
    resolution: {integrity: sha512-WbFU7T6485v8Onnp+JJnrzKFvvGP7OFyJmHlqXiXc2RcXl9Sax+ykJxiNwEXWjGjcgF9/KTfv0+pAVkP0vZlKg==}
    peerDependencies:
      expo: '>=43.0'
      expo-asset: '>=8.4'
      expo-file-system: '>=11.0'
      expo-gl: '>=11.0'
      react: '>=18.0'
      react-dom: '>=18.0'
      react-native: '>=0.64'
      three: '>=0.133'
    peerDependenciesMeta:
      expo:
        optional: true
      expo-asset:
        optional: true
      expo-file-system:
        optional: true
      expo-gl:
        optional: true
      react-dom:
        optional: true
      react-native:
        optional: true

  '@react-types/shared@3.31.0':
    resolution: {integrity: sha512-ua5U6V66gDcbLZe4P2QeyNgPp4YWD1ymGA6j3n+s8CGExtrCPe64v+g4mvpT8Bnb985R96e4zFT61+m0YCwqMg==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1

  '@remix-run/router@1.23.0':
    resolution: {integrity: sha512-O3rHJzAQKamUz1fvE0Qaw0xSFqsA/yafi2iqeE0pvdFtCO1viYx8QL6f3Ln/aCCTLxs68SLf0KPM9eSeM8yBnA==}
    engines: {node: '>=14.0.0'}

  '@rolldown/pluginutils@1.0.0-beta.27':
    resolution: {integrity: sha512-+d0F4MKMCbeVUJwG96uQ4SgAznZNSq93I3V+9NHA4OpvqG8mRCpGdKmK8l/dl02h2CCDHwW2FqilnTyDcAnqjA==}

  '@rollup/pluginutils@5.2.0':
    resolution: {integrity: sha512-qWJ2ZTbmumwiLFomfzTyt5Kng4hwPi9rwCYN4SHb6eaRU1KNO4ccxINHr/VhH4GgPlt1XfSTLX2LBTme8ne4Zw==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  '@rollup/rollup-android-arm-eabi@4.45.1':
    resolution: {integrity: sha512-NEySIFvMY0ZQO+utJkgoMiCAjMrGvnbDLHvcmlA33UXJpYBCvlBEbMMtV837uCkS+plG2umfhn0T5mMAxGrlRA==}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.45.1':
    resolution: {integrity: sha512-ujQ+sMXJkg4LRJaYreaVx7Z/VMgBBd89wGS4qMrdtfUFZ+TSY5Rs9asgjitLwzeIbhwdEhyj29zhst3L1lKsRQ==}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.45.1':
    resolution: {integrity: sha512-FSncqHvqTm3lC6Y13xncsdOYfxGSLnP+73k815EfNmpewPs+EyM49haPS105Rh4aF5mJKywk9X0ogzLXZzN9lA==}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.45.1':
    resolution: {integrity: sha512-2/vVn/husP5XI7Fsf/RlhDaQJ7x9zjvC81anIVbr4b/f0xtSmXQTFcGIQ/B1cXIYM6h2nAhJkdMHTnD7OtQ9Og==}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.45.1':
    resolution: {integrity: sha512-4g1kaDxQItZsrkVTdYQ0bxu4ZIQ32cotoQbmsAnW1jAE4XCMbcBPDirX5fyUzdhVCKgPcrwWuucI8yrVRBw2+g==}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.45.1':
    resolution: {integrity: sha512-L/6JsfiL74i3uK1Ti2ZFSNsp5NMiM4/kbbGEcOCps99aZx3g8SJMO1/9Y0n/qKlWZfn6sScf98lEOUe2mBvW9A==}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.45.1':
    resolution: {integrity: sha512-RkdOTu2jK7brlu+ZwjMIZfdV2sSYHK2qR08FUWcIoqJC2eywHbXr0L8T/pONFwkGukQqERDheaGTeedG+rra6Q==}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.45.1':
    resolution: {integrity: sha512-3kJ8pgfBt6CIIr1o+HQA7OZ9mp/zDk3ctekGl9qn/pRBgrRgfwiffaUmqioUGN9hv0OHv2gxmvdKOkARCtRb8Q==}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.45.1':
    resolution: {integrity: sha512-k3dOKCfIVixWjG7OXTCOmDfJj3vbdhN0QYEqB+OuGArOChek22hn7Uy5A/gTDNAcCy5v2YcXRJ/Qcnm4/ma1xw==}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.45.1':
    resolution: {integrity: sha512-PmI1vxQetnM58ZmDFl9/Uk2lpBBby6B6rF4muJc65uZbxCs0EA7hhKCk2PKlmZKuyVSHAyIw3+/SiuMLxKxWog==}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.45.1':
    resolution: {integrity: sha512-9UmI0VzGmNJ28ibHW2GpE2nF0PBQqsyiS4kcJ5vK+wuwGnV5RlqdczVocDSUfGX/Na7/XINRVoUgJyFIgipoRg==}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-powerpc64le-gnu@4.45.1':
    resolution: {integrity: sha512-7nR2KY8oEOUTD3pBAxIBBbZr0U7U+R9HDTPNy+5nVVHDXI4ikYniH1oxQz9VoB5PbBU1CZuDGHkLJkd3zLMWsg==}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.45.1':
    resolution: {integrity: sha512-nlcl3jgUultKROfZijKjRQLUu9Ma0PeNv/VFHkZiKbXTBQXhpytS8CIj5/NfBeECZtY2FJQubm6ltIxm/ftxpw==}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-musl@4.45.1':
    resolution: {integrity: sha512-HJV65KLS51rW0VY6rvZkiieiBnurSzpzore1bMKAhunQiECPuxsROvyeaot/tcK3A3aGnI+qTHqisrpSgQrpgA==}
    cpu: [riscv64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-s390x-gnu@4.45.1':
    resolution: {integrity: sha512-NITBOCv3Qqc6hhwFt7jLV78VEO/il4YcBzoMGGNxznLgRQf43VQDae0aAzKiBeEPIxnDrACiMgbqjuihx08OOw==}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.45.1':
    resolution: {integrity: sha512-+E/lYl6qu1zqgPEnTrs4WysQtvc/Sh4fC2nByfFExqgYrqkKWp1tWIbe+ELhixnenSpBbLXNi6vbEEJ8M7fiHw==}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.45.1':
    resolution: {integrity: sha512-a6WIAp89p3kpNoYStITT9RbTbTnqarU7D8N8F2CV+4Cl9fwCOZraLVuVFvlpsW0SbIiYtEnhCZBPLoNdRkjQFw==}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.45.1':
    resolution: {integrity: sha512-T5Bi/NS3fQiJeYdGvRpTAP5P02kqSOpqiopwhj0uaXB6nzs5JVi2XMJb18JUSKhCOX8+UE1UKQufyD6Or48dJg==}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.45.1':
    resolution: {integrity: sha512-lxV2Pako3ujjuUe9jiU3/s7KSrDfH6IgTSQOnDWr9aJ92YsFd7EurmClK0ly/t8dzMkDtd04g60WX6yl0sGfdw==}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.45.1':
    resolution: {integrity: sha512-M/fKi4sasCdM8i0aWJjCSFm2qEnYRR8AMLG2kxp6wD13+tMGA4Z1tVAuHkNRjud5SW2EM3naLuK35w9twvf6aA==}
    cpu: [x64]
    os: [win32]

  '@svgr/babel-plugin-add-jsx-attribute@8.0.0':
    resolution: {integrity: sha512-b9MIk7yhdS1pMCZM8VeNfUlSKVRhsHZNMl5O9SfaX0l0t5wjdgu4IDzGB8bpnGBBOjGST3rRFVsaaEtI4W6f7g==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0':
    resolution: {integrity: sha512-BcCkm/STipKvbCl6b7QFrMh/vx00vIP63k2eM66MfHJzPr6O2U0jYEViXkHJWqXqQYjdeA9cuCl5KWmlwjDvbA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0':
    resolution: {integrity: sha512-5BcGCBfBxB5+XSDSWnhTThfI9jcO5f0Ai2V24gZpG+wXF14BzwxxdDb4g6trdOux0rhibGs385BeFMSmxtS3uA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0':
    resolution: {integrity: sha512-KVQ+PtIjb1BuYT3ht8M5KbzWBhdAjjUPdlMtpuw/VjT8coTrItWX6Qafl9+ji831JaJcu6PJNKCV0bp01lBNzQ==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-svg-dynamic-title@8.0.0':
    resolution: {integrity: sha512-omNiKqwjNmOQJ2v6ge4SErBbkooV2aAWwaPFs2vUY7p7GhVkzRkJ00kILXQvRhA6miHnNpXv7MRnnSjdRjK8og==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-svg-em-dimensions@8.0.0':
    resolution: {integrity: sha512-mURHYnu6Iw3UBTbhGwE/vsngtCIbHE43xCRK7kCw4t01xyGqb2Pd+WXekRRoFOBIY29ZoOhUCTEweDMdrjfi9g==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-transform-react-native-svg@8.1.0':
    resolution: {integrity: sha512-Tx8T58CHo+7nwJ+EhUwx3LfdNSG9R2OKfaIXXs5soiy5HtgoAEkDay9LIimLOcG8dJQH1wPZp/cnAv6S9CrR1Q==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-plugin-transform-svg-component@8.0.0':
    resolution: {integrity: sha512-DFx8xa3cZXTdb/k3kfPeaixecQLgKh5NVBMwD0AQxOzcZawK4oo1Jh9LbrcACUivsCA7TLG8eeWgrDXjTMhRmw==}
    engines: {node: '>=12'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/babel-preset@8.1.0':
    resolution: {integrity: sha512-7EYDbHE7MxHpv4sxvnVPngw5fuR6pw79SkcrILHJ/iMpuKySNCl5W1qcwPEpU+LgyRXOaAFgH0KhwD18wwg6ug==}
    engines: {node: '>=14'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@svgr/core@8.1.0':
    resolution: {integrity: sha512-8QqtOQT5ACVlmsvKOJNEaWmRPmcojMOzCz4Hs2BGG/toAp/K38LcsMRyLp349glq5AzJbCEeimEoxaX6v/fLrA==}
    engines: {node: '>=14'}

  '@svgr/hast-util-to-babel-ast@8.0.0':
    resolution: {integrity: sha512-EbDKwO9GpfWP4jN9sGdYwPBU0kdomaPIL2Eu4YwmgP+sJeXT+L7bMwJUBnhzfH8Q2qMBqZ4fJwpCyYsAN3mt2Q==}
    engines: {node: '>=14'}

  '@svgr/plugin-jsx@8.1.0':
    resolution: {integrity: sha512-0xiIyBsLlr8quN+WyuxooNW9RJ0Dpr8uOnH/xrCVO8GLUcwHISwj1AG0k+LFzteTkAA0GbX0kj9q6Dk70PTiPA==}
    engines: {node: '>=14'}
    peerDependencies:
      '@svgr/core': '*'

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@tailwindcss/node@4.1.11':
    resolution: {integrity: sha512-yzhzuGRmv5QyU9qLNg4GTlYI6STedBWRE7NjxP45CsFYYq9taI0zJXZBMqIC/c8fViNLhmrbpSFS57EoxUmD6Q==}

  '@tailwindcss/oxide-android-arm64@4.1.11':
    resolution: {integrity: sha512-3IfFuATVRUMZZprEIx9OGDjG3Ou3jG4xQzNTvjDoKmU9JdmoCohQJ83MYd0GPnQIu89YoJqvMM0G3uqLRFtetg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    resolution: {integrity: sha512-ESgStEOEsyg8J5YcMb1xl8WFOXfeBmrhAwGsFxxB2CxY9evy63+AtpbDLAyRkJnxLy2WsD1qF13E97uQyP1lfQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.11':
    resolution: {integrity: sha512-EgnK8kRchgmgzG6jE10UQNaH9Mwi2n+yw1jWmof9Vyg2lpKNX2ioe7CJdf9M5f8V9uaQxInenZkOxnTVL3fhAw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    resolution: {integrity: sha512-xdqKtbpHs7pQhIKmqVpxStnY1skuNh4CtbcyOHeX1YBE0hArj2romsFGb6yUmzkq/6M24nkxDqU8GYrKrz+UcA==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    resolution: {integrity: sha512-ryHQK2eyDYYMwB5wZL46uoxz2zzDZsFBwfjssgB7pzytAeCCa6glsiJGjhTEddq/4OsIjsLNMAiMlHNYnkEEeg==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    resolution: {integrity: sha512-mYwqheq4BXF83j/w75ewkPJmPZIqqP1nhoghS9D57CLjsh3Nfq0m4ftTotRYtGnZd3eCztgbSPJ9QhfC91gDZQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    resolution: {integrity: sha512-m/NVRFNGlEHJrNVk3O6I9ggVuNjXHIPoD6bqay/pubtYC9QIdAMpS+cswZQPBLvVvEF6GtSNONbDkZrjWZXYNQ==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    resolution: {integrity: sha512-YW6sblI7xukSD2TdbbaeQVDysIm/UPJtObHJHKxDEcW2exAtY47j52f8jZXkqE1krdnkhCMGqP3dbniu1Te2Fg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    resolution: {integrity: sha512-e3C/RRhGunWYNC3aSF7exsQkdXzQ/M+aYuZHKnw4U7KQwTJotnWsGOIVih0s2qQzmEzOFIJ3+xt7iq67K/p56Q==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    resolution: {integrity: sha512-Xo1+/GU0JEN/C/dvcammKHzeM6NqKovG+6921MR6oadee5XPBaKOumrJCXvopJ/Qb5TH7LX/UAywbqrP4lax0g==}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]
    bundledDependencies:
      - '@napi-rs/wasm-runtime'
      - '@emnapi/core'
      - '@emnapi/runtime'
      - '@tybys/wasm-util'
      - '@emnapi/wasi-threads'
      - tslib

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    resolution: {integrity: sha512-UgKYx5PwEKrac3GPNPf6HVMNhUIGuUh4wlDFR2jYYdkX6pL/rn73zTq/4pzUm8fOjAn5L8zDeHp9iXmUGOXZ+w==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    resolution: {integrity: sha512-YfHoggn1j0LK7wR82TOucWc5LDCguHnoS879idHekmmiR7g9HUtMw9MI0NHatS28u/Xlkfi9w5RJWgz2Dl+5Qg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.1.11':
    resolution: {integrity: sha512-Q69XzrtAhuyfHo+5/HMgr1lAiPP/G40OMFAnws7xcFEYqcypZmdW8eGXaOUIeOl1dzPJBPENXgbjsOyhg2nkrg==}
    engines: {node: '>= 10'}

  '@tailwindcss/vite@4.1.11':
    resolution: {integrity: sha512-RHYhrR3hku0MJFRV+fN2gNbDNEh3dwKvY8XJvTxCSXeMOsCRSr+uKvDWQcbizrHgjML6ZmTE5OwMrl5wKcujCw==}
    peerDependencies:
      vite: ^5.2.0 || ^6 || ^7

  '@tanstack/query-core@5.83.0':
    resolution: {integrity: sha512-0M8dA+amXUkyz5cVUm/B+zSk3xkQAcuXuz5/Q/LveT4ots2rBpPTZOzd7yJa2Utsf8D2Upl5KyjhHRY+9lB/XA==}

  '@tanstack/react-query@5.83.0':
    resolution: {integrity: sha512-/XGYhZ3foc5H0VM2jLSD/NyBRIOK4q9kfeml4+0x2DlL6xVuAcVEW+hTlTapAmejObg0i3eNqhkr2dT+eciwoQ==}
    peerDependencies:
      react: ^18 || ^19

  '@tanstack/react-virtual@3.13.12':
    resolution: {integrity: sha512-Gd13QdxPSukP8ZrkbgS2RwoZseTTbQPLnQEn7HY/rqtM+8Zt95f7xKC7N0EsKs7aoz0WzZ+fditZux+F8EzYxA==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  '@tanstack/virtual-core@3.13.12':
    resolution: {integrity: sha512-1YBOJfRHV4sXUmWsFSf5rQor4Ss82G8dQWLRbnk3GA4jeP8hQt1hxXh0tmflpC0dz3VgEv/1+qwPyLeWkQuPFA==}

  '@turf/along@7.2.0':
    resolution: {integrity: sha512-Cf+d2LozABdb0TJoIcJwFKB+qisJY4nMUW9z6PAuZ9UCH7AR//hy2Z06vwYCKFZKP4a7DRPkOMBadQABCyoYuw==}

  '@turf/angle@7.2.0':
    resolution: {integrity: sha512-b28rs1NO8Dt/MXadFhnpqH7GnEWRsl+xF5JeFtg9+eM/+l/zGrdliPYMZtAj12xn33w22J1X4TRprAI0rruvVQ==}

  '@turf/area@7.2.0':
    resolution: {integrity: sha512-zuTTdQ4eoTI9nSSjerIy4QwgvxqwJVciQJ8tOPuMHbXJ9N/dNjI7bU8tasjhxas/Cx3NE9NxVHtNpYHL0FSzoA==}

  '@turf/bbox-clip@7.2.0':
    resolution: {integrity: sha512-q6RXTpqeUQAYLAieUL1n3J6ukRGsNVDOqcYtfzaJbPW+0VsAf+1cI16sN700t0sekbeU1DH/RRVAHhpf8+36wA==}

  '@turf/bbox-polygon@7.2.0':
    resolution: {integrity: sha512-Aj4G1GAAy26fmOqMjUk0Z+Lcax5VQ9g1xYDbHLQWXvfTsaueBT+RzdH6XPnZ/seEEnZkio2IxE8V5af/osupgA==}

  '@turf/bbox@7.2.0':
    resolution: {integrity: sha512-wzHEjCXlYZiDludDbXkpBSmv8Zu6tPGLmJ1sXQ6qDwpLE1Ew3mcWqt8AaxfTP5QwDNQa3sf2vvgTEzNbPQkCiA==}

  '@turf/bearing@7.2.0':
    resolution: {integrity: sha512-Jm0Xt3GgHjRrWvBtAGvgfnADLm+4exud2pRlmCYx8zfiKuNXQFkrcTZcOiJOgTfG20Agq28iSh15uta47jSIbg==}

  '@turf/bezier-spline@7.2.0':
    resolution: {integrity: sha512-7BPkc3ufYB9KLvcaTpTsnpXzh9DZoENxCS0Ms9XUwuRXw45TpevwUpOsa3atO76iKQ5puHntqFO4zs8IUxBaaA==}

  '@turf/boolean-clockwise@5.1.5':
    resolution: {integrity: sha512-FqbmEEOJ4rU4/2t7FKx0HUWmjFEVqR+NJrFP7ymGSjja2SQ7Q91nnBihGuT+yuHHl6ElMjQ3ttsB/eTmyCycxA==}

  '@turf/boolean-clockwise@7.2.0':
    resolution: {integrity: sha512-0fJeFSARxy6ealGBM4Gmgpa1o8msQF87p2Dx5V6uSqzT8VPDegX1NSWl4b7QgXczYa9qv7IAABttdWP0K7Q7eQ==}

  '@turf/boolean-concave@7.2.0':
    resolution: {integrity: sha512-v3dTN04dfO6VqctQj1a+pjDHb6+/Ev90oAR2QjJuAntY4ubhhr7vKeJdk/w+tWNSMKULnYwfe65Du3EOu3/TeA==}

  '@turf/boolean-contains@7.2.0':
    resolution: {integrity: sha512-dgRQm4uVO5XuLee4PLVH7CFQZKdefUBMIXTPITm2oRIDmPLJKHDOFKQTNkGJ73mDKKBR2lmt6eVH3br6OYrEYg==}

  '@turf/boolean-crosses@7.2.0':
    resolution: {integrity: sha512-9GyM4UUWFKQOoNhHVSfJBf5XbPy8Fxfz9djjJNAnm/IOl8NmFUSwFPAjKlpiMcr6yuaAoc9R/1KokS9/eLqPvA==}

  '@turf/boolean-disjoint@7.2.0':
    resolution: {integrity: sha512-xdz+pYKkLMuqkNeJ6EF/3OdAiJdiHhcHCV0ykX33NIuALKIEpKik0+NdxxNsZsivOW6keKwr61SI+gcVtHYcnQ==}

  '@turf/boolean-equal@7.2.0':
    resolution: {integrity: sha512-TmjKYLsxXqEmdDtFq3QgX4aSogiISp3/doeEtDOs3NNSR8susOtBEZkmvwO6DLW+g/rgoQJIBR6iVoWiRqkBxw==}

  '@turf/boolean-intersects@7.2.0':
    resolution: {integrity: sha512-GLRyLQgK3F14drkK5Qi9Mv7Z9VT1bgQUd9a3DB3DACTZWDSwfh8YZUFn/HBwRkK8dDdgNEXaavggQHcPi1k9ow==}

  '@turf/boolean-overlap@7.2.0':
    resolution: {integrity: sha512-ieM5qIE4anO+gUHIOvEN7CjyowF+kQ6v20/oNYJCp63TVS6eGMkwgd+I4uMzBXfVW66nVHIXjODdUelU+Xyctw==}

  '@turf/boolean-parallel@7.2.0':
    resolution: {integrity: sha512-iOtuzzff8nmwv05ROkSvyeGLMrfdGkIi+3hyQ+DH4IVyV37vQbqR5oOJ0Nt3Qq1Tjrq9fvF8G3OMdAv3W2kY9w==}

  '@turf/boolean-point-in-polygon@7.2.0':
    resolution: {integrity: sha512-lvEOjxeXIp+wPXgl9kJA97dqzMfNexjqHou+XHVcfxQgolctoJiRYmcVCWGpiZ9CBf/CJha1KmD1qQoRIsjLaA==}

  '@turf/boolean-point-on-line@7.2.0':
    resolution: {integrity: sha512-H/bXX8+2VYeSyH8JWrOsu8OGmeA9KVZfM7M6U5/fSqGsRHXo9MyYJ94k39A9kcKSwI0aWiMXVD2UFmiWy8423Q==}

  '@turf/boolean-touches@7.2.0':
    resolution: {integrity: sha512-8qb1CO+cwFATGRGFgTRjzL9aibfsbI91pdiRl7KIEkVdeN/H9k8FDrUA1neY7Yq48IaciuwqjbbojQ16FD9b0w==}

  '@turf/boolean-valid@7.2.0':
    resolution: {integrity: sha512-xb7gdHN8VV6ivPJh6rPpgxmAEGReiRxqY+QZoEZVGpW2dXcmU1BdY6FA6G/cwvggXAXxJBREoANtEDgp/0ySbA==}

  '@turf/boolean-within@7.2.0':
    resolution: {integrity: sha512-zB3AiF59zQZ27Dp1iyhp9mVAKOFHat8RDH45TZhLY8EaqdEPdmLGvwMFCKfLryQcUDQvmzP8xWbtUR82QM5C4g==}

  '@turf/buffer@7.2.0':
    resolution: {integrity: sha512-QH1FTr5Mk4z1kpQNztMD8XBOZfpOXPOtlsxaSAj2kDIf5+LquA6HtJjZrjUngnGtzG5+XwcfyRL4ImvLnFjm5Q==}

  '@turf/center-mean@7.2.0':
    resolution: {integrity: sha512-NaW6IowAooTJ35O198Jw3U4diZ6UZCCeJY+4E+WMLpks3FCxMDSHEfO2QjyOXQMGWZnVxVelqI5x9DdniDbQ+A==}

  '@turf/center-median@7.2.0':
    resolution: {integrity: sha512-/CgVyHNG4zAoZpvkl7qBCe4w7giWNVtLyTU5PoIfg1vWM4VpYw+N7kcBBH46bbzvVBn0vhmZr586r543EwdC/A==}

  '@turf/center-of-mass@7.2.0':
    resolution: {integrity: sha512-ij3pmG61WQPHGTQvOziPOdIgwTMegkYTwIc71Gl7xn4C0vWH6KLDSshCphds9xdWSXt2GbHpUs3tr4XGntHkEQ==}

  '@turf/center@7.2.0':
    resolution: {integrity: sha512-UTNp9abQ2kuyRg5gCIGDNwwEQeF3NbpYsd1Q0KW9lwWuzbLVNn0sOwbxjpNF4J2HtMOs5YVOcqNvYyuoa2XrXw==}

  '@turf/centroid@7.2.0':
    resolution: {integrity: sha512-yJqDSw25T7P48au5KjvYqbDVZ7qVnipziVfZ9aSo7P2/jTE7d4BP21w0/XLi3T/9bry/t9PR1GDDDQljN4KfDw==}

  '@turf/circle@7.2.0':
    resolution: {integrity: sha512-1AbqBYtXhstrHmnW6jhLwsv7TtmT0mW58Hvl1uZXEDM1NCVXIR50yDipIeQPjrCuJ/Zdg/91gU8+4GuDCAxBGA==}

  '@turf/clean-coords@7.2.0':
    resolution: {integrity: sha512-+5+J1+D7wW7O/RDXn46IfCHuX1gIV1pIAQNSA7lcDbr3HQITZj334C4mOGZLEcGbsiXtlHWZiBtm785Vg8i+QQ==}

  '@turf/clone@5.1.5':
    resolution: {integrity: sha512-//pITsQ8xUdcQ9pVb4JqXiSqG4dos5Q9N4sYFoWghX21tfOV2dhc5TGqYOhnHrQS7RiKQL1vQ48kIK34gQ5oRg==}

  '@turf/clone@7.2.0':
    resolution: {integrity: sha512-JlGUT+/5qoU5jqZmf6NMFIoLDY3O7jKd53Up+zbpJ2vzUp6QdwdNzwrsCeONhynWM13F0MVtPXH4AtdkrgFk4g==}

  '@turf/clusters-dbscan@7.2.0':
    resolution: {integrity: sha512-VWVUuDreev56g3/BMlnq/81yzczqaz+NVTypN5CigGgP67e+u/CnijphiuhKjtjDd/MzGjXgEWBJc26Y6LYKAw==}

  '@turf/clusters-kmeans@7.2.0':
    resolution: {integrity: sha512-BxQdK8jc8Mwm9yoClCYkktm4W004uiQGqb/i/6Y7a8xqgJITWDgTu/cy//wOxAWPk4xfe6MThjnqkszWW8JdyQ==}

  '@turf/clusters@7.2.0':
    resolution: {integrity: sha512-sKOrIKHHtXAuTKNm2USnEct+6/MrgyzMW42deZ2YG2RRKWGaaxHMFU2Yw71Yk4DqStOqTIBQpIOdrRuSOwbuQw==}

  '@turf/collect@7.2.0':
    resolution: {integrity: sha512-zRVGDlYS8Bx/Zz4vnEUyRg4dmqHhkDbW/nIUIJh657YqaMj1SFi4Iv2i9NbcurlUBDJFkpuOhCvvEvAdskJ8UA==}

  '@turf/combine@7.2.0':
    resolution: {integrity: sha512-VEjm3IvnbMt3IgeRIhCDhhQDbLqCU1/5uN1+j1u6fyA095pCizPThGp4f/COSzC3t1s/iiV+fHuDsB6DihHffQ==}

  '@turf/concave@7.2.0':
    resolution: {integrity: sha512-cpaDDlumK762kdadexw5ZAB6g/h2pJdihZ+e65lbQVe3WukJHAANnIEeKsdFCuIyNKrwTz2gWu5ws+OpjP48Yw==}

  '@turf/convex@7.2.0':
    resolution: {integrity: sha512-HsgHm+zHRE8yPCE/jBUtWFyaaBmpXcSlyHd5/xsMhSZRImFzRzBibaONWQo7xbKZMISC3Nc6BtUjDi/jEVbqyA==}

  '@turf/destination@7.2.0':
    resolution: {integrity: sha512-8DUxtOO0Fvrh1xclIUj3d9C5WS20D21F5E+j+X9Q+ju6fcM4huOqTg5ckV1DN2Pg8caABEc5HEZJnGch/5YnYQ==}

  '@turf/difference@7.2.0':
    resolution: {integrity: sha512-NHKD1v3s8RX+9lOpvHJg6xRuJOKiY3qxHhz5/FmE0VgGqnCkE7OObqWZ5SsXG+Ckh0aafs5qKhmDdDV/gGi6JA==}

  '@turf/dissolve@7.2.0':
    resolution: {integrity: sha512-gPG5TE3mAYuZqBut8tPYCKwi4hhx5Cq0ALoQMB9X0hrVtFIKrihrsj98XQM/5pL/UIpAxQfwisQvy6XaOFaoPA==}

  '@turf/distance-weight@7.2.0':
    resolution: {integrity: sha512-NeoyV0fXDH+7nIoNtLjAoH9XL0AS1pmTIyDxEE6LryoDTsqjnuR0YQxIkLCCWDqECoqaOmmBqpeWONjX5BwWCg==}

  '@turf/distance@7.2.0':
    resolution: {integrity: sha512-HBjjXIgEcD/wJYjv7/6OZj5yoky2oUvTtVeIAqO3lL80XRvoYmVg6vkOIu6NswkerwLDDNT9kl7+BFLJoHbh6Q==}

  '@turf/ellipse@7.2.0':
    resolution: {integrity: sha512-/Y75S5hE2+xjnTw4dXpQ5r/Y2HPM4xrwkPRCCQRpuuboKdEvm42azYmh7isPnMnBTVcmGb9UmGKj0HHAbiwt1g==}

  '@turf/envelope@7.2.0':
    resolution: {integrity: sha512-xOMtDeNKHwUuDfzQeoSNmdabsP0/IgVDeyzitDe/8j9wTeW+MrKzVbGz7627PT3h6gsO+2nUv5asfKtUbmTyHA==}

  '@turf/explode@7.2.0':
    resolution: {integrity: sha512-jyMXg93J1OI7/65SsLE1k9dfQD3JbcPNMi4/O3QR2Qb3BAs2039oFaSjtW+YqhMqVC4V3ZeKebMcJ8h9sK1n+A==}

  '@turf/flatten@7.2.0':
    resolution: {integrity: sha512-q38Qsqr4l7mxp780zSdn0gp/WLBX+sa+gV6qIbDQ1HKCrrPK8QQJmNx7gk1xxEXVot6tq/WyAPysCQdX+kLmMA==}

  '@turf/flip@7.2.0':
    resolution: {integrity: sha512-X0TQ0U/UYh4tyXdLO5itP1sO2HOvfrZC0fYSWmTfLDM14jEPkEK8PblofznfBygL+pIFtOS2is8FuVcp5XxYpQ==}

  '@turf/geojson-rbush@7.2.0':
    resolution: {integrity: sha512-ST8fLv+EwxVkDgsmhHggM0sPk2SfOHTZJkdgMXVFT7gB9o4lF8qk4y4lwvCCGIfFQAp2yv/PN5EaGMEKutk6xw==}

  '@turf/great-circle@7.2.0':
    resolution: {integrity: sha512-n30OiADyOKHhor0aXNgYfXQYXO3UtsOKmhQsY1D89/Oh1nCIXG/1ZPlLL9ZoaRXXBTUBjh99a+K8029NQbGDhw==}

  '@turf/helpers@5.1.5':
    resolution: {integrity: sha512-/lF+JR+qNDHZ8bF9d+Cp58nxtZWJ3sqFe6n3u3Vpj+/0cqkjk4nXKYBSY0azm+GIYB5mWKxUXvuP/m0ZnKj1bw==}

  '@turf/helpers@7.2.0':
    resolution: {integrity: sha512-cXo7bKNZoa7aC7ydLmUR02oB3IgDe7MxiPuRz3cCtYQHn+BJ6h1tihmamYDWWUlPHgSNF0i3ATc4WmDECZafKw==}

  '@turf/hex-grid@7.2.0':
    resolution: {integrity: sha512-Yo2yUGxrTCQfmcVsSjDt0G3Veg8YD26WRd7etVPD9eirNNgXrIyZkbYA7zVV/qLeRWVmYIKRXg1USWl7ORQOGA==}

  '@turf/interpolate@7.2.0':
    resolution: {integrity: sha512-Ifgjm1SEo6XujuSAU6lpRMvoJ1SYTreil1Rf5WsaXj16BQJCedht/4FtWCTNhSWTwEz2motQ1WNrjTCuPG94xA==}

  '@turf/intersect@7.2.0':
    resolution: {integrity: sha512-81GMzKS9pKqLPa61qSlFxLFeAC8XbwyCQ9Qv4z6o5skWk1qmMUbEHeMqaGUTEzk+q2XyhZ0sju1FV4iLevQ/aw==}

  '@turf/invariant@5.2.0':
    resolution: {integrity: sha512-28RCBGvCYsajVkw2EydpzLdcYyhSA77LovuOvgCJplJWaNVyJYH6BOR3HR9w50MEkPqb/Vc/jdo6I6ermlRtQA==}

  '@turf/invariant@7.2.0':
    resolution: {integrity: sha512-kV4u8e7Gkpq+kPbAKNC21CmyrXzlbBgFjO1PhrHPgEdNqXqDawoZ3i6ivE3ULJj2rSesCjduUaC/wyvH/sNr2Q==}

  '@turf/isobands@7.2.0':
    resolution: {integrity: sha512-lYoHeRieFzpBp29Jh19QcDIb0E+dzo/K5uwZuNga4wxr6heNU0AfkD4ByAHYIXHtvmp4m/JpSKq/2N6h/zvBkg==}

  '@turf/isolines@7.2.0':
    resolution: {integrity: sha512-4ZXKxvA/JKkxAXixXhN3UVza5FABsdYgOWXyYm3L5ryTPJVOYTVSSd9A+CAVlv9dZc3YdlsqMqLTXNOOre/kwg==}

  '@turf/jsts@2.7.2':
    resolution: {integrity: sha512-zAezGlwWHPyU0zxwcX2wQY3RkRpwuoBmhhNE9HY9kWhFDkCxZ3aWK5URKwa/SWKJbj9aztO+8vtdiBA28KVJFg==}

  '@turf/kinks@7.2.0':
    resolution: {integrity: sha512-BtxDxGewJR0Q5WR9HKBSxZhirFX+GEH1rD7/EvgDsHS8e1Y5/vNQQUmXdURjdPa4StzaUBsWRU5T3A356gLbPA==}

  '@turf/length@7.2.0':
    resolution: {integrity: sha512-LBmYN+iCgVtWNLsckVnpQIJENqIIPO63mogazMp23lrDGfWXu07zZQ9ZinJVO5xYurXNhc/QI2xxoqt2Xw90Ig==}

  '@turf/line-arc@7.2.0':
    resolution: {integrity: sha512-kfWzA5oYrTpslTg5fN50G04zSypiYQzjZv3FLjbZkk6kta5fo4JkERKjTeA8x4XNojb+pfmjMBB0yIh2w2dDRw==}

  '@turf/line-chunk@7.2.0':
    resolution: {integrity: sha512-1ODyL5gETtWSL85MPI0lgp/78vl95M39gpeBxePXyDIqx8geDP9kXfAzctuKdxBoR4JmOVM3NT7Fz7h+IEkC+g==}

  '@turf/line-intersect@7.2.0':
    resolution: {integrity: sha512-GhCJVEkc8EmggNi85EuVLoXF5T5jNVxmhIetwppiVyJzMrwkYAkZSYB3IBFYGUUB9qiNFnTwungVSsBV/S8ZiA==}

  '@turf/line-offset@7.2.0':
    resolution: {integrity: sha512-1+OkYueDCbnEWzbfBh3taVr+3SyM2bal5jfnSEuDiLA6jnlScgr8tn3INo+zwrUkPFZPPAejL1swVyO5TjUahw==}

  '@turf/line-overlap@7.2.0':
    resolution: {integrity: sha512-NNn7/jg53+N10q2Kyt66bEDqN3101iW/1zA5FW7J6UbKApDFkByh+18YZq1of71kS6oUYplP86WkDp16LFpqqw==}

  '@turf/line-segment@7.2.0':
    resolution: {integrity: sha512-E162rmTF9XjVN4rINJCd15AdQGCBlNqeWN3V0YI1vOUpZFNT2ii4SqEMCcH2d+5EheHLL8BWVwZoOsvHZbvaWA==}

  '@turf/line-slice-along@7.2.0':
    resolution: {integrity: sha512-4/gPgP0j5Rp+1prbhXqn7kIH/uZTmSgiubUnn67F8nb9zE+MhbRglhSlRYEZxAVkB7VrGwjyolCwvrROhjHp2A==}

  '@turf/line-slice@7.2.0':
    resolution: {integrity: sha512-bHotzZIaU1GPV3RMwttYpDrmcvb3X2i1g/WUttPZWtKrEo2VVAkoYdeZ2aFwtogERYS4quFdJ/TDzAtquBC8WQ==}

  '@turf/line-split@7.2.0':
    resolution: {integrity: sha512-yJTZR+c8CwoKqdW/aIs+iLbuFwAa3Yan+EOADFQuXXIUGps3bJUXx/38rmowNoZbHyP1np1+OtrotyHu5uBsfQ==}

  '@turf/line-to-polygon@7.2.0':
    resolution: {integrity: sha512-iKpJqc7EYc5NvlD4KaqrKKO6mXR7YWO/YwtW60E2FnsF/blnsy9OfAOcilYHgH3S/V/TT0VedC7DW7Kgjy2EIA==}

  '@turf/mask@7.2.0':
    resolution: {integrity: sha512-ulJ6dQqXC0wrjIoqFViXuMUdIPX5Q6GPViZ3kGfeVijvlLM7kTFBsZiPQwALSr5nTQg4Ppf3FD0Jmg8IErPrgA==}

  '@turf/meta@5.2.0':
    resolution: {integrity: sha512-ZjQ3Ii62X9FjnK4hhdsbT+64AYRpaI8XMBMcyftEOGSmPMUVnkbvuv3C9geuElAXfQU7Zk1oWGOcrGOD9zr78Q==}

  '@turf/meta@7.2.0':
    resolution: {integrity: sha512-igzTdHsQc8TV1RhPuOLVo74Px/hyPrVgVOTgjWQZzt3J9BVseCdpfY/0cJBdlSRI4S/yTmmHl7gAqjhpYH5Yaw==}

  '@turf/midpoint@7.2.0':
    resolution: {integrity: sha512-AMn5S9aSrbXdE+Q4Rj+T5nLdpfpn+mfzqIaEKkYI021HC0vb22HyhQHsQbSeX+AWcS4CjD1hFsYVcgKI+5qCfw==}

  '@turf/moran-index@7.2.0':
    resolution: {integrity: sha512-Aexh1EmXVPJhApr9grrd120vbalIthcIsQ3OAN2Tqwf+eExHXArJEJqGBo9IZiQbIpFJeftt/OvUvlI8BeO1bA==}

  '@turf/nearest-neighbor-analysis@7.2.0':
    resolution: {integrity: sha512-LmP/crXb7gilgsL0wL9hsygqc537W/a1W5r9XBKJT4SKdqjoXX5APJatJfd3nwXbRIqwDH0cDA9/YyFjBPlKnA==}

  '@turf/nearest-point-on-line@7.2.0':
    resolution: {integrity: sha512-UOhAeoDPVewBQV+PWg1YTMQcYpJsIqfW5+EuZ5vJl60XwUa0+kqB/eVfSLNXmHENjKKIlEt9Oy9HIDF4VeWmXA==}

  '@turf/nearest-point-to-line@7.2.0':
    resolution: {integrity: sha512-EorU7Qj30A7nAjh++KF/eTPDlzwuuV4neBz7tmSTB21HKuXZAR0upJsx6M2X1CSyGEgNsbFB0ivNKIvymRTKBw==}

  '@turf/nearest-point@7.2.0':
    resolution: {integrity: sha512-0wmsqXZ8CGw4QKeZmS+NdjYTqCMC+HXZvM3XAQIU6k6laNLqjad2oS4nDrtcRs/nWDvcj1CR+Io7OiQ6sbpn5Q==}

  '@turf/planepoint@7.2.0':
    resolution: {integrity: sha512-8Vno01tvi5gThUEKBQ46CmlEKDAwVpkl7stOPFvJYlA1oywjAL4PsmgwjXgleZuFtXQUPBNgv5a42Pf438XP4g==}

  '@turf/point-grid@7.2.0':
    resolution: {integrity: sha512-ai7lwBV2FREPW3XiUNohT4opC1hd6+F56qZe20xYhCTkTD9diWjXHiNudQPSmVAUjgMzQGasblQQqvOdL+bJ3Q==}

  '@turf/point-on-feature@7.2.0':
    resolution: {integrity: sha512-ksoYoLO9WtJ/qI8VI9ltF+2ZjLWrAjZNsCsu8F7nyGeCh4I8opjf4qVLytFG44XA2qI5yc6iXDpyv0sshvP82Q==}

  '@turf/point-to-line-distance@7.2.0':
    resolution: {integrity: sha512-fB9Rdnb5w5+t76Gho2dYDkGe20eRrFk8CXi4v1+l1PC8YyLXO+x+l3TrtT8HzL/dVaZeepO6WUIsIw3ditTOPg==}

  '@turf/point-to-polygon-distance@7.2.0':
    resolution: {integrity: sha512-w+WYuINgTiFjoZemQwOaQSje/8Kq+uqJOynvx7+gleQPHyWQ3VtTodtV4LwzVzXz8Sf7Mngx1Jcp2SNai5CJYA==}

  '@turf/points-within-polygon@7.2.0':
    resolution: {integrity: sha512-jRKp8/mWNMzA+hKlQhxci97H5nOio9tp14R2SzpvkOt+cswxl+NqTEi1hDd2XetA7tjU0TSoNjEgVY8FfA0S6w==}

  '@turf/polygon-smooth@7.2.0':
    resolution: {integrity: sha512-KCp9wF2IEynvGXVhySR8oQ2razKP0zwg99K+fuClP21pSKCFjAPaihPEYq6e8uI/1J7ibjL5++6EMl+LrUTrLg==}

  '@turf/polygon-tangents@7.2.0':
    resolution: {integrity: sha512-AHUUPmOjiQDrtP/ODXukHBlUG0C/9I1je7zz50OTfl2ZDOdEqFJQC3RyNELwq07grTXZvg5TS5wYx/Y7nsm47g==}

  '@turf/polygon-to-line@7.2.0':
    resolution: {integrity: sha512-9jeTN3LiJ933I5sd4K0kwkcivOYXXm1emk0dHorwXeSFSHF+nlYesEW3Hd889wb9lZd7/SVLMUeX/h39mX+vCA==}

  '@turf/polygonize@7.2.0':
    resolution: {integrity: sha512-U9v+lBhUPDv+nsg/VcScdiqCB59afO6CHDGrwIl2+5i6Ve+/KQKjpTV/R+NqoC1iMXAEq3brY6HY8Ukp/pUWng==}

  '@turf/projection@7.2.0':
    resolution: {integrity: sha512-/qke5vJScv8Mu7a+fU3RSChBRijE6EVuFHU3RYihMuYm04Vw8dBMIs0enEpoq0ke/IjSbleIrGQNZIMRX9EwZQ==}

  '@turf/quadrat-analysis@7.2.0':
    resolution: {integrity: sha512-fDQh3+ldYNxUqS6QYlvJ7GZLlCeDZR6tD3ikdYtOsSemwW1n/4gm2xcgWJqy3Y0uszBwxc13IGGY7NGEjHA+0w==}

  '@turf/random@7.2.0':
    resolution: {integrity: sha512-fNXs5mOeXsrirliw84S8UCNkpm4RMNbefPNsuCTfZEXhcr1MuHMzq4JWKb4FweMdN1Yx2l/xcytkO0s71cJ50w==}

  '@turf/rectangle-grid@7.2.0':
    resolution: {integrity: sha512-f0o5ifvy0Ml/nHDJzMNcuSk4h11aa3BfvQNnYQhLpuTQu03j/ICZNlzKTLxwjcUqvxADUifty7Z9CX5W6zky4A==}

  '@turf/rewind@5.1.5':
    resolution: {integrity: sha512-Gdem7JXNu+G4hMllQHXRFRihJl3+pNl7qY+l4qhQFxq+hiU1cQoVFnyoleIqWKIrdK/i2YubaSwc3SCM7N5mMw==}

  '@turf/rewind@7.2.0':
    resolution: {integrity: sha512-SZpRAZiZsE22+HVz6pEID+ST25vOdpAMGk5NO1JeqzhpMALIkIGnkG+xnun2CfYHz7wv8/Z0ADiAvei9rkcQYA==}

  '@turf/rhumb-bearing@7.2.0':
    resolution: {integrity: sha512-jbdexlrR8X2ZauUciHx3tRwG+BXoMXke4B8p8/IgDlAfIrVdzAxSQN89FMzIKnjJ/kdLjo9bFGvb92bu31Etug==}

  '@turf/rhumb-destination@7.2.0':
    resolution: {integrity: sha512-U9OLgLAHlH4Wfx3fBZf3jvnkDjdTcfRan5eI7VPV1+fQWkOteATpzkiRjCvSYK575GljVwWBjkKca8LziGWitQ==}

  '@turf/rhumb-distance@7.2.0':
    resolution: {integrity: sha512-NsijTPON1yOc9tirRPEQQuJ5aQi7pREsqchQquaYKbHNWsexZjcDi4wnw2kM3Si4XjmgynT+2f7aXH7FHarHzw==}

  '@turf/sample@7.2.0':
    resolution: {integrity: sha512-f+ZbcbQJ9glQ/F26re8LadxO0ORafy298EJZe6XtbctRTJrNus6UNAsl8+GYXFqMnXM22tbTAznnJX3ZiWNorA==}

  '@turf/sector@7.2.0':
    resolution: {integrity: sha512-zL06MjbbMG4DdpiNz+Q9Ax8jsCekt3R76uxeWShulAGkyDB5smdBOUDoRwxn05UX7l4kKv4Ucq2imQXhxKFd1w==}

  '@turf/shortest-path@7.2.0':
    resolution: {integrity: sha512-6fpx8feZ2jMSaeRaFdqFShGWkNb+veUOeyLFSHA/aRD9n/e9F2pWZoRbQWKbKTpcKFJ2FnDEqCZnh/GrcAsqWA==}

  '@turf/simplify@7.2.0':
    resolution: {integrity: sha512-9YHIfSc8BXQfi5IvEMbCeQYqNch0UawIGwbboJaoV8rodhtk6kKV2wrpXdGqk/6Thg6/RWvChJFKVVTjVrULyQ==}

  '@turf/square-grid@7.2.0':
    resolution: {integrity: sha512-EmzGXa90hz+tiCOs9wX+Lak6pH0Vghb7QuX6KZej+pmWi3Yz7vdvQLmy/wuN048+wSkD5c8WUo/kTeNDe7GnmA==}

  '@turf/square@7.2.0':
    resolution: {integrity: sha512-9pMoAGFvqzCDOlO9IRSSBCGXKbl8EwMx6xRRBMKdZgpS0mZgfm9xiptMmx/t1m4qqHIlb/N+3MUF7iMBx6upcA==}

  '@turf/standard-deviational-ellipse@7.2.0':
    resolution: {integrity: sha512-+uC0pR2nRjm90JvMXe/2xOCZsYV2II1ZZ2zmWcBWv6bcFXBspcxk2QfCC3k0bj6jDapELzoQgnn3cG5lbdQV2w==}

  '@turf/tag@7.2.0':
    resolution: {integrity: sha512-TAFvsbp5TCBqXue8ui+CtcLsPZ6NPC88L8Ad6Hb/R6VAi21qe0U42WJHQYXzWmtThoTNwxi+oKSeFbRDsr0FIA==}

  '@turf/tesselate@7.2.0':
    resolution: {integrity: sha512-zHGcG85aOJJu1seCm+CYTJ3UempX4Xtyt669vFG6Hbr/Hc7ii6STQ2ysFr7lJwFtU9uyYhphVrrgwIqwglvI/Q==}

  '@turf/tin@7.2.0':
    resolution: {integrity: sha512-y24Vt3oeE6ZXvyLJamP0Ke02rPlDGE9gF7OFADnR0mT+2uectb0UTIBC3kKzON80TEAlA3GXpKFkCW5Fo/O/Kg==}

  '@turf/transform-rotate@7.2.0':
    resolution: {integrity: sha512-EMCj0Zqy3cF9d3mGRqDlYnX2ZBXe3LgT+piDR0EuF5c5sjuKErcFcaBIsn/lg1gp4xCNZFinkZ3dsFfgGHf6fw==}

  '@turf/transform-scale@7.2.0':
    resolution: {integrity: sha512-HYB+pw938eeI8s1/zSWFy6hq+t38fuUaBb0jJsZB1K9zQ1WjEYpPvKF/0//80zNPlyxLv3cOkeBucso3hzI07A==}

  '@turf/transform-translate@7.2.0':
    resolution: {integrity: sha512-zAglR8MKCqkzDTjGMIQgbg/f+Q3XcKVzr9cELw5l9CrS1a0VTSDtBZLDm0kWx0ankwtam7ZmI2jXyuQWT8Gbug==}

  '@turf/triangle-grid@7.2.0':
    resolution: {integrity: sha512-4gcAqWKh9hg6PC5nNSb9VWyLgl821cwf9yR9yEzQhEFfwYL/pZONBWCO1cwVF23vSYMSMm+/TwqxH4emxaArfw==}

  '@turf/truncate@7.2.0':
    resolution: {integrity: sha512-jyFzxYbPugK4XjV5V/k6Xr3taBjjvo210IbPHJXw0Zh7Y6sF+hGxeRVtSuZ9VP/6oRyqAOHKUrze+OOkPqBgUg==}

  '@turf/turf@7.2.0':
    resolution: {integrity: sha512-G1kKBu4hYgoNoRJgnpJohNuS7bLnoWHZ2G/4wUMym5xOSiYah6carzdTEsMoTsauyi7ilByWHx5UHwbjjCVcBw==}

  '@turf/union@7.2.0':
    resolution: {integrity: sha512-Xex/cfKSmH0RZRWSJl4RLlhSmEALVewywiEXcu0aIxNbuZGTcpNoI0h4oLFrE/fUd0iBGFg/EGLXRL3zTfpg6g==}

  '@turf/unkink-polygon@7.2.0':
    resolution: {integrity: sha512-dFPfzlIgkEr15z6oXVxTSWshWi51HeITGVFtl1GAKGMtiXJx1uMqnfRsvljqEjaQu/4AzG1QAp3b+EkSklQSiQ==}

  '@turf/voronoi@7.2.0':
    resolution: {integrity: sha512-3K6N0LtJsWTXxPb/5N2qD9e8f4q8+tjTbGV3lE3v8x06iCnNlnuJnqM5NZNPpvgvCatecBkhClO3/3RndE61Fw==}

  '@tweenjs/tween.js@18.6.4':
    resolution: {integrity: sha512-lB9lMjuqjtuJrx7/kOkqQBtllspPIN+96OvTCeJ2j5FEzinoAXTdAMFnDAQT1KVPRlnYfBrqxtqP66vDM40xxQ==}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==}

  '@types/bcryptjs@2.4.6':
    resolution: {integrity: sha512-9xlo6R2qDs5uixm0bcIqCeMCE6HiQsIyel9KQySStiyqNl2tnj2mP3DX1Nf56MD6KMenNNlBBsy3LJ7gUEQPXQ==}

  '@types/better-sqlite3@7.6.13':
    resolution: {integrity: sha512-NMv9ASNARoKksWtsq/SHakpYAYnhBrQgGD8zkLYk/jaK8jUGn08CfEdTRgYhMypUQAfzSP8W6gNLe0q19/t4VA==}

  '@types/brotli@1.3.4':
    resolution: {integrity: sha512-cKYjgaS2DMdCKF7R0F5cgx1nfBYObN2ihIuPGQ4/dlIY6RpV7OWNwe9L8V4tTVKL2eZqOkNM9FM/rgTvLf4oXw==}

  '@types/crypto-js@4.2.2':
    resolution: {integrity: sha512-sDOLlVbHhXpAUAL0YHDUUwDZf3iN4Bwi4W6a0W0b+QcAezUbRtH4FVb+9J4h+XFPW7l/gQ9F8qC7P+Ec4k8QVQ==}

  '@types/d3-array@3.2.1':
    resolution: {integrity: sha512-Y2Jn2idRrLzUfAKV2LyRImR+y4oa2AntrgID95SHJxuMUrkNXmanDSed71sRNZysveJVt1hLLemQZIady0FpEg==}

  '@types/d3-axis@3.0.6':
    resolution: {integrity: sha512-pYeijfZuBd87T0hGn0FO1vQ/cgLk6E1ALJjfkC0oJ8cbwkZl3TpgS8bVBLZN+2jjGgg38epgxb2zmoGtSfvgMw==}

  '@types/d3-brush@3.0.6':
    resolution: {integrity: sha512-nH60IZNNxEcrh6L1ZSMNA28rj27ut/2ZmI3r96Zd+1jrZD++zD3LsMIjWlvg4AYrHn/Pqz4CF3veCxGjtbqt7A==}

  '@types/d3-chord@3.0.6':
    resolution: {integrity: sha512-LFYWWd8nwfwEmTZG9PfQxd17HbNPksHBiJHaKuY1XeqscXacsS2tyoo6OdRsjf+NQYeB6XrNL3a25E3gH69lcg==}

  '@types/d3-color@1.4.5':
    resolution: {integrity: sha512-5sNP3DmtSnSozxcjqmzQKsDOuVJXZkceo1KJScDc1982kk/TS9mTPc6lpli1gTu1MIBF1YWutpHpjucNWcIj5g==}

  '@types/d3-color@3.1.3':
    resolution: {integrity: sha512-iO90scth9WAbmgv7ogoq57O9YpKmFBbmoEoCHDB2xMBY0+/KVrqAaCDyCE16dUspeOvIxFFRI+0sEtqDqy2b4A==}

  '@types/d3-contour@3.0.6':
    resolution: {integrity: sha512-BjzLgXGnCWjUSYGfH1cpdo41/hgdWETu4YxpezoztawmqsvCeep+8QGfiY6YbDvfgHz/DkjeIkkZVJavB4a3rg==}

  '@types/d3-delaunay@6.0.4':
    resolution: {integrity: sha512-ZMaSKu4THYCU6sV64Lhg6qjf1orxBthaC161plr5KuPHo3CNm8DTHiLw/5Eq2b6TsNP0W0iJrUOFscY6Q450Hw==}

  '@types/d3-dispatch@3.0.6':
    resolution: {integrity: sha512-4fvZhzMeeuBJYZXRXrRIQnvUYfyXwYmLsdiN7XXmVNQKKw1cM8a5WdID0g1hVFZDqT9ZqZEY5pD44p24VS7iZQ==}

  '@types/d3-drag@3.0.7':
    resolution: {integrity: sha512-HE3jVKlzU9AaMazNufooRJ5ZpWmLIoc90A37WU2JMmeq28w1FQqCZswHZ3xR+SuxYftzHq6WU6KJHvqxKzTxxQ==}

  '@types/d3-dsv@3.0.7':
    resolution: {integrity: sha512-n6QBF9/+XASqcKK6waudgL0pf/S5XHPPI8APyMLLUHd8NqouBGLsU8MgtO7NINGtPBtk9Kko/W4ea0oAspwh9g==}

  '@types/d3-ease@3.0.2':
    resolution: {integrity: sha512-NcV1JjO5oDzoK26oMzbILE6HW7uVXOHLQvHshBUW4UMdZGfiY6v5BeQwh9a9tCzv+CeefZQHJt5SRgK154RtiA==}

  '@types/d3-fetch@3.0.7':
    resolution: {integrity: sha512-fTAfNmxSb9SOWNB9IoG5c8Hg6R+AzUHDRlsXsDZsNp6sxAEOP0tkP3gKkNSO/qmHPoBFTxNrjDprVHDQDvo5aA==}

  '@types/d3-force@3.0.10':
    resolution: {integrity: sha512-ZYeSaCF3p73RdOKcjj+swRlZfnYpK1EbaDiYICEEp5Q6sUiqFaFQ9qgoshp5CzIyyb/yD09kD9o2zEltCexlgw==}

  '@types/d3-format@3.0.4':
    resolution: {integrity: sha512-fALi2aI6shfg7vM5KiR1wNJnZ7r6UuggVqtDA+xiEdPZQwy/trcQaHnwShLuLdta2rTymCNpxYTiMZX/e09F4g==}

  '@types/d3-geo@3.1.0':
    resolution: {integrity: sha512-856sckF0oP/diXtS4jNsiQw/UuK5fQG8l/a9VVLeSouf1/PPbBE1i1W852zVwKwYCBkFJJB7nCFTbk6UMEXBOQ==}

  '@types/d3-hierarchy@3.1.7':
    resolution: {integrity: sha512-tJFtNoYBtRtkNysX1Xq4sxtjK8YgoWUNpIiUee0/jHGRwqvzYxkq0hGVbbOGSz+JgFxxRu4K8nb3YpG3CMARtg==}

  '@types/d3-interpolate@3.0.4':
    resolution: {integrity: sha512-mgLPETlrpVV1YRJIglr4Ez47g7Yxjl1lj7YKsiMCb27VJH9W8NVM6Bb9d8kkpG/uAQS5AmbA48q2IAolKKo1MA==}

  '@types/d3-path@3.1.1':
    resolution: {integrity: sha512-VMZBYyQvbGmWyWVea0EHs/BwLgxc+MKi1zLDCONksozI4YJMcTt8ZEuIR4Sb1MMTE8MMW49v0IwI5+b7RmfWlg==}

  '@types/d3-polygon@3.0.2':
    resolution: {integrity: sha512-ZuWOtMaHCkN9xoeEMr1ubW2nGWsp4nIql+OPQRstu4ypeZ+zk3YKqQT0CXVe/PYqrKpZAi+J9mTs05TKwjXSRA==}

  '@types/d3-quadtree@3.0.6':
    resolution: {integrity: sha512-oUzyO1/Zm6rsxKRHA1vH0NEDG58HrT5icx/azi9MF1TWdtttWl0UIUsjEQBBh+SIkrpd21ZjEv7ptxWys1ncsg==}

  '@types/d3-random@3.0.3':
    resolution: {integrity: sha512-Imagg1vJ3y76Y2ea0871wpabqp613+8/r0mCLEBfdtqC7xMSfj9idOnmBYyMoULfHePJyxMAw3nWhJxzc+LFwQ==}

  '@types/d3-scale-chromatic@3.1.0':
    resolution: {integrity: sha512-iWMJgwkK7yTRmWqRB5plb1kadXyQ5Sj8V/zYlFGMUBbIPKQScw+Dku9cAAMgJG+z5GYDoMjWGLVOvjghDEFnKQ==}

  '@types/d3-scale@3.3.5':
    resolution: {integrity: sha512-YOpKj0kIEusRf7ofeJcSZQsvKbnTwpe1DUF+P2qsotqG53kEsjm7EzzliqQxMkAWdkZcHrg5rRhB4JiDOQPX+A==}

  '@types/d3-scale@4.0.9':
    resolution: {integrity: sha512-dLmtwB8zkAeO/juAMfnV+sItKjlsw2lKdZVVy6LRr0cBmegxSABiLEpGVmSJJ8O08i4+sGR6qQtb6WtuwJdvVw==}

  '@types/d3-selection@3.0.11':
    resolution: {integrity: sha512-bhAXu23DJWsrI45xafYpkQ4NtcKMwWnAC/vKrd2l+nxMFuvOT3XMYTIj2opv8vq8AO5Yh7Qac/nSeP/3zjTK0w==}

  '@types/d3-shape@3.1.7':
    resolution: {integrity: sha512-VLvUQ33C+3J+8p+Daf+nYSOsjB4GXp19/S/aGo60m9h1v6XaxjiT82lKVWJCfzhtuZ3yD7i/TPeC/fuKLLOSmg==}

  '@types/d3-time-format@4.0.3':
    resolution: {integrity: sha512-5xg9rC+wWL8kdDj153qZcsJ0FWiFt0J5RB6LYUNZjwSnesfblqrI/bJ1wBdJ8OQfncgbJG5+2F+qfqnqyzYxyg==}

  '@types/d3-time@2.1.4':
    resolution: {integrity: sha512-BTfLsxTeo7yFxI/haOOf1ZwJ6xKgQLT9dCp+EcmQv87Gox6X+oKl4mLKfO6fnWm3P22+A6DknMNEZany8ql2Rw==}

  '@types/d3-time@3.0.4':
    resolution: {integrity: sha512-yuzZug1nkAAaBlBBikKZTgzCeA+k1uy4ZFwWANOfKw5z5LRhV0gNA7gNkKm7HoK+HRN0wX3EkxGk0fpbWhmB7g==}

  '@types/d3-timer@3.0.2':
    resolution: {integrity: sha512-Ps3T8E8dZDam6fUyNiMkekK3XUsaUEik+idO9/YjPtfj2qruF8tFBXS7XhtE4iIXBLxhmLjP3SXpLhVf21I9Lw==}

  '@types/d3-transition@3.0.9':
    resolution: {integrity: sha512-uZS5shfxzO3rGlu0cC3bjmMFKsXv+SmZZcgp0KD22ts4uGXp5EVYGzu/0YdwZeKmddhcAccYtREJKkPfXkZuCg==}

  '@types/d3-voronoi@1.1.12':
    resolution: {integrity: sha512-DauBl25PKZZ0WVJr42a6CNvI6efsdzofl9sajqZr2Gf5Gu733WkDdUGiPkUHXiUvYGzNNlFQde2wdZdfQPG+yw==}

  '@types/d3-zoom@3.0.8':
    resolution: {integrity: sha512-iqMC4/YlFCSlO8+2Ii1GGGliCAY4XdeG748w5vQUbevlbDu0zSjH/+jojorQVBK/se0j6DUFNPBGSqD3YWYnDw==}

  '@types/d3@7.4.3':
    resolution: {integrity: sha512-lZXZ9ckh5R8uiFVt8ogUNf+pIrK4EsWrx2Np75WvF/eTpJ0FMHNhjXk8CKEx/+gpHbNQyJWehbFaTvqmHWB3ww==}

  '@types/draco3d@1.4.10':
    resolution: {integrity: sha512-AX22jp8Y7wwaBgAixaSvkoG4M/+PlAcm3Qs4OW8yT9DM4xUpWKeFhLueTAyZF39pviAdcDdeJoACapiAceqNcw==}

  '@types/estree@1.0.8':
    resolution: {integrity: sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==}

  '@types/geojson-vt@3.2.5':
    resolution: {integrity: sha512-qDO7wqtprzlpe8FfQ//ClPV9xiuoh2nkIgiouIptON9w5jvD/fA4szvP9GBlDVdJ5dldAl0kX/sy3URbWwLx0g==}

  '@types/geojson@7946.0.16':
    resolution: {integrity: sha512-6C8nqWur3j98U6+lXDfTUWIfgvZU+EumvpHKcYjujKH7woYyLj2sUmff0tRhrqM7BohUw7Pz3ZB1jj2gW9Fvmg==}

  '@types/google.maps@3.58.1':
    resolution: {integrity: sha512-X9QTSvGJ0nCfMzYOnaVs/k6/4L+7F5uCS+4iUmkLEls6J9S/Phv+m/i3mDeyc49ZBgwab3EFO1HEoBY7k98EGQ==}

  '@types/mapbox__point-geometry@0.1.4':
    resolution: {integrity: sha512-mUWlSxAmYLfwnRBmgYV86tgYmMIICX4kza8YnE/eIlywGe2XoOxlpVnXWwir92xRLjwyarqwpu2EJKD2pk0IUA==}

  '@types/mapbox__vector-tile@1.3.4':
    resolution: {integrity: sha512-bpd8dRn9pr6xKvuEBQup8pwQfD4VUyqO/2deGjfpe6AwC8YRlyEipvefyRJUSiCJTZuCb8Pl1ciVV5ekqJ96Bg==}

  '@types/node@16.18.126':
    resolution: {integrity: sha512-OTcgaiwfGFBKacvfwuHzzn1KLxH/er8mluiy8/uM3sGXHaRe73RrSIj01jow9t4kJEW633Ov+cOexXeiApTyAw==}

  '@types/node@20.19.7':
    resolution: {integrity: sha512-1GM9z6BJOv86qkPvzh2i6VW5+VVrXxCLknfmTkWEqz+6DqosiY28XUWCTmBcJ0ACzKqx/iwdIREfo1fwExIlkA==}

  '@types/node@20.19.9':
    resolution: {integrity: sha512-cuVNgarYWZqxRJDQHEB58GEONhOK79QVR/qYx4S7kcUObQvUwvFnYxJuuHUKm2aieN9X3yZB4LZsuYNU1Qphsw==}

  '@types/offscreencanvas@2019.7.3':
    resolution: {integrity: sha512-ieXiYmgSRXUDeOntE1InxjWyvEelZGP63M+cGuquuRLuIKKT1osnkXjxev9B7d1nXSug5vpunx+gNlbVxMlC9A==}

  '@types/pako@1.0.7':
    resolution: {integrity: sha512-YBtzT2ztNF6R/9+UXj2wTGFnC9NklAnASt3sC0h2m1bbH7G6FyBIkt4AN8ThZpNfxUo1b2iMVO0UawiJymEt8A==}

  '@types/pbf@3.0.5':
    resolution: {integrity: sha512-j3pOPiEcWZ34R6a6mN07mUkM4o4Lwf6hPNt8eilOeZhTFbxFXmKhvXl9Y28jotFPaI1bpPDJsbCprUoNke6OrA==}

  '@types/prop-types@15.7.15':
    resolution: {integrity: sha512-F6bEyamV9jKGAFBEmlQnesRPGOQqS2+Uwi0Em15xenOxHaf2hv6L8YCVn3rPdPJOiJfPiCnLIRyvwVaqMY3MIw==}

  '@types/react-dom@18.3.7':
    resolution: {integrity: sha512-MEe3UeoENYVFXzoXEWsvcpg6ZvlrFNlOQ7EOsvhI3CfAXwzPfO8Qwuxd40nepsYKqyyVQnTdEfv68q91yLcKrQ==}
    peerDependencies:
      '@types/react': ^18.0.0

  '@types/react-reconciler@0.26.7':
    resolution: {integrity: sha512-mBDYl8x+oyPX/VBb3E638N0B7xG+SPk/EAMcVPeexqus/5aTpTphQi0curhhshOqRrc9t6OPoJfEUkbymse/lQ==}

  '@types/react-reconciler@0.28.9':
    resolution: {integrity: sha512-HHM3nxyUZ3zAylX8ZEyrDNd2XZOnQ0D5XfunJF5FLQnZbHHYq4UWvW1QfelQNXv1ICNkwYhfxjwfnqivYB6bFg==}
    peerDependencies:
      '@types/react': '*'

  '@types/react@18.3.23':
    resolution: {integrity: sha512-/LDXMQh55EzZQ0uVAZmKKhfENivEvWz6E+EYzh+/MCjMhNsotd+ZHhBGIjFDTi6+fz0OhQQQLbTgdQIxxCsC0w==}

  '@types/sortablejs@1.15.8':
    resolution: {integrity: sha512-b79830lW+RZfwaztgs1aVPgbasJ8e7AXtZYHTELNXZPsERt4ymJdjV4OccDbHQAvHrCcFpbF78jkm0R6h/pZVg==}

  '@types/stats.js@0.17.4':
    resolution: {integrity: sha512-jIBvWWShCvlBqBNIZt0KAshWpvSjhkwkEu4ZUcASoAvhmrgAUI2t1dXrjSL4xXVLB4FznPrIsX3nKXFl/Dt4vA==}

  '@types/supercluster@7.1.3':
    resolution: {integrity: sha512-Z0pOY34GDFl3Q6hUFYf3HkTwKEE02e7QgtJppBt+beEAxnyOpJua+voGFvxINBHa06GwLFFym7gRPY2SiKIfIA==}

  '@types/three@0.152.0':
    resolution: {integrity: sha512-9QdaV5bfZEqeQi0xkXLdnoJt7lgYZbppdBAgJSWRicdtZoCYJ34nS2QkdeuzXt+UXExofk4OWqMzdX71HeDOVg==}

  '@types/trusted-types@2.0.7':
    resolution: {integrity: sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==}

  '@types/webxr@0.5.22':
    resolution: {integrity: sha512-Vr6Stjv5jPRqH690f5I5GLjVk8GSsoQSYJ2FVd/3jJF7KaqfwPi3ehfBS96mlQ2kPCwZaX6U0rG2+NGHBKkA/A==}

  '@use-gesture/core@10.3.1':
    resolution: {integrity: sha512-WcINiDt8WjqBdUXye25anHiNxPc0VOrlT8F6LLkU6cycrOGUDyY/yyFmsg3k8i5OLvv25llc0QC45GhR/C8llw==}

  '@use-gesture/react@10.3.1':
    resolution: {integrity: sha512-Yy19y6O2GJq8f7CHf7L0nxL8bf4PZCPaVOCgJrusOeFHY1LvHgYXnmnXg6N5iwAnbgbZCDjo60SiM6IPJi9C5g==}
    peerDependencies:
      react: '>= 16.8.0'

  '@vaadin/a11y-base@24.7.11':
    resolution: {integrity: sha512-zNjv1ZJSQlxZxK+CPSMG0uet6owb0lw/zKFFLKSFqNRfVqyzW+SViQoxE22StQ5r4dwBxyZXuutu3iisIFwiwA==}

  '@vaadin/checkbox@24.7.11':
    resolution: {integrity: sha512-x4UZ+y/eXrK2AOESxkssjqJ+eQO1kZlFpXNfeLOBwIX8+mzmQpnV4B5roC9hHbX4V/J+ijlaY9YnYAZJaz6Acg==}

  '@vaadin/component-base@24.7.11':
    resolution: {integrity: sha512-KyRJT8clpAxExouD7DDDU0b7jxus0W+TqyUgbdHXfyLxKinf9AFsYKRLmRyIusVYRK9mT2QgT82t0m7nuw0bQA==}

  '@vaadin/field-base@24.7.11':
    resolution: {integrity: sha512-5I1FVw9H/mJp9ZLurx/75/e0rjNLl8UGmNwCvV2pk7ibq7vrNzfa7dZSAgnp/MFBmvyZqHzUeZMpMnTPlfdbQQ==}

  '@vaadin/grid@24.7.11':
    resolution: {integrity: sha512-gZP7+vQDY6UZu0Lh27oIMzU2KEOcVe4n9336bHB6FaTZkovjMv7xpSfab/MWlHhdI7jETnW214pF5DnIVTHPcw==}

  '@vaadin/icon@24.7.11':
    resolution: {integrity: sha512-mJCdsQn7LzYMBdHk5MiyqfXJMe+i4AxBNDqCLC0akvB4pQFT9utWg3ighyPgcAGbnXhelU9pEu9EEhkhOZ2NYg==}

  '@vaadin/input-container@24.7.11':
    resolution: {integrity: sha512-6j0S2lG1ZMiVm06NDggHjtZ3AXEANP4wloM/GuhRP8Dot786f9ypkPqfWUqHV/LJPQ+kjpOvD6QsAF/xGm8mJQ==}

  '@vaadin/lit-renderer@24.7.11':
    resolution: {integrity: sha512-IcUvORztp2USBuZnwwKr1VmamXQZTAs1QTpe+VWf9yglELX6lWAwA3fIAIAFwej+vBiO9v8XHPJK0bDiPiTLKA==}

  '@vaadin/text-field@24.7.11':
    resolution: {integrity: sha512-vnx/DJPyUzy9yXuj/gWGUHWQGjPIaRfeezV5E7MBpQLrwRcK/uDbnc2MqSsVkLEkClGQangmvDm6svX6KCzq7Q==}

  '@vaadin/vaadin-development-mode-detector@2.0.7':
    resolution: {integrity: sha512-9FhVhr0ynSR3X2ao+vaIEttcNU5XfzCbxtmYOV8uIRnUCtNgbvMOIcyGBvntsX9I5kvIP2dV3cFAOG9SILJzEA==}

  '@vaadin/vaadin-lumo-styles@24.7.11':
    resolution: {integrity: sha512-MKRqX26XcvfHAWearEOpJmpj0Xg8G6PuMjHgkfqLEz9b9rX4xkIjKjFbrCjddujntmaGbzJfhKxWl+sj9Nkqzg==}

  '@vaadin/vaadin-material-styles@24.7.11':
    resolution: {integrity: sha512-gqomj16K0vJYRnUIoH6TuvMMpS+XtALcDfkFQ51rj4icF+WpgrhEeFYEIx85Z4MSsE9ODMwytC+Cx4qPWBlcvQ==}

  '@vaadin/vaadin-themable-mixin@24.7.11':
    resolution: {integrity: sha512-/yjaJbMtRJQZgl6XH+aV6vtMt8lJA5NJwE/N4vPL87Wh3xcA+CIigedRlP9B7OeWhjZmrPJfjCdCktNJGq63BQ==}

  '@vaadin/vaadin-usage-statistics@2.1.3':
    resolution: {integrity: sha512-8r4TNknD7OJQADe3VygeofFR7UNAXZ2/jjBFP5dgI8+2uMfnuGYgbuHivasKr9WSQ64sPej6m8rDoM1uSllXjQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  '@vis.gl/react-mapbox@8.0.4':
    resolution: {integrity: sha512-NFk0vsWcNzSs0YCsVdt2100Zli9QWR+pje8DacpLkkGEAXFaJsFtI1oKD0Hatiate4/iAIW39SQHhgfhbeEPfQ==}
    peerDependencies:
      mapbox-gl: '>=3.5.0'
      react: '>=16.3.0'
      react-dom: '>=16.3.0'
    peerDependenciesMeta:
      mapbox-gl:
        optional: true

  '@vis.gl/react-maplibre@8.0.4':
    resolution: {integrity: sha512-HwZyfLjEu+y1mUFvwDAkVxinGm8fEegaWN+O8np/WZ2Sqe5Lv6OXFpV6GWz9LOEvBYMbGuGk1FQdejo+4HCJ5w==}
    peerDependencies:
      maplibre-gl: '>=4.0.0'
      react: '>=16.3.0'
      react-dom: '>=16.3.0'
    peerDependenciesMeta:
      maplibre-gl:
        optional: true

  '@vitejs/plugin-react@4.7.0':
    resolution: {integrity: sha512-gUu9hwfWvvEDBBmgtAowQCojwZmJ5mcLn3aufeCsitijs3+f2NsrPtlAWIR6OPiqljl96GVCUbLe0HyqIpVaoA==}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0

  '@webcomponents/shadycss@1.11.2':
    resolution: {integrity: sha512-vRq+GniJAYSBmTRnhCYPAPq6THYqovJ/gzGThWbgEZUQaBccndGTi1hdiUP15HzEco0I6t4RCtXyX0rsSmwgPw==}

  '@zeit/schemas@2.36.0':
    resolution: {integrity: sha512-7kjMwcChYEzMKjeex9ZFXkt1AyNov9R5HZtjBKVsmVpw7pa7ZtlCGvCBC2vnnXctaYN+aRI61HjIqeetZW5ROg==}

  '@zip.js/zip.js@2.7.68':
    resolution: {integrity: sha512-HkabH6ThvDh1btwLbJiLFXzUW4fNN0leoYiopCg03OWg394j8NSb/Mro4i5J0S9ZLTwbBUpFrx86TzDXQRlT4Q==}
    engines: {bun: '>=0.7.0', deno: '>=1.0.0', node: '>=16.5.0'}

  abstract-logging@2.0.1:
    resolution: {integrity: sha512-2BjRTZxTPvheOvGbBslFSYOUkr+SjPtOnrLP33f+VIWLzezQpZcqVg7ja3L4dBXmzzgwT+a029jRx5PCi3JuiA==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-formats@3.0.1:
    resolution: {integrity: sha512-8iUql50EUR+uUcdRQ3HDqa6EVyo3docL8g5WJ3FNcWmu62IbkGUue/pEyLBW8VGKKucTPgqeks4fIU1DA4yowQ==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv@8.12.0:
    resolution: {integrity: sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==}

  ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}

  ansi-align@3.0.1:
    resolution: {integrity: sha512-IOfwwBF5iczOjp/WeY4YxyjqAFMQoZufdQWDd19SEExbVLNXqvpzSJ/M7Za4/sCPmQ0+GRquoA7bGcINcxew6w==}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  arch@2.2.0:
    resolution: {integrity: sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ==}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  arr-union@3.1.0:
    resolution: {integrity: sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==}
    engines: {node: '>=0.10.0'}

  asn1.js@5.4.1:
    resolution: {integrity: sha512-+I//4cYPccV8LdmBLiX8CYvf9Sp3vQsrqu2QNXRcrbiWvcx/UdlFiqUJJzxRQxgsZmvhXhn4cSKeSmoFjVdupA==}

  assign-symbols@1.0.0:
    resolution: {integrity: sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==}
    engines: {node: '>=0.10.0'}

  atomic-sleep@1.0.0:
    resolution: {integrity: sha512-kNOjDqAh7px0XWNI+4QbzoiR/nTkHAWNud2uvnJquD1/x5a7EQZMJT0AczqK0Qn67oY/TTQ1LbUKajZpp3I9tQ==}
    engines: {node: '>=8.0.0'}

  automation-events@7.1.11:
    resolution: {integrity: sha512-TnclbJ0482ydRenzrR9FIbqalHScBBdQTIXv8tVunhYx8dq7E0Eq5v5CSAo67YmLXNbx5jCstHcLZDJ33iONDw==}
    engines: {node: '>=18.2.0'}

  avvio@8.4.0:
    resolution: {integrity: sha512-CDSwaxINFy59iNwhYnkvALBwZiTydGkOecZyPkqBpABYR1KqGEsET0VOOYDwtleZSUIdeY36DC2bSZ24CO1igA==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  bcryptjs@2.4.3:
    resolution: {integrity: sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ==}

  better-sqlite3@12.2.0:
    resolution: {integrity: sha512-eGbYq2CT+tos1fBwLQ/tkBt9J5M3JEHjku4hbvQUePCckkvVf14xWj+1m7dGoK81M/fOjFT7yM9UMeKT/+vFLQ==}
    engines: {node: 20.x || 22.x || 23.x || 24.x}

  bidi-js@1.0.3:
    resolution: {integrity: sha512-RKshQI1R3YQ+n9YJz2QQ147P66ELpa1FQEg20Dk8oW9t2KgLbpDLLp9aGZ7y8WHSshDknG0bknqGw5/tyCs5tw==}

  bignumber.js@9.3.1:
    resolution: {integrity: sha512-Ko0uX15oIUS7wJ3Rb30Fs6SkVbLmPBAKdlm7q9+ak9bbIeFf0MwuBsQV6z7+X768/cHsfg+WlysDWJcmthjsjQ==}

  bindings@1.5.0:
    resolution: {integrity: sha512-p2q/t/mhvuOj/UeLlV6566GD/guowlr0hHxClI0W9m7MWYkL1F0hLo+0Aexs9HSPCtR1SXQ0TD3MMKrXZajbiQ==}

  bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}

  bn.js@4.12.2:
    resolution: {integrity: sha512-n4DSx829VRTRByMRGdjQ9iqsN0Bh4OolPsFnaZBLcbi8iXcB+kJ9s7EnRt4wILZNV3kPLHkRVfOc/HvhC3ovDw==}

  boxen@7.0.0:
    resolution: {integrity: sha512-j//dBVuyacJbvW+tvZ9HuH03fZ46QcaKvvhZickZqtB271DxJ7SNRSNxrV/dZX0085m7hISRZWbzWlJvx/rHSg==}
    engines: {node: '>=14.16'}

  brace-expansion@1.1.12:
    resolution: {integrity: sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==}

  brace-expansion@2.0.2:
    resolution: {integrity: sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==}

  brotli@1.3.3:
    resolution: {integrity: sha512-oTKjJdShmDuGW94SyyaoQvAjf30dZaHnjJ8uAF+u2/vGJkJbJPJAT1gDiOJP5v1Zb6f9KEyW/1HpuaWIXtGHPg==}

  browserslist@4.25.1:
    resolution: {integrity: sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buf-compare@1.0.1:
    resolution: {integrity: sha512-Bvx4xH00qweepGc43xFvMs5BKASXTbHaHm6+kDYIK9p/4iFwjATQkmPKHQSgJZzKbAymhztRbXUf1Nqhzl73/Q==}
    engines: {node: '>=0.10.0'}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  bytes@3.0.0:
    resolution: {integrity: sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==}
    engines: {node: '>= 0.8'}

  bytewise-core@1.2.3:
    resolution: {integrity: sha512-nZD//kc78OOxeYtRlVk8/zXqTB4gf/nlguL1ggWA8FuchMyOxcyHR4QPQZMUmA7czC+YnaBrPUCubqAWe50DaA==}

  bytewise@1.1.0:
    resolution: {integrity: sha512-rHuuseJ9iQ0na6UDhnrRVDh8YnWVlU6xM3VH6q/+yHDeUH2zIhUzP+2/h3LIrhLDBtTqzWpE3p3tP/boefskKQ==}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  camelcase@7.0.1:
    resolution: {integrity: sha512-xlx1yCK2Oc1APsPXDL2LdlNP6+uu8OCDdhOBSVT279M/S+y75O30C2VuD8T2ogdePBBl7PfPF4504tnLgX3zfw==}
    engines: {node: '>=14.16'}

  camera-controls@2.10.1:
    resolution: {integrity: sha512-KnaKdcvkBJ1Irbrzl8XD6WtZltkRjp869Jx8c0ujs9K+9WD+1D7ryBsCiVqJYUqt6i/HR5FxT7RLASieUD+Q5w==}
    peerDependencies:
      three: '>=0.126.1'

  caniuse-lite@1.0.30001727:
    resolution: {integrity: sha512-pB68nIHmbN6L/4C6MH1DokyR3bYqFwjaSs/sWDHGj4CTcFtQUQMuJftVwWkXq7mNWOybD3KhUv3oWHoGxgP14Q==}

  cartocolor@5.0.2:
    resolution: {integrity: sha512-Ihb/wU5V6BVbHwapd8l/zg7bnhZ4YPFVfa7quSpL86lfkPJSf4YuNBT+EvesPRP5vSqhl6vZVsQJwCR8alBooQ==}

  chalk-template@0.4.0:
    resolution: {integrity: sha512-/ghrgmhfY8RaSdeo43hNXxpoHAtxdbskUHjPpfqUWGttFgycUhYPGx3YZBCnUCvOa7Doivn1IZec3DEGFoMgLg==}
    engines: {node: '>=12'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chalk@5.0.1:
    resolution: {integrity: sha512-Fo07WOYGqMfCWHOzSXOt2CxDbC6skS/jO9ynEcmpANMoPrD+W1r1K6Vx7iNm+AQmETU1Xr2t+n8nzkV9t6xh3w==}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  charenc@0.0.2:
    resolution: {integrity: sha512-yrLQ/yVUFXkzg7EDQsPieE/53+0RlaWTs+wBrvW36cyilJ2SaDWfl4Yj7MtLTXleV9uEKefbAGUPv2/iWSooRA==}

  chownr@1.1.4:
    resolution: {integrity: sha512-jJ0bqzaylmJtVnNgzTeSOs8DPavpbYgEr/b0YL8/2GO3xJEhInFmhKMUnEJQjZumK7KXGFhUy89PrsJWlakBVg==}

  chownr@3.0.0:
    resolution: {integrity: sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==}
    engines: {node: '>=18'}

  cli-boxes@3.0.0:
    resolution: {integrity: sha512-/lzGpEWL/8PfI0BmBOPRwp0c/wFNX1RdUML3jK/RcSBA9T8mZDdQpqYBKtCFTOfQbwPqWEOpjqW+Fnayc0969g==}
    engines: {node: '>=10'}

  clipboardy@3.0.0:
    resolution: {integrity: sha512-Su+uU5sr1jkUy1sGRpLKjKrvEOVXgSgiSInwa/qeID6aJ07yh+5NWc3h2QfjHjBnfX4LhtFcuAWKUsJ3r+fjbg==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clsx@2.1.1:
    resolution: {integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==}
    engines: {node: '>=6'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-convert@3.1.0:
    resolution: {integrity: sha512-TVoqAq8ZDIpK5lsQY874DDnu65CSsc9vzq0wLpNQ6UMBq81GSZocVazPiBbYGzngzBOIRahpkTzCLVe2at4MfA==}
    engines: {node: '>=14.6'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-name@2.0.0:
    resolution: {integrity: sha512-SbtvAMWvASO5TE2QP07jHBMXKafgdZz8Vrsrn96fiL+O92/FN/PLARzUW5sKt013fjAprK2d2iCn2hk2Xb5oow==}
    engines: {node: '>=12.20'}

  color-string@2.0.1:
    resolution: {integrity: sha512-5z9FbYTZPAo8iKsNEqRNv+OlpBbDcoE+SY9GjLfDUHEfcNNV7tS9eSAlFHEaub/r5tBL9LtskAeq1l9SaoZ5tQ==}
    engines: {node: '>=18'}

  color@5.0.0:
    resolution: {integrity: sha512-16BlyiuyLq3MLxpRWyOTiWsO3ii/eLQLJUQXBSNcxMBBSnyt1ee9YUdaozQp03ifwm5woztEZGDbk9RGVuCsdw==}
    engines: {node: '>=18'}

  colorbrewer@1.5.6:
    resolution: {integrity: sha512-fONg2pGXyID8zNgKHBlagW8sb/AMShGzj4rRJfz5biZ7iuHQZYquSCLE/Co1oSQFmt/vvwjyezJCejQl7FG/tg==}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}

  composed-offset-position@0.0.6:
    resolution: {integrity: sha512-Q7dLompI6lUwd7LWyIcP66r4WcS9u7AL2h8HaeipiRfCRPLMWqRx8fYsjb4OHi6UQFifO7XtNC2IlEJ1ozIFxw==}
    peerDependencies:
      '@floating-ui/utils': ^0.2.5

  compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}

  compression@1.7.4:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  concaveman@1.2.1:
    resolution: {integrity: sha512-PwZYKaM/ckQSa8peP5JpVr7IMJ4Nn/MHIaWUjP4be+KoZ7Botgs8seAZGpmaOM+UZXawcdYRao/px9ycrCihHw==}

  concurrently@8.2.2:
    resolution: {integrity: sha512-1dP4gpXFhei8IOtlXRE/T/4H88ElHgTiUzh71YUmtjTEHMSRS2Z/fgOxHSxxusGHogsRfxNq1vyAwxSC+EVyDg==}
    engines: {node: ^14.13.0 || >=16.0.0}
    hasBin: true

  content-disposition@0.5.2:
    resolution: {integrity: sha512-kRGRZw3bLlFISDBgwTSA1TMBFN6J6GWDeubmDE3AF+3+yXL8hTWv8r5rkLbqYXY4RjPk/EzHnClI3zQf1cFmHA==}
    engines: {node: '>= 0.6'}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie@0.7.2:
    resolution: {integrity: sha512-yki5XnKuf750l50uGTllt6kKILY4nQ1eNIQatoXEByZ5dWgnKqbnqmTrBE5B4N7lrMJKQ2ytWMiTO2o0v6Ew/w==}
    engines: {node: '>= 0.6'}

  core-assert@0.2.1:
    resolution: {integrity: sha512-IG97qShIP+nrJCXMCgkNZgH7jZQ4n8RpPyPeXX++T6avR/KhLhgLiHKoEn5Rc1KjfycSfA9DMa6m+4C4eguHhw==}
    engines: {node: '>=0.10.0'}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  cosmiconfig@8.3.6:
    resolution: {integrity: sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  cross-env@7.0.3:
    resolution: {integrity: sha512-+/HKd6EgcQCJGh2PSjZuUitQBQynKor4wrFbRg4DtAgS1aWO+gU52xpH7M9ScGgXSYmAVS9bIJ8EzuaGw0oNAw==}
    engines: {node: '>=10.14', npm: '>=6', yarn: '>=1'}
    hasBin: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  crypt@0.0.2:
    resolution: {integrity: sha512-mCxBlsHFYh9C+HVpiEacem8FEBnMXgU9gy4zmNC+SXAZNB/1idgp/aulFJ4FgCi7GPEVbfyng092GqL2k2rmow==}

  cssfilter@0.0.10:
    resolution: {integrity: sha512-FAaLDaplstoRsDR8XGYH51znUN0UY7nMc6Z9/fvE8EXGwvJE9hu7W2vHwx1+bd6gCYnln9nLbzxFTrcO9YQDZw==}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  d3-array@1.2.4:
    resolution: {integrity: sha512-KHW6M86R+FUPYGb3R5XiYjXPq7VzwxZ22buHhAEVG5ztoEcZZMLov530mmccaqA1GghZArjQV46fuc8kUqhhHw==}

  d3-array@3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==}
    engines: {node: '>=12'}

  d3-axis@3.0.0:
    resolution: {integrity: sha512-IH5tgjV4jE/GhHkRV0HiVYPDtvfjHQlQfJHs0usq7M30XcSBvOotpmH1IgkcXsO/5gEQZD43B//fc7SRT5S+xw==}
    engines: {node: '>=12'}

  d3-brush@3.0.0:
    resolution: {integrity: sha512-ALnjWlVYkXsVIGlOsuWH1+3udkYFI48Ljihfnh8FZPF2QS9o+PzGLBslO0PjzVoHLZ2KCVgAM8NVkXPJB2aNnQ==}
    engines: {node: '>=12'}

  d3-chord@3.0.1:
    resolution: {integrity: sha512-VE5S6TNa+j8msksl7HwjxMHDM2yNK3XCkusIlpX5kwauBfXuyLAtNg9jCp/iHH61tgI4sb6R/EIMWCqEIdjT/g==}
    engines: {node: '>=12'}

  d3-color@3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}

  d3-contour@4.0.2:
    resolution: {integrity: sha512-4EzFTRIikzs47RGmdxbeUvLWtGedDUNkTcmzoeyg4sP/dvCexO47AaQL7VKy/gul85TOxw+IBgA8US2xwbToNA==}
    engines: {node: '>=12'}

  d3-delaunay@6.0.4:
    resolution: {integrity: sha512-mdjtIZ1XLAM8bm/hx3WwjfHt6Sggek7qH043O8KEjDXN40xi3vx/6pYSVTwLjEgiXQTbvaouWKynLBiUZ6SK6A==}
    engines: {node: '>=12'}

  d3-dispatch@3.0.1:
    resolution: {integrity: sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==}
    engines: {node: '>=12'}

  d3-drag@3.0.0:
    resolution: {integrity: sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==}
    engines: {node: '>=12'}

  d3-dsv@3.0.1:
    resolution: {integrity: sha512-UG6OvdI5afDIFP9w4G0mNq50dSOsXHJaRE8arAS5o9ApWnIElp8GZw1Dun8vP8OyHOZ/QJUKUJwxiiCCnUwm+Q==}
    engines: {node: '>=12'}
    hasBin: true

  d3-ease@3.0.1:
    resolution: {integrity: sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==}
    engines: {node: '>=12'}

  d3-fetch@3.0.1:
    resolution: {integrity: sha512-kpkQIM20n3oLVBKGg6oHrUchHM3xODkTzjMoj7aWQFq5QEM+R6E4WkzT5+tojDY7yjez8KgCBRoj4aEr99Fdqw==}
    engines: {node: '>=12'}

  d3-force@3.0.0:
    resolution: {integrity: sha512-zxV/SsA+U4yte8051P4ECydjD/S+qeYtnaIyAs9tgHCqfguma/aAQDjo85A9Z6EKhBirHRJHXIgJUlffT4wdLg==}
    engines: {node: '>=12'}

  d3-format@3.1.0:
    resolution: {integrity: sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==}
    engines: {node: '>=12'}

  d3-geo@1.7.1:
    resolution: {integrity: sha512-O4AempWAr+P5qbk2bC2FuN/sDW4z+dN2wDf9QV3bxQt4M5HfOEeXLgJ/UKQW0+o1Dj8BE+L5kiDbdWUMjsmQpw==}

  d3-geo@3.1.1:
    resolution: {integrity: sha512-637ln3gXKXOwhalDzinUgY83KzNWZRKbYubaG+fGVuc/dxO64RRljtCTnf5ecMyE1RIdtqpkVcq0IbtU2S8j2Q==}
    engines: {node: '>=12'}

  d3-hexbin@0.2.2:
    resolution: {integrity: sha512-KS3fUT2ReD4RlGCjvCEm1RgMtp2NFZumdMu4DBzQK8AZv3fXRM6Xm8I4fSU07UXvH4xxg03NwWKWdvxfS/yc4w==}

  d3-hierarchy@3.1.2:
    resolution: {integrity: sha512-FX/9frcub54beBdugHjDCdikxThEqjnR93Qt7PvQTOHxyiNCAlvMrHhclk3cD5VeAaq9fxmfRp+CnWw9rEMBuA==}
    engines: {node: '>=12'}

  d3-interpolate@3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}

  d3-path@3.1.0:
    resolution: {integrity: sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==}
    engines: {node: '>=12'}

  d3-polygon@3.0.1:
    resolution: {integrity: sha512-3vbA7vXYwfe1SYhED++fPUQlWSYTTGmFmQiany/gdbiWgU/iEyQzyymwL9SkJjFFuCS4902BSzewVGsHHmHtXg==}
    engines: {node: '>=12'}

  d3-quadtree@3.0.1:
    resolution: {integrity: sha512-04xDrxQTDTCFwP5H6hRhsRcb9xxv2RzkcsygFzmkSIOJy3PeRJP7sNk3VRIbKXcog561P9oU0/rVH6vDROAgUw==}
    engines: {node: '>=12'}

  d3-random@3.0.1:
    resolution: {integrity: sha512-FXMe9GfxTxqd5D6jFsQ+DJ8BJS4E/fT5mqqdjovykEB2oFbTMDVdg1MGFxfQW+FBOGoB++k8swBrgwSHT1cUXQ==}
    engines: {node: '>=12'}

  d3-scale-chromatic@3.1.0:
    resolution: {integrity: sha512-A3s5PWiZ9YCXFye1o246KoscMWqf8BsD9eRiJ3He7C9OBaxKhAd5TFCdEx/7VbKtxxTsu//1mMJFrEt572cEyQ==}
    engines: {node: '>=12'}

  d3-scale@4.0.2:
    resolution: {integrity: sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==}
    engines: {node: '>=12'}

  d3-selection@3.0.0:
    resolution: {integrity: sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==}
    engines: {node: '>=12'}

  d3-shape@3.2.0:
    resolution: {integrity: sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==}
    engines: {node: '>=12'}

  d3-time-format@4.1.0:
    resolution: {integrity: sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==}
    engines: {node: '>=12'}

  d3-time@3.1.0:
    resolution: {integrity: sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==}
    engines: {node: '>=12'}

  d3-timer@3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}

  d3-transition@3.0.1:
    resolution: {integrity: sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==}
    engines: {node: '>=12'}
    peerDependencies:
      d3-selection: 2 - 3

  d3-voronoi@1.1.2:
    resolution: {integrity: sha512-RhGS1u2vavcO7ay7ZNAPo4xeDh/VYeGof3x5ZLJBQgYhLegxr3s5IykvWmJ94FTU6mcbtp4sloqZ54mP6R4Utw==}

  d3-zoom@3.0.0:
    resolution: {integrity: sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==}
    engines: {node: '>=12'}

  d3@7.9.0:
    resolution: {integrity: sha512-e1U46jVP+w7Iut8Jt8ri1YsPOvFpg46k+K8TpCb0P+zjCkjkPnV7WzfDJzMHy1LnA+wj5pLT1wjO901gLXeEhA==}
    engines: {node: '>=12'}

  data-uri-to-buffer@4.0.1:
    resolution: {integrity: sha512-0R9ikRb668HB7QDxT1vkpuUBtqc53YyAwMwGeUFKRojY/NWKvdZ+9UYtRfGmhqNbRkTSVpMbmyhXipFFv2cb/A==}
    engines: {node: '>= 12'}

  date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}

  dayjs@1.11.13:
    resolution: {integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.1:
    resolution: {integrity: sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  deck.gl@9.1.13:
    resolution: {integrity: sha512-YmeEXFfVJTOjSOI20dy631+8BZ4UuhtSDxxRWz/iHTBxYe1MlPcdLkQYgRgXS+GTHWrxLvqo/mSbILuGwi09cw==}
    peerDependencies:
      '@arcgis/core': ^4.0.0
      react: '>=16.3.0'
      react-dom: '>=16.3.0'
    peerDependenciesMeta:
      '@arcgis/core':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  decompress-response@6.0.0:
    resolution: {integrity: sha512-aW35yZM6Bb/4oJlZncMH2LCoZtJXTRxES17vE3hoRiowU2kWHaJKFkSBDnDR+cm9J+9QhXmREyIfv0pji9ejCQ==}
    engines: {node: '>=10'}

  deep-extend@0.6.0:
    resolution: {integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==}
    engines: {node: '>=4.0.0'}

  deep-strict-equal@0.2.0:
    resolution: {integrity: sha512-3daSWyvZ/zwJvuMGlzG1O+Ow0YSadGfb3jsh9xoCutv2tWyB9dA4YvR9L9/fSdDZa2dByYQe+TqapSGUrjnkoA==}
    engines: {node: '>=0.10.0'}

  delaunator@5.0.1:
    resolution: {integrity: sha512-8nvh+XBe96aCESrGOqMp/84b13H9cdKbG5P2ejQCh4d4sK9RL4371qou9drQjMhvnPmhWl5hnmqbEE0fXr9Xnw==}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  detect-gpu@5.0.70:
    resolution: {integrity: sha512-bqerEP1Ese6nt3rFkwPnGbsUF9a4q+gMmpTVVOEzoCyeCc+y7/RvJnQZJx1JwhgQI5Ntg0Kgat8Uu7XpBqnz1w==}

  detect-libc@2.0.4:
    resolution: {integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==}
    engines: {node: '>=8'}

  dnd-core@16.0.1:
    resolution: {integrity: sha512-HK294sl7tbw6F6IeuK16YSBUoorvHpY8RHO+9yFfaJyCDVb6n7PRcezrOEOa2SBCqiYpemh5Jx20ZcjKdFAVng==}

  dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==}

  dotenv@17.2.0:
    resolution: {integrity: sha512-Q4sgBT60gzd0BB0lSyYD3xM4YxrXA9y4uBDof1JNYGzOXrQdQ6yX+7XIAqoFOGQFOTK1D3Hts5OllpxMDZFONQ==}
    engines: {node: '>=12'}

  draco3d@1.5.7:
    resolution: {integrity: sha512-m6WCKt/erDXcw+70IJXnG7M3awwQPAsZvJGX5zY7beBqpELw6RDGkYVU0W43AFxye4pDZ5i2Lbyc/NNGqwjUVQ==}

  earcut@2.2.4:
    resolution: {integrity: sha512-/pjZsA1b4RPHbeWZQn66SWS8nZZWLQQ23oE3Eam7aroEFGEvwKAsJfZ9ytiEMycfzXWpca4FA9QIOehf7PocBQ==}

  earcut@3.0.2:
    resolution: {integrity: sha512-X7hshQbLyMJ/3RPhyObLARM2sNxxmRALLKx1+NVFFnQ9gKzmCrxm9+uLIAdBcvc8FNLpctqlQ2V6AE92Ol9UDQ==}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  ecdsa-sig-formatter@1.0.11:
    resolution: {integrity: sha512-nagl3RYrbNv6kQkeJIpt6NJZy8twLB/2vtz6yN9Z4vRKHN4/QZJIEbqohALSgwKdnksuY3k5Addp5lg8sVoVcQ==}

  echarts-for-react@3.0.2:
    resolution: {integrity: sha512-DRwIiTzx8JfwPOVgGttDytBqdp5VzCSyMRIxubgU/g2n9y3VLUmF2FK7Icmg/sNVkv4+rktmrLN9w22U2yy3fA==}
    peerDependencies:
      echarts: ^3.0.0 || ^4.0.0 || ^5.0.0
      react: ^15.0.0 || >=16.0.0

  echarts@5.6.0:
    resolution: {integrity: sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==}

  electron-to-chromium@1.5.190:
    resolution: {integrity: sha512-k4McmnB2091YIsdCgkS0fMVMPOJgxl93ltFzaryXqwip1AaxeDqKCGLxkXODDA5Ab/D+tV5EL5+aTx76RvLRxw==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  end-of-stream@1.4.5:
    resolution: {integrity: sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==}

  enhanced-resolve@5.18.2:
    resolution: {integrity: sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ==}
    engines: {node: '>=10.13.0'}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  entities@6.0.1:
    resolution: {integrity: sha512-aN97NXWF6AWBTahfVOIrB/NShkzi5H7F9r1s9mD3cDj4Ko5f2qhhVoYMibXF7GlLveb/D2ioWay8lxI97Ven3g==}
    engines: {node: '>=0.12'}

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  esbuild@0.25.6:
    resolution: {integrity: sha512-GVuzuUwtdsghE3ocJ9Bs8PNoF13HNQ5TXbEi2AhvVb8xU1Iwt9Fos9FEamfoee+u/TOsn7GUWc04lz46n2bbTg==}
    engines: {node: '>=18'}
    hasBin: true

  esbuild@0.25.8:
    resolution: {integrity: sha512-vVC0USHGtMi8+R4Kz8rt6JhEWLxsv9Rnu/lGYbPR8u47B+DCBksq9JarW0zOO7bs37hyOK1l2/oqtbciutL5+Q==}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  esri-loader@3.7.0:
    resolution: {integrity: sha512-cB1Sw9EQjtW4mtT7eFBjn/6VaaIWNTjmTd2asnnEyuZk1xVSFRMCfLZSBSjZM7ZarDcVu5WIjOP0t0MYVu4hVQ==}
    deprecated: Use @arcgis/core instead.

  estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==}

  execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}

  expand-template@2.0.3:
    resolution: {integrity: sha512-XYfuKMvj4O35f/pOXLObndIRvyQ+/+6AhODh+OKWj9S9498pHHn/IMszH+gt0fBCRWMNfk1ZSp5x3AifmnI2vg==}
    engines: {node: '>=6'}

  extend-shallow@2.0.1:
    resolution: {integrity: sha512-zCnTtlxNoAiDc3gqY2aYAWFx7XWWiasuF2K8Me5WbN8otHKTUKBwjPtNpRs/rbUZm7KxWAaNj7P1a/p52GbVug==}
    engines: {node: '>=0.10.0'}

  extend-shallow@3.0.2:
    resolution: {integrity: sha512-BwY5b5Ql4+qZoefgMj2NUmx+tehVTH/Kf4k1ZEtOHNFcm2wSxMRo992l6X3TIgni2eZVTZ85xMOjF31fwZAj6Q==}
    engines: {node: '>=0.10.0'}

  fast-content-type-parse@1.1.0:
    resolution: {integrity: sha512-fBHHqSTFLVnR61C+gltJuE5GkVQMV0S2nqUO8TJ+5Z3qAKG8vAx4FKai1s5jq/inV1+sREynIWSuQ6HgoSXpDQ==}

  fast-decode-uri-component@1.0.1:
    resolution: {integrity: sha512-WKgKWg5eUxvRZGwW8FvfbaH7AXSh2cL+3j5fMGzUMCxWBJ3dV3a7Wz8y2f/uQ0e3B6WmodD3oS54jTQ9HVTIIg==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-json-stringify@5.16.1:
    resolution: {integrity: sha512-KAdnLvy1yu/XrRtP+LJnxbBGrhN+xXu+gt3EUvZhYGKCr3lFHq/7UFJHHFgmJKoqlh6B40bZLEv7w46B0mqn1g==}

  fast-jwt@4.0.5:
    resolution: {integrity: sha512-QnpNdn0955GT7SlT8iMgYfhTsityUWysrQjM+Q7bGFijLp6+TNWzlbSMPvgalbrQGRg4ZaHZgMcns5fYOm5avg==}
    engines: {node: '>=16'}

  fast-querystring@1.1.2:
    resolution: {integrity: sha512-g6KuKWmFXc0fID8WWH0jit4g0AGBoJhCkJMb1RmbsSEUNvQ+ZC8D6CUZ+GtF8nMzSPXnhiePyyqqipzNNEnHjg==}

  fast-redact@3.5.0:
    resolution: {integrity: sha512-dwsoQlS7h9hMeYUq1W++23NDcBLV4KqONnITDV9DjfS3q1SgDGVrBdvvTLUotWtPSD7asWDV9/CmsZPy8Hf70A==}
    engines: {node: '>=6'}

  fast-uri@2.4.0:
    resolution: {integrity: sha512-ypuAmmMKInk5q7XcepxlnUWDLWv4GFtaJqAzWKqn62IpQ3pejtr5dTVbt3vwqVaMKmkNR55sTT+CqUKIaT21BA==}

  fast-uri@3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}

  fast-xml-parser@4.5.3:
    resolution: {integrity: sha512-RKihhV+SHsIUGXObeVy9AXiBbFwkVk7Syp8XgwN5U3JV416+Gwp/GO9i0JYKmikykgz/UHRrrV4ROuZEo/T0ig==}
    hasBin: true

  fastfall@1.5.1:
    resolution: {integrity: sha512-KH6p+Z8AKPXnmA7+Iz2Lh8ARCMr+8WNPVludm1LGkZoD2MjY6LVnRMtTKhkdzI+jr0RzQWXKzKyBJm1zoHEL4Q==}
    engines: {node: '>=0.10.0'}

  fastify-plugin@4.5.1:
    resolution: {integrity: sha512-stRHYGeuqpEZTL1Ef0Ovr2ltazUT9g844X5z/zEBFLG8RYlpDiOCIG+ATvYEp+/zmc7sN29mcIMp8gvYplYPIQ==}

  fastify@4.29.1:
    resolution: {integrity: sha512-m2kMNHIG92tSNWv+Z3UeTR9AWLLuo7KctC7mlFPtMEVrfjIhmQhkQnT9v15qA/BfVq3vvj134Y0jl9SBje3jXQ==}

  fastparallel@2.4.1:
    resolution: {integrity: sha512-qUmhxPgNHmvRjZKBFUNI0oZuuH9OlSIOXmJ98lhKPxMZZ7zS/Fi0wRHOihDSz0R1YiIOjxzOY4bq65YTcdBi2Q==}

  fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}

  fastseries@1.7.2:
    resolution: {integrity: sha512-dTPFrPGS8SNSzAt7u/CbMKCJ3s01N04s4JFbORHcmyvVfVKmbhMD1VtRbh5enGHxkaQDqWyLefiKOGGmohGDDQ==}

  fdir@6.4.6:
    resolution: {integrity: sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  fetch-blob@3.2.0:
    resolution: {integrity: sha512-7yAQpD2UMJzLi1Dqv7qFYnPbaPx7ZfFK6PiIxQ4PfkGPyNyl2Ugx+a/umUonmKqjhM4DnfbMvdX6otXq83soQQ==}
    engines: {node: ^12.20 || >= 14.13}

  fflate@0.6.10:
    resolution: {integrity: sha512-IQrh3lEPM93wVCEczc9SaAOvkmcoQn/G8Bo1e8ZPlY3X3bnAxWaBdvTdvM1hP62iZp0BXWDy4vTAy4fF0+Dlpg==}

  fflate@0.7.4:
    resolution: {integrity: sha512-5u2V/CDW15QM1XbbgS+0DfPxVB+jUKhWEKuuFuHncbk3tEEqzmoXL+2KyOFuKGqOnmdIy0/davWF1CkuwtibCw==}

  file-uri-to-path@1.0.0:
    resolution: {integrity: sha512-0Zt+s3L7Vf1biwWZ29aARiVYLx7iMGnEUl9x33fbB/j3jR81u/O2LbqK+Bm1CDSNDKVtJ/YjwY7TUd5SkeLQLw==}

  find-my-way@8.2.2:
    resolution: {integrity: sha512-Dobi7gcTEq8yszimcfp/R7+owiT4WncAJ7VTTgFH1jYJ5GaG1FbhjwDG820hptN0QDFvzVY3RfCzdInvGPGzjA==}
    engines: {node: '>=14'}

  focus-trap@7.6.5:
    resolution: {integrity: sha512-7Ke1jyybbbPZyZXFxEftUtxFGLMpE2n6A+z//m4CRDlj0hW+o3iYSmh8nFlYMurOiJVDmJRilUQtJr08KfIxlg==}

  foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}

  formdata-polyfill@4.0.10:
    resolution: {integrity: sha512-buewHzMvYL29jdeQTVILecSaZKnt/RJWjoZCF5OW60Z67/GmSLBkOFM7qh1PI3zFNtJbaZL5eQu1vLfazOwj4g==}
    engines: {node: '>=12.20.0'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fs-constants@1.0.0:
    resolution: {integrity: sha512-y6OAwoSIf7FyjMIv94u+b5rdheZEjzR63GTyZJm5qh4Bi+2YgwLCcI/fPFZkL5PSixOt6ZNKm+w+Hfp/Bciwow==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  geojson-equality-ts@1.0.2:
    resolution: {integrity: sha512-h3Ryq+0mCSN/7yLs0eDgrZhvc9af23o/QuC4aTiuuzP/MRCtd6mf5rLsLRY44jX0RPUfM8c4GqERQmlUxPGPoQ==}

  geojson-polygon-self-intersections@1.2.1:
    resolution: {integrity: sha512-/QM1b5u2d172qQVO//9CGRa49jEmclKEsYOQmWP9ooEjj63tBM51m2805xsbxkzlEELQ2REgTf700gUhhlegxA==}

  geojson-vt@4.0.2:
    resolution: {integrity: sha512-AV9ROqlNqoZEIJGfm1ncNjEXfkz2hdFlZf0qkVfmkwdKa8vj7H16YUOT81rJw1rdFhyEDlN2Tds91p/glzbl5A==}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  get-tsconfig@4.10.1:
    resolution: {integrity: sha512-auHyJ4AgMz7vgS8Hp3N6HXSmlMdUyhSUrfBF16w153rxtLIEOE+HGqaBppczZvnHLqQJfiHotCYpNhl0lUROFQ==}

  get-value@2.0.6:
    resolution: {integrity: sha512-Ln0UQDlxH1BapMu3GPtf7CuYNwRZf2gwCuPqbyG6pB8WfmFpzqcy4xtAaAMUhnNqjMKTiCPZG2oMT3YSx8U2NA==}
    engines: {node: '>=0.10.0'}

  github-from-package@0.0.0:
    resolution: {integrity: sha512-SyHy3T1v2NUXn29OsWdxmK6RwHD+vkj3v8en8AOBZ1wBQ/hCAQ5bAQTD02kW4W9tUp/3Qh6J8r9EvntiyCmOOw==}

  gl-matrix@3.4.3:
    resolution: {integrity: sha512-wcCp8vu8FT22BnvKVPjXa/ICBWRq/zjFfdofZy1WSpQZpphblv12/bOQLBC1rMM7SGOFS9ltVmKOHil5+Ml7gA==}

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  global-prefix@4.0.0:
    resolution: {integrity: sha512-w0Uf9Y9/nyHinEk5vMJKRie+wa4kR5hmDbEhGGds/kG1PwGLLHKRoNMeJOyCQjjBkANlnScqgzcFwGHgmgLkVA==}
    engines: {node: '>=16'}

  glsl-noise@0.0.0:
    resolution: {integrity: sha512-b/ZCF6amfAUb7dJM/MxRs7AetQEahYzJ8PtgfrmEdtw6uyGOr+ZSGtgjFm6mfsBkxJ4d2W7kg+Nlqzqvn3Bc0w==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  h3-js@4.1.0:
    resolution: {integrity: sha512-LQhmMl1dRQQjMXPzJc7MpZ/CqPOWWuAvVEoVJM9n/s7vHypj+c3Pd5rLQCkAsOgAoAYKbNCsYFE++LF7MvSfCQ==}
    engines: {node: '>=4', npm: '>=3', yarn: '>=1.3.0'}

  h3-js@4.2.1:
    resolution: {integrity: sha512-HYiUrq5qTRFqMuQu3jEHqxXLk1zsSJiby9Lja/k42wHjabZG7tN9rOuzT/PEFf+Wa7rsnHLMHRWIu0mgcJ0ewQ==}
    engines: {node: '>=4', npm: '>=3', yarn: '>=1.3.0'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  image-size@0.7.5:
    resolution: {integrity: sha512-Hiyv+mXHfFEP7LzUL/llg9RwFxxY+o9N3JVLIeG5E7iFIFAalxvRU9UZthBdYDEVnzHMgjnKJPPpay5BWf1g9g==}
    engines: {node: '>=6.9.0'}
    hasBin: true

  immediate@3.0.6:
    resolution: {integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==}

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}

  ini@4.1.3:
    resolution: {integrity: sha512-X7rqawQBvfdjS10YU1y1YVreA3SsLrW9dX2CewP2EbBJM4ypVNLDkO5y04gejPwKIY9lR+7r9gn3rFPt/kmWFg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  interactjs@1.10.27:
    resolution: {integrity: sha512-y/8RcCftGAF24gSp76X2JS3XpHiUvDQyhF8i7ujemBz77hwiHDuJzftHx7thY8cxGogwGiPJ+o97kWB6eAXnsA==}

  internmap@2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==}
    engines: {node: '>=12'}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-buffer@1.1.6:
    resolution: {integrity: sha512-NcdALwpXkTm5Zvvbk7owOUSvVvBKDgKP5/ewfXEznmQFfs4ZRmanOeKBTjRVjka3QFoN6XJ+9F3USqfHqTaU5w==}

  is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  is-error@2.2.2:
    resolution: {integrity: sha512-IOQqts/aHWbiisY5DuPJQ0gcbvaLFCa7fBa9xoLfxBZvQ+ZI/Zh9xoI7Gk+G64N0FdK4AbibytHht2tWgpJWLg==}

  is-extendable@0.1.1:
    resolution: {integrity: sha512-5BMULNob1vgFX6EjQw5izWDxrecWK9AM72rugNr0TFldMOi0fj6Jk+zeKIt0xGj4cEfQIJth4w3OKWOJ4f+AFw==}
    engines: {node: '>=0.10.0'}

  is-extendable@1.0.1:
    resolution: {integrity: sha512-arnXMxT1hhoKo9k1LZdmlNyJdDDfy2v0fXjFlmok4+i8ul/6WlbVge9bhM74OpNPQPMGUToDtz+KXa1PneJxOA==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}

  is-port-reachable@4.0.0:
    resolution: {integrity: sha512-9UoipoxYmSk6Xy7QFgRv2HDyaysmgSG75TFQs6S+3pDM7ZhKTF/bskZV+0UlABHzKjNVhPjYCLfeZUEg1wXxig==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isexe@3.1.1:
    resolution: {integrity: sha512-LpB/54B+/2J5hqQ7imZHfdU31OlgQqx7ZicVlkm9kzg9/w8GKLEcFfJl/t7DCEDueOyBAD6zCCwTO6Fzs0NoEQ==}
    engines: {node: '>=16'}

  isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  its-fine@1.2.5:
    resolution: {integrity: sha512-fXtDA0X0t0eBYAGLVM5YsgJGsJ5jEmqZEPrGbzdf5awjv0xE7nqv3TVnvtUF060Tkes15DbDAKW/I48vsb6SyA==}
    peerDependencies:
      react: '>=18.0'

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jiti@2.5.1:
    resolution: {integrity: sha512-twQoecYPiVA5K/h6SxtORw/Bs3ar+mLUtoPSc7iMXzQzK8d7eJ/R09wmTwAjiamETn1cXYPGfNnu7DMoHgu12w==}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsep@0.3.5:
    resolution: {integrity: sha512-AoRLBDc6JNnKjNcmonituEABS5bcfqDhQAWWXNTFrqu6nVXBpBAGfcoTGZMFlIrh9FjmE1CQyX9CTNwZrXMMDA==}
    engines: {node: '>= 6.0.0'}

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-schema-ref-resolver@1.0.1:
    resolution: {integrity: sha512-EJAj1pgHc1hxF6vo2Z3s69fMjO1INq6eGHXZ8Z6wCQeldCuwxGK9Sxf4/cScGn3FZubCVUehfWtcDM/PLteCQw==}

  json-schema-resolver@2.0.0:
    resolution: {integrity: sha512-pJ4XLQP4Q9HTxl6RVDLJ8Cyh1uitSs0CzDBAz1uoJ4sRD/Bk7cFSXL1FUXDW3zJ7YnfliJx6eu8Jn283bpZ4Yg==}
    engines: {node: '>=10'}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-stringify-pretty-compact@3.0.0:
    resolution: {integrity: sha512-Rc2suX5meI0S3bfdZuA7JMFBGkJ875ApfVyq2WHELjBiiG22My/l7/8zPpH/CfFVQHuVLd8NLR0nv6vi0BYYKA==}

  json-stringify-pretty-compact@4.0.0:
    resolution: {integrity: sha512-3CNZ2DnrpByG9Nqj6Xo8vqbjT4F6N+tb4Gb28ESAZjYZ5yqvmc56J+/kuIwkaAMOyblTQhUW7PxMkUb8Q36N3Q==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsts@2.7.1:
    resolution: {integrity: sha512-x2wSZHEBK20CY+Wy+BPE7MrFQHW6sIsdaGUMEqmGAio+3gFzQaBYPwLRonUfQf9Ak8pBieqj9tUofX1+WtAEIg==}
    engines: {node: '>= 12'}

  jszip@3.10.1:
    resolution: {integrity: sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==}

  kdbush@4.0.2:
    resolution: {integrity: sha512-WbCVYJ27Sz8zi9Q7Q0xHC+05iwkm3Znipc2XTlrnJbsHMYktW4hPhXUE8Ys1engBrvffoSCqbil1JQAa7clRpA==}

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  ktx-parse@0.7.1:
    resolution: {integrity: sha512-FeA3g56ksdFNwjXJJsc1CCc7co+AJYDp6ipIp878zZ2bU8kWROatLYf39TQEd4/XRSUvBXovQ8gaVKWPXsCLEQ==}

  lie@3.3.0:
    resolution: {integrity: sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==}

  light-my-request@5.14.0:
    resolution: {integrity: sha512-aORPWntbpH5esaYpGOOmri0OHDOe3wC5M2MQxZ9dvMLZm6DnaAn0kJlcbU9hwsQgLzmZyReKwFwwPkR+nHu5kA==}

  lightningcss-darwin-arm64@1.30.1:
    resolution: {integrity: sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.30.1:
    resolution: {integrity: sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.30.1:
    resolution: {integrity: sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.30.1:
    resolution: {integrity: sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.30.1:
    resolution: {integrity: sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-arm64-musl@1.30.1:
    resolution: {integrity: sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  lightningcss-linux-x64-gnu@1.30.1:
    resolution: {integrity: sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-x64-musl@1.30.1:
    resolution: {integrity: sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  lightningcss-win32-arm64-msvc@1.30.1:
    resolution: {integrity: sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.30.1:
    resolution: {integrity: sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.30.1:
    resolution: {integrity: sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==}
    engines: {node: '>= 12.0.0'}

  lil-gui@0.17.0:
    resolution: {integrity: sha512-MVBHmgY+uEbmJNApAaPbtvNh1RCAeMnKym82SBjtp5rODTYKWtM+MXHCifLe2H2Ti1HuBGBtK/5SyG4ShQ3pUQ==}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  lit-element@4.2.1:
    resolution: {integrity: sha512-WGAWRGzirAgyphK2urmYOV72tlvnxw7YfyLDgQ+OZnM9vQQBQnumQ7jUJe6unEzwGU3ahFOjuz1iz1jjrpCPuw==}

  lit-html@3.3.1:
    resolution: {integrity: sha512-S9hbyDu/vs1qNrithiNyeyv64c9yqiW9l+DBgI18fL+MTvOtWoFR0FWiyq1TxaYef5wNlpEmzlXoBlZEO+WjoA==}

  lit@3.3.1:
    resolution: {integrity: sha512-Ksr/8L3PTapbdXJCk+EJVB78jDodUMaP54gD24W186zGRARvwrsPfS60wae/SSCTCNZVPd1chXqio1qHQmu4NA==}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash.clamp@4.0.3:
    resolution: {integrity: sha512-HvzRFWjtcguTW7yd8NJBshuNaCa8aqNFtnswdT7f/cMd/1YKy5Zzoq4W/Oxvnx9l7aeY258uSdDfM793+eLsVg==}

  lodash.omit@4.5.0:
    resolution: {integrity: sha512-XeqSp49hNGmlkj2EJlfrQFIzQ6lXdNro9sddtQzcJY8QaoC2GO0DT7xaIokHeyM+mIT0mPMlPvkYzg2xCuHdZg==}
    deprecated: This package is deprecated. Use destructuring assignment syntax instead.

  lodash.pick@4.4.0:
    resolution: {integrity: sha512-hXt6Ul/5yWjfklSGvLQl8vM//l3FtyHZeuelpzK6mm99pNvN9yTDruNZPEJZD1oWrqo+izBmB7oUfWgcCX7s4Q==}
    deprecated: This package is deprecated. Use destructuring assignment syntax instead.

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  long@3.2.0:
    resolution: {integrity: sha512-ZYvPPOMqUwPoDsbJaR10iQJYnMuZhRTvHYl62ErLIEX7RgFlziSBUUvrt3OVfc47QlHHpzPZYP17g3Fv7oeJkg==}
    engines: {node: '>=0.6'}

  long@5.3.2:
    resolution: {integrity: sha512-mNAgZ1GmyNhD7AuqnTG3/VQ26o760+ZYBPKjPvugO8+nLbYfX6TVpJPseBvopbdY+qpZ/lKUnmEc1LeZYS3QAA==}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==}

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lucide-react@0.292.0:
    resolution: {integrity: sha512-rRgUkpEHWpa5VCT66YscInCQmQuPCB1RFRzkkxMxg4b+jaL0V12E3riWWR2Sh5OIiUhCwGW/ZExuEO4Az32E6Q==}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0

  luxon@3.6.1:
    resolution: {integrity: sha512-tJLxrKJhO2ukZ5z0gyjY1zPh3Rh88Ej9P7jNrZiHMUXHae1yvI2imgOZtL1TO8TW6biMMKfTtAOoEJANgtWBMQ==}
    engines: {node: '>=12'}

  lz4js@0.2.0:
    resolution: {integrity: sha512-gY2Ia9Lm7Ep8qMiuGRhvUq0Q7qUereeldZPP1PMEJxPtEWHJLqw9pgX68oHajBH0nzJK4MaZEA/YNV3jT8u8Bg==}

  lzo-wasm@0.0.4:
    resolution: {integrity: sha512-VKlnoJRFrB8SdJhlVKvW5vI1gGwcZ+mvChEXcSX6r2xDNc/Q2FD9esfBmGCuPZdrJ1feO+YcVFd2PTk0c137Gw==}

  maath@0.9.0:
    resolution: {integrity: sha512-aAR8hoUqPxlsU8VOxkS9y37jhUzdUxM017NpCuxFU1Gk+nMaZASZxymZrV8LRSHzRk/watlbfyNKu6XPUhCFrQ==}
    peerDependencies:
      '@types/three': '>=0.144.0'
      three: '>=0.144.0'

  magic-string@0.30.17:
    resolution: {integrity: sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==}

  maplibre-gl@5.6.1:
    resolution: {integrity: sha512-TTSfoTaF7RqKUR9wR5qDxCHH2J1XfZ1E85luiLOx0h8r50T/LnwAwwfV0WVNh9o8dA7rwt57Ucivf1emyeukXg==}
    engines: {node: '>=16.14.0', npm: '>=8.1.0'}

  marchingsquares@1.3.3:
    resolution: {integrity: sha512-gz6nNQoVK7Lkh2pZulrT4qd4347S/toG9RXH2pyzhLgkL5mLkBoqgv4EvAGXcV0ikDW72n/OQb3Xe8bGagQZCg==}

  marked@15.0.12:
    resolution: {integrity: sha512-8dD6FusOQSrpv9Z1rdNMdlSgQOIP880DHqnohobOmYLElGEqAL/JvxvuxZO16r4HtjTlfPRDC1hbvxC9dPN2nA==}
    engines: {node: '>= 18'}
    hasBin: true

  md5@2.3.0:
    resolution: {integrity: sha512-T1GITYmFaKuO91vxyoQMFETst+O71VUPEU3ze5GNzDm0OWdP8v1ziTaAEPUr/3kLsY3Sftgz242A1SetQiDL7g==}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  meshline@3.3.1:
    resolution: {integrity: sha512-/TQj+JdZkeSUOl5Mk2J7eLcYTLiQm2IDzmlSvYm7ov15anEcDJ92GHqqazxTSreeNgfnYu24kiEvvv0WlbCdFQ==}
    peerDependencies:
      three: '>=0.137'

  mime-db@1.33.0:
    resolution: {integrity: sha512-BHJ/EKruNIqJf/QahvxwQZXKygOQ256myeN/Ew+THcAa5q+PjyTTMMeNQC4DZw5AwfvelsUrA6B67NKMqXDbzQ==}
    engines: {node: '>= 0.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-db@1.54.0:
    resolution: {integrity: sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.18:
    resolution: {integrity: sha512-lc/aahn+t4/SWV/qcmumYjymLsWfN3ELhpmVuUFjgsORruuZPVSwAQryq+HHGvO/SI2KVX26bx+En+zhM8g8hQ==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@3.0.0:
    resolution: {integrity: sha512-jSCU7/VB1loIWBZe14aEYHU/+1UMEHoaO7qxCOVJOw9GgH72VAWppxNcjU+x9a2k3GSIBXNKxXQFqRvvZ7vr3A==}
    engines: {node: '>=10.0.0'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mimic-response@3.1.0:
    resolution: {integrity: sha512-z0yWI+4FDrrweS8Zmt4Ej5HdJmky15+L2e6Wgn3+iK5fWzb6T3fhNFq2+MeTRb064c6Wr4N/wv0DzQTjNzHNGQ==}
    engines: {node: '>=10'}

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@3.0.2:
    resolution: {integrity: sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==}
    engines: {node: '>= 18'}

  mjolnir.js@3.0.0:
    resolution: {integrity: sha512-siX3YCG7N2HnmN1xMH3cK4JkUZJhbkhRFJL+G5N1vH0mh1t5088rJknIoqDFWDIU6NPGvRRgLnYW3ZHjSMEBLA==}

  mkdirp-classic@0.5.3:
    resolution: {integrity: sha512-gKLcREMhtuZRwRAfqP3RFW+TK4JqApVBtOIftVgjuABpAtpxhPGaDcfvbhNvD0B8iD1oUr/txX35NjcaY6Ns/A==}

  mkdirp@3.0.1:
    resolution: {integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==}
    engines: {node: '>=10'}
    hasBin: true

  mnemonist@0.39.6:
    resolution: {integrity: sha512-A/0v5Z59y63US00cRSLiloEIw3t5G+MiKz4BhX21FI+YBJXBOGW0ohFxTxO08dsOYlzxo87T7vGfZKYp2bcAWA==}

  mnemonist@0.39.8:
    resolution: {integrity: sha512-vyWo2K3fjrUw8YeeZ1zF0fy6Mu59RHokURlld8ymdUPjMlD9EC9ov1/YPqTgqRvUN9nTr3Gqfz29LYAmu0PHPQ==}

  moment-timezone@0.5.48:
    resolution: {integrity: sha512-f22b8LV1gbTO2ms2j2z13MuPogNoh5UzxL3nzNAYKGraILnbGc9NEE6dyiiiLv46DGRb8A4kg8UKWLjPthxBHw==}

  moment@2.30.1:
    resolution: {integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  murmurhash-js@1.0.0:
    resolution: {integrity: sha512-TvmkNhkv8yct0SVBSy+o8wYzXjE4Zz3PCesbfs8HiCXXdcTuocApFv11UWlNFWKYsP2okqrhb7JNlSm9InBhIw==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  napi-build-utils@2.0.0:
    resolution: {integrity: sha512-GEbrYkbfF7MoNaoh2iGG84Mnf/WZfB0GdGEsM8wz7Expx/LlWf5U8t9nvJKXSp3qr5IsEbK04cBGhol/KwOsWA==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==}

  node-abi@3.75.0:
    resolution: {integrity: sha512-OhYaY5sDsIka7H7AtijtI9jwGYLyl29eQn/W623DiN/MIv5sUqc4g7BIDThX+gb7di9f6xK02nkp8sdfFWZLTg==}
    engines: {node: '>=10'}

  node-domexception@1.0.0:
    resolution: {integrity: sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==}
    engines: {node: '>=10.5.0'}
    deprecated: Use your platform's native DOMException instead

  node-fetch@3.3.2:
    resolution: {integrity: sha512-dRB78srN/l6gqWulah9SrxeYnxeddIG30+GOqK/9OlLVyLg3HPnr6SqOWTWOXKRwC2eGYCkZ59NNuSgvSrpgOA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  obliterator@2.0.5:
    resolution: {integrity: sha512-42CPE9AhahZRsMNslczq0ctAEtqk8Eka26QofnqC346BZdHDySk3LWka23LI7ULIw11NmltpiLagIq8gBozxTw==}

  on-exit-leak-free@2.1.2:
    resolution: {integrity: sha512-0eJJY6hXLGf1udHwfNftBqH+g73EU4B504nZeKpz1sYRKafAghwxEJunB2O7rDZkL4PGfsMVnTXZ2EjibbqcsA==}
    engines: {node: '>=14.0.0'}

  on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  openapi-types@12.1.3:
    resolution: {integrity: sha512-N4YtSYJqghVu4iek2ZUvcN/0aqH1kRDuNqzcycDxhOUpg7GdvLa2F3DgS6yBNhInhv2r/6I0Flkn7CqL8+nIcw==}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}

  pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  parse5@7.3.0:
    resolution: {integrity: sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==}

  path-is-inside@1.0.2:
    resolution: {integrity: sha512-DUWJr3+ULp4zXmol/SZkFf3JGsS9/SIv+Y3Rt93/UjPpDpklB5f1er4O3POIbUuUJ3FXgqte2Q7SrU6zAqwk8w==}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-to-regexp@3.3.0:
    resolution: {integrity: sha512-qyCH421YQPS2WFDxDjftfc1ZR5WKQzVzqsp4n9M2kQhVOo/ByahFoUNJfl58kOcEGfQ//7weFTDhm+ss8Ecxgw==}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  pbf@3.3.0:
    resolution: {integrity: sha512-XDF38WCH3z5OV/OVa8GKUNtLAyneuzbCisx7QUCF8Q6Nutx0WnJrQe5O+kOtBlLfRNUws98Y58Lblp+NJG5T4Q==}
    hasBin: true

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@4.0.3:
    resolution: {integrity: sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q==}
    engines: {node: '>=12'}

  pino-abstract-transport@2.0.0:
    resolution: {integrity: sha512-F63x5tizV6WCh4R6RHyi2Ml+M70DNRXt/+HANowMflpgGFMAym/VKm6G7ZOQRjqN7XbGxK1Lg9t6ZrtzOaivMw==}

  pino-std-serializers@7.0.0:
    resolution: {integrity: sha512-e906FRY0+tV27iq4juKzSYPbUj2do2X2JX4EzSca1631EB2QJQUqGbDuERal7LCtOpxl6x3+nvo9NPZcmjkiFA==}

  pino@9.7.0:
    resolution: {integrity: sha512-vnMCM6xZTb1WDmLvtG2lE/2p+t9hDEIvTWJsu6FejkE62vB7gDhvzrpFR4Cw2to+9JNQxVnkAKVPA1KPB98vWg==}
    hasBin: true

  point-in-polygon-hao@1.2.4:
    resolution: {integrity: sha512-x2pcvXeqhRHlNRdhLs/tgFapAbSSe86wa/eqmj1G6pWftbEs5aVRJhRGM6FYSUERKu0PjekJzMq0gsI2XyiclQ==}

  point-in-polygon@1.1.0:
    resolution: {integrity: sha512-3ojrFwjnnw8Q9242TzgXuTD+eKiutbzyslcq1ydfu82Db2y+Ogbmyrkpv0Hgj31qwT3lbS9+QAAO/pIQM35XRw==}

  polyclip-ts@0.16.8:
    resolution: {integrity: sha512-JPtKbDRuPEuAjuTdhR62Gph7Is2BS1Szx69CFOO3g71lpJDFo78k4tFyi+qFOMVPePEzdSKkpGU3NBXPHHjvKQ==}

  postcss@8.5.6:
    resolution: {integrity: sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==}
    engines: {node: ^10 || ^12 || >=14}

  potpack@1.0.2:
    resolution: {integrity: sha512-choctRBIV9EMT9WGAZHn3V7t0Z2pMQyl0EZE6pFc/6ml3ssw7Dlf/oAOvFwjm1HVsqfQN8GfeFyJ+d8tRzqueQ==}

  potpack@2.1.0:
    resolution: {integrity: sha512-pcaShQc1Shq0y+E7GqJqvZj8DTthWV1KeHGdi0Z6IAin2Oi3JnLCOfwnCo84qc+HAp52wT9nK9H7FAJp5a44GQ==}

  preact@10.26.9:
    resolution: {integrity: sha512-SSjF9vcnF27mJK1XyFMNJzFd5u3pQiATFqoaDy03XuN00u4ziveVVEGt5RKJrDR8MHE/wJo9Nnad56RLzS2RMA==}

  prebuild-install@7.1.3:
    resolution: {integrity: sha512-8Mf2cbV7x1cXPUILADGI3wuhfqWvtiLA1iclTDbFRZkgRQS0NqsPZphna9V+HyTEadheuPmjaJMsbzKQFOzLug==}
    engines: {node: '>=10'}
    hasBin: true

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  process-warning@3.0.0:
    resolution: {integrity: sha512-mqn0kFRl0EoqhnL0GQ0veqFHyIN1yig9RHh/InzORTUiZHFRAur+aMtRkELNwGs9aNwKS6tg/An4NYBPGwvtzQ==}

  process-warning@5.0.0:
    resolution: {integrity: sha512-a39t9ApHNx2L4+HBnQKqxxHNs1r7KF+Intd8Q/g1bUh6q0WIp9voPXJ/x0j+ZL45KF1pJd9+q2jLIRMfvEshkA==}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  protocol-buffers-schema@3.6.0:
    resolution: {integrity: sha512-TdDRD+/QNdrCGCE7v8340QyuXd4kIWIgapsE2+n/SaGiSSbomYl4TjHlvIoCWRpE7wFt02EpB35VVA2ImcBVqw==}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  pump@3.0.3:
    resolution: {integrity: sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  quadbin@0.4.2:
    resolution: {integrity: sha512-1NFzjFVM23Um51/ttD6lFDqGtUHNS5Ky1slZHk3YPwMbC+7Jl3ULLb4QvDo6+Nerv8b8SgUV+ysOhziUh4B5cQ==}
    engines: {node: '>=18'}

  quick-format-unescaped@4.0.4:
    resolution: {integrity: sha512-tYC1Q1hgyRuHgloV/YXs2w15unPVh8qfu/qCTfhTYamaw7fyhumKa2yGpdSo87vY32rIclj+4fWYQXUMs9EHvg==}

  quickselect@1.1.1:
    resolution: {integrity: sha512-qN0Gqdw4c4KGPsBOQafj6yj/PA6c/L63f6CaZ/DCF/xF4Esu3jVmKLUDYxghFx8Kb/O7y9tI7x2RjTSXwdK1iQ==}

  quickselect@2.0.0:
    resolution: {integrity: sha512-RKJ22hX8mHe3Y6wH/N3wCM6BWtjaxIyyUIkpHOvfFnxdI4yD4tBXEBKSbriGujF6jnSVkJrffuo6vxACiSSxIw==}

  quickselect@3.0.0:
    resolution: {integrity: sha512-XdjUArbK4Bm5fLLvlm5KpTFOiOThgfWWI4axAZDWg4E/0mKdZyI9tNEfds27qCi1ze/vwTR16kvmmGhRra3c2g==}

  range-parser@1.2.0:
    resolution: {integrity: sha512-kA5WQoNVo4t9lNx2kQNFCxKeBl5IbbSNBl1M/tLkw9WCn+hxNBAW5Qh8gdhs63CJnhjJ2zQWFoqPJP2sK1AV5A==}
    engines: {node: '>= 0.6'}

  rbush@2.0.2:
    resolution: {integrity: sha512-XBOuALcTm+O/H8G90b6pzu6nX6v2zCKiFG4BJho8a+bY6AER6t8uQUZdi5bomQc0AprCWhEGa7ncAbbRap0bRA==}

  rbush@3.0.1:
    resolution: {integrity: sha512-XRaVO0YecOpEuIvbhbpTrZgoiI6xBlz6hnlr6EHhd+0x9ase6EmeN+hdwwUaJvLcsFFQ8iWVF1GAK1yB0BWi0w==}

  rc@1.2.8:
    resolution: {integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==}
    hasBin: true

  react-composer@5.0.3:
    resolution: {integrity: sha512-1uWd07EME6XZvMfapwZmc7NgCZqDemcvicRi3wMJzXsQLvZ3L7fTHVyPy1bZdnWXM4iPjYuNE+uJ41MLKeTtnA==}
    peerDependencies:
      react: ^15.0.0 || ^16.0.0 || ^17.0.0 || ^18.0.0

  react-dnd-html5-backend@16.0.1:
    resolution: {integrity: sha512-Wu3dw5aDJmOGw8WjH1I1/yTH+vlXEL4vmjk5p+MHxP8HuHJS1lAGeIdG/hze1AvNeXWo/JgULV87LyQOr+r5jw==}

  react-dnd@16.0.1:
    resolution: {integrity: sha512-QeoM/i73HHu2XF9aKksIUuamHPDvRglEwdHL4jsp784BgUuWcg6mzfxT0QDdQz8Wj0qyRKx2eMg8iZtWvU4E2Q==}
    peerDependencies:
      '@types/hoist-non-react-statics': '>= 3.3.1'
      '@types/node': '>= 12'
      '@types/react': '>= 16'
      react: '>= 16.14'
    peerDependenciesMeta:
      '@types/hoist-non-react-statics':
        optional: true
      '@types/node':
        optional: true
      '@types/react':
        optional: true

  react-dom@18.3.1:
    resolution: {integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==}
    peerDependencies:
      react: ^18.3.1

  react-hook-form@7.61.1:
    resolution: {integrity: sha512-2vbXUFDYgqEgM2RcXcAT2PwDW/80QARi+PKmHy5q2KhuKvOlG8iIYgf7eIlIANR5trW9fJbP4r5aub3a4egsew==}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18 || ^19

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-map-gl@8.0.4:
    resolution: {integrity: sha512-SHdpvFIvswsZBg6BCPcwY/nbKuCo3sJM1Cj7Sd+gA3gFRFOixD+KtZ2XSuUWq2WySL2emYEXEgrLZoXsV4Ut4Q==}
    peerDependencies:
      mapbox-gl: '>=1.13.0'
      maplibre-gl: '>=1.13.0'
      react: '>=16.3.0'
      react-dom: '>=16.3.0'
    peerDependenciesMeta:
      mapbox-gl:
        optional: true
      maplibre-gl:
        optional: true

  react-merge-refs@1.1.0:
    resolution: {integrity: sha512-alTKsjEL0dKH/ru1Iyn7vliS2QRcBp9zZPGoWxUOvRGWPUYgjo+V01is7p04It6KhgrzhJGnIj9GgX8W4bZoCQ==}

  react-reconciler@0.27.0:
    resolution: {integrity: sha512-HmMDKciQjYmBRGuuhIaKA1ba/7a+UsM5FzOZsMO2JYHt9Jh8reCb7j1eDC95NOyUlKM9KRyvdx0flBuDvYSBoA==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      react: ^18.0.0

  react-refresh@0.17.0:
    resolution: {integrity: sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==}
    engines: {node: '>=0.10.0'}

  react-router-dom@6.30.1:
    resolution: {integrity: sha512-llKsgOkZdbPU1Eg3zK8lCn+sjD9wMRZZPuzmdWWX5SUs8OFkN5HnFVC0u5KMeMaC9aoancFI/KoLuKPqN+hxHw==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  react-router@6.30.1:
    resolution: {integrity: sha512-X1m21aEmxGXqENEPG3T6u0Th7g0aS4ZmoNynhbs+Cn+q+QGTLt+d5IQ2bHAXKzKcxGJjxACpVbnYQSCRcfxHlQ==}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'

  react-use-measure@2.1.7:
    resolution: {integrity: sha512-KrvcAo13I/60HpwGO5jpW7E9DfusKyLPLvuHlUyP5zqnmAPhNc6qTRjUQrdTADl0lpPpDVU2/Gg51UlOGHXbdg==}
    peerDependencies:
      react: '>=16.13'
      react-dom: '>=16.13'
    peerDependenciesMeta:
      react-dom:
        optional: true

  react@18.3.1:
    resolution: {integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==}
    engines: {node: '>=0.10.0'}

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  real-require@0.2.0:
    resolution: {integrity: sha512-57frrGM/OCTLqLOAh0mhVA9VBMHd+9U7Zb2THMGdBUoZVOtGbJzjxsYGDJ3A9AYYCP4hn6y1TVbaOfzWtm5GFg==}
    engines: {node: '>= 12.13.0'}

  redux@4.2.1:
    resolution: {integrity: sha512-LAUYz4lc+Do8/g7aeRa8JkyDErK6ekstQaqWQrNRW//MY1TvCEpMtpTWvlQ+FPbWCx+Xixu/6SHt5N0HR+SB4w==}

  registry-auth-token@3.3.2:
    resolution: {integrity: sha512-JL39c60XlzCVgNrO+qq68FoNb56w/m7JYvGR2jT5iR1xBrUA3Mfx5Twk5rqTThPmQKMWydGmq8oFtDlxfrmxnQ==}

  registry-url@3.1.0:
    resolution: {integrity: sha512-ZbgR5aZEdf4UKZVBPYIgaglBmSF2Hi94s2PcIHhRGFjKYu+chjJdYfHn4rt3hB6eCKLJ8giVIIfgMa1ehDfZKA==}
    engines: {node: '>=0.10.0'}

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-pkg-maps@1.0.0:
    resolution: {integrity: sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==}

  resolve-protobuf-schema@2.1.0:
    resolution: {integrity: sha512-kI5ffTiZWmJaS/huM8wZfEMer1eRd7oJQhDuxeCLe3t7N7mX3z94CN0xPxBQxFYQTSNz9T0i+v6inKqSdK8xrQ==}

  ret@0.4.3:
    resolution: {integrity: sha512-0f4Memo5QP7WQyUEAYUO3esD/XjOc3Zjjg5CPsAq1p8sIu0XPeMbHJemKA0BO7tV0X7+A0FoEpbmHXWxPyD3wQ==}
    engines: {node: '>=10'}

  reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha512-q1b3N5QkRUWUl7iyylaaj3kOpIT0N2i9MqIEQXP73GVsN9cw3fdx8X63cEmWhJGi2PPCF23Ijp7ktmd39rawIA==}

  robust-predicates@2.0.4:
    resolution: {integrity: sha512-l4NwboJM74Ilm4VKfbAtFeGq7aEjWL+5kVFcmgFA2MrdnQWx9iE/tUGvxY5HyMI7o/WpSIUFLbC5fbeaHgSCYg==}

  robust-predicates@3.0.2:
    resolution: {integrity: sha512-IXgzBWvWQwE6PrDI05OvmXUIruQTcoMDzRsOd5CDvHCVLcLHMTSYvOK5Cm46kWqlV3yAbuSpBZdJ5oP5OUoStg==}

  rollup@4.45.1:
    resolution: {integrity: sha512-4iya7Jb76fVpQyLoiVpzUrsjQ12r3dM7fIVz+4NwoYvZOShknRmiv+iu9CClZml5ZLGb0XMcYLutK6w9tgxHDw==}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rw@1.3.3:
    resolution: {integrity: sha512-PdhdWy89SiZogBLaw42zdeqtRJ//zFd2PgQavcICDUgJT5oW10QCRKbJ6bg4r0/UY2M6BWd5tkxuGFRvCkgfHQ==}

  rxjs@7.8.2:
    resolution: {integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-regex2@3.1.0:
    resolution: {integrity: sha512-RAAZAGbap2kBfbVhvmnTFv73NWLMvDGOITFYTZBAaY8eR+Ir4ef7Up/e7amo+y1+AH+3PtLkrt9mvcTsG9LXug==}

  safe-stable-stringify@2.5.0:
    resolution: {integrity: sha512-b3rppTKm9T+PsVCBEOUR46GWI7fdOs00VKZ1+9c1EWDaDMvjQc6tUwuFyIprgGgTcWoVHSKrU8H31ZHA2e0RHA==}
    engines: {node: '>=10'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  scheduler@0.21.0:
    resolution: {integrity: sha512-1r87x5fz9MXqswA2ERLo0EbOAU74DpIUO090gIasYTqlVoJeMcl+Z1Rg7WHz+qtPujhS/hGIt9kxZOYBV3faRQ==}

  scheduler@0.23.2:
    resolution: {integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==}

  secure-json-parse@2.7.0:
    resolution: {integrity: sha512-6aU+Rwsezw7VR8/nyvKTx8QpWH9FrcYiXXlqC4z5d5XQBDRqtbfsRjnwGyqbi3gddNtWHuEk9OANUotL26qKUw==}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  serve-handler@6.1.6:
    resolution: {integrity: sha512-x5RL9Y2p5+Sh3D38Fh9i/iQ5ZK+e4xuXRd/pGbM4D13tgo/MGwbttUk8emytcr1YYzBYs+apnUngBDFYfpjPuQ==}

  serve@14.2.4:
    resolution: {integrity: sha512-qy1S34PJ/fcY8gjVGszDB3EXiPSk5FKhUa7tQe0UPRddxRidc2V6cNHPNewbE1D7MAkgLuWEt3Vw56vYy73tzQ==}
    engines: {node: '>= 14'}
    hasBin: true

  set-cookie-parser@2.7.1:
    resolution: {integrity: sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ==}

  set-value@2.0.1:
    resolution: {integrity: sha512-JxHc1weCN68wRY0fhCoXpyK55m/XPHafOmK4UWD7m2CI14GMcFypt4w/0+NV5f/ZMby2F6S2wwA7fgynh9gWSw==}
    engines: {node: '>=0.10.0'}

  setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shell-quote@1.8.3:
    resolution: {integrity: sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  simple-concat@1.0.1:
    resolution: {integrity: sha512-cSFtAPtRhljv69IK0hTVZQ+OfE9nePi/rtJmw5UjHeVyVroEqJXP1sFztKUy1qU+xvz3u/sfYJLa947b7nAN2Q==}

  simple-get@4.0.1:
    resolution: {integrity: sha512-brv7p5WgH0jmQJr1ZDDfKDOSeWWg+OVypG99A/5vYGPqJ6pxiaHLy8nxtFjBA7oMa01ebA9gfh1uMCFqOuXxvA==}

  size-sensor@1.0.2:
    resolution: {integrity: sha512-2NCmWxY7A9pYKGXNBfteo4hy14gWu47rg5692peVMst6lQLPKrVjhY+UTEsPI5ceFRJSl3gVgMYaUi/hKuaiKw==}

  skmeans@0.9.7:
    resolution: {integrity: sha512-hNj1/oZ7ygsfmPZ7ZfN5MUBRoGg1gtpnImuJBgLO0ljQ67DtJuiQaiYdS4lUA6s0KCwnPhGivtC/WRwIZLkHyg==}

  snake-case@3.0.4:
    resolution: {integrity: sha512-LAOh4z89bGQvl9pFfNF8V146i7o7/CqFPbqzYgP+yYzDIDeS9HaNFtXABamRW+AQzEVODcvE79ljJ+8a9YSdMg==}

  snappyjs@0.6.1:
    resolution: {integrity: sha512-YIK6I2lsH072UE0aOFxxY1dPDCS43I5ktqHpeAsuLNYWkE5pGxRGWfDM4/vSUfNzXjC1Ivzt3qx31PCLmc9yqg==}

  sonic-boom@4.2.0:
    resolution: {integrity: sha512-INb7TM37/mAcsGmc9hyyI6+QR3rR1zVRu36B0NeGXKnOOLiZOfER5SA+N7X7k3yUYRzLWafduTDvJAfDswwEww==}

  sort-asc@0.2.0:
    resolution: {integrity: sha512-umMGhjPeHAI6YjABoSTrFp2zaBtXBej1a0yKkuMUyjjqu6FJsTF+JYwCswWDg+zJfk/5npWUUbd33HH/WLzpaA==}
    engines: {node: '>=0.10.0'}

  sort-desc@0.2.0:
    resolution: {integrity: sha512-NqZqyvL4VPW+RAxxXnB8gvE1kyikh8+pR+T+CXLksVRN9eiQqkQlPwqWYU0mF9Jm7UnctShlxLyAt1CaBOTL1w==}
    engines: {node: '>=0.10.0'}

  sort-object@3.0.3:
    resolution: {integrity: sha512-nK7WOY8jik6zaG9CRwZTaD5O7ETWDLZYMM12pqY8htll+7dYeqGfEUPcUBHOpSJg2vJOrvFIY2Dl5cX2ih1hAQ==}
    engines: {node: '>=0.10.0'}

  sortablejs@1.15.6:
    resolution: {integrity: sha512-aNfiuwMEpfBM/CN6LY0ibyhxPfPbyFeBTYJKCvzkJ2GkUpazIt3H+QIPAMHwqQ7tMKaHz1Qj+rJJCqljnf4p3A==}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  spawn-command@0.0.2:
    resolution: {integrity: sha512-zC8zGoGkmc8J9ndvml8Xksr1Amk9qBujgbF0JAIWO7kXr43w0h/0GJNM/Vustixu+YE8N/MTrQ7N31FvHUACxQ==}

  splaytree-ts@1.0.2:
    resolution: {integrity: sha512-0kGecIZNIReCSiznK3uheYB8sbstLjCZLiwcQwbmLhgHJj2gz6OnSPkVzJQCMnmEz1BQ4gPK59ylhBoEWOhGNA==}

  split-string@3.1.0:
    resolution: {integrity: sha512-NzNVhJDYpwceVVii8/Hu6DKfD2G+NrQHlS/V/qgv763EYudVwEcMQNxd2lh+0VrUByXN/oJkl5grOhYWvQUYiw==}
    engines: {node: '>=0.10.0'}

  split2@4.2.0:
    resolution: {integrity: sha512-UcjcJOWknrNkF6PLX83qcHM6KHgVKNkV62Y8a5uYDVv9ydGQVwAHMKqHdJje1VTWpljG0WYpCDhrCdAOYH4TWg==}
    engines: {node: '>= 10.x'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  standardized-audio-context@25.3.77:
    resolution: {integrity: sha512-Ki9zNz6pKcC5Pi+QPjPyVsD9GwJIJWgryji0XL9cAJXMGyn+dPOf6Qik1AHei0+UNVcc4BOCa0hWLBzlwqsW/A==}

  stats-gl@1.0.7:
    resolution: {integrity: sha512-vZI82CjefSxLC1bjw36z28v0+QE9rJKymGlXtfWu+ipW70ZEAwa4EbO4LxluAfLfpqiaAS04NzpYBRLDeAwYWQ==}

  stats.js@0.17.0:
    resolution: {integrity: sha512-hNKz8phvYLPEcRkeG1rsGmV5ChMjKDAWU7/OJJdDErPBNChQXxCo3WZurGpnWc6gZhAzEPFad1aVgyOANH1sMw==}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  steed@1.1.3:
    resolution: {integrity: sha512-EUkci0FAUiE4IvGTSKcDJIQ/eRUP2JJb56+fvZ4sdnguLTqIdKjSxUe138poW8mkvKWXW2sFPrgTsxqoISnmoA==}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-json-comments@2.0.1:
    resolution: {integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==}
    engines: {node: '>=0.10.0'}

  strnum@1.1.2:
    resolution: {integrity: sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==}

  supercluster@8.0.1:
    resolution: {integrity: sha512-IiOea5kJ9iqzD2t7QJq/cREyLHTtSmUT6gQsweojg9WH2sYJqZK9SswTu6jrscO6D1G5v5vYZ9ru/eq85lXeZQ==}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  suspend-react@0.1.3:
    resolution: {integrity: sha512-aqldKgX9aZqpoDp3e8/BZ8Dm7x1pJl+qI3ZKxDN0i/IQTWUwBx/ManmlVJ3wowqbno6c2bmiIfs+Um6LbsjJyQ==}
    peerDependencies:
      react: '>=17.0'

  svg-parser@2.0.4:
    resolution: {integrity: sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ==}

  sweepline-intersections@1.5.0:
    resolution: {integrity: sha512-AoVmx72QHpKtItPu72TzFL+kcYjd67BPLDoR0LarIk+xyaRg+pDTMFXndIEvZf9xEKnJv6JdhgRMnocoG0D3AQ==}

  tabbable@6.2.0:
    resolution: {integrity: sha512-Cat63mxsVJlzYvN51JmVXIgNoUokrIaT2zLclCXjRd8boZ0004U4KCs/sToJ75C6sdlByWxpYnb5Boif1VSFew==}

  tailwindcss@4.1.11:
    resolution: {integrity: sha512-2E9TBm6MDD/xKYe+dvJZAmg3yxIEDNRc0jwlNyDg/4Fil2QcSLjFKGVff0lAf1jjeaArlG/M75Ey/EYr/OJtBA==}

  tapable@2.2.2:
    resolution: {integrity: sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg==}
    engines: {node: '>=6'}

  tar-fs@2.1.3:
    resolution: {integrity: sha512-090nwYJDmlhwFwEW3QQl+vaNnxsO2yVsd45eTKRBzSzu+hlb1w2K9inVq5b0ngXuLVqQ4ApvsUHHnu/zQNkWAg==}

  tar-stream@2.2.0:
    resolution: {integrity: sha512-ujeqbceABgwMZxEJnk2HDY2DlnUZ+9oEcb1KzTVfYHio0UE6dG71n60d8D2I4qNvleWrrXpmjpt7vZeF1LnMZQ==}
    engines: {node: '>=6'}

  tar@7.4.3:
    resolution: {integrity: sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==}
    engines: {node: '>=18'}

  terra-draw-maplibre-gl-adapter@1.1.1:
    resolution: {integrity: sha512-PkrhUO2sVuc8dviOzzfKyEAuToEKcy1UWOQ/EtdNXx+dPAw8WuCXOhXJpf+M1yi02z+v6mRMq/7h8sRs2fBeng==}
    peerDependencies:
      maplibre-gl: '>=4'
      terra-draw: ^1.0.0

  terra-draw@1.10.0:
    resolution: {integrity: sha512-7lM6AmNxKWsrls/SZ+qoVTqD74NBrwq0a4fMBvKcvHmteJ72FUluujn72s4nkASbeS4X199Hn+7KI9uiBiKlVQ==}

  texture-compressor@1.0.2:
    resolution: {integrity: sha512-dStVgoaQ11mA5htJ+RzZ51ZxIZqNOgWKAIvtjLrW1AliQQLCmrDqNzQZ8Jh91YealQ95DXt4MEduLzJmbs6lig==}
    hasBin: true

  thread-stream@3.1.0:
    resolution: {integrity: sha512-OqyPZ9u96VohAyMfJykzmivOrY2wfMSf3C5TtFJVgN+Hm6aj+voFhlK+kZEIv2FBh1X6Xp3DlnCOfEQ3B2J86A==}

  three-mesh-bvh@0.6.8:
    resolution: {integrity: sha512-EGebF9DZx1S8+7OZYNNTT80GXJZVf+UYXD/HyTg/e2kR/ApofIFfUS4ZzIHNnUVIadpnLSzM4n96wX+l7GMbnQ==}
    peerDependencies:
      three: '>= 0.151.0'

  three-stdlib@2.36.0:
    resolution: {integrity: sha512-kv0Byb++AXztEGsULgMAs8U2jgUdz6HPpAB/wDJnLiLlaWQX2APHhiTJIN7rqW+Of0eRgcp7jn05U1BsCP3xBA==}
    peerDependencies:
      three: '>=0.128.0'

  three@0.152.0:
    resolution: {integrity: sha512-uvKoYo4b2bnqzsR4RJFuWecxwMKcgT1nFNmiWooCNr6AxZLCtfkj/xcfFgoi5mFopSVorh7bnvTHPfeW8DINGg==}

  timezone-groups@0.10.4:
    resolution: {integrity: sha512-AnkJYrbb7uPkDCEqGeVJiawZNiwVlSkkeX4jZg1gTEguClhyX+/Ezn07KB6DT29tG3UN418ldmS/W6KqGOTDjg==}
    engines: {node: '>=18.12.0'}

  tinyglobby@0.2.14:
    resolution: {integrity: sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==}
    engines: {node: '>=12.0.0'}

  tinyqueue@2.0.3:
    resolution: {integrity: sha512-ppJZNDuKGgxzkHihX8v9v9G5f+18gzaTfrukGrq6ueg0lmH4nqVnA2IPG0AEH3jKEk2GRJCUhDoqpoiw3PHLBA==}

  tinyqueue@3.0.0:
    resolution: {integrity: sha512-gRa9gwYU3ECmQYv3lslts5hxuIa90veaEcxDYuu3QGOIAEM2mOZkVHp48ANJuu1CURtRdHKUBY5Lm1tHV+sD4g==}

  toad-cache@3.7.0:
    resolution: {integrity: sha512-/m8M+2BJUpoJdgAHoG+baCwBT+tf2VraSfkBgl0Y00qIWt41DJ8R5B8nsEw0I58YwF5IZH6z24/2TobDKnqSWw==}
    engines: {node: '>=12'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  tone@15.1.22:
    resolution: {integrity: sha512-TCScAGD4sLsama5DjvTUXlLDXSqPealhL64nsdV1hhr6frPWve0DeSo63AKnSJwgfg55fhvxj0iPPRwPN5o0ag==}

  topojson-client@3.1.0:
    resolution: {integrity: sha512-605uxS6bcYxGXw9qi62XyrV6Q3xwbndjachmNxu8HWTtVPxZfEJN9fd/SZS1Q54Sn2y0TMyMxFj/cJINqGHrKw==}
    hasBin: true

  topojson-server@3.0.1:
    resolution: {integrity: sha512-/VS9j/ffKr2XAOjlZ9CgyyeLmgJ9dMwq6Y0YEON8O7p/tGGk+dCWnrE03zEdu7i4L7YsFZLEPZPzCvcB7lEEXw==}
    hasBin: true

  tree-kill@1.2.2:
    resolution: {integrity: sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==}
    hasBin: true

  troika-three-text@0.47.2:
    resolution: {integrity: sha512-qylT0F+U7xGs+/PEf3ujBdJMYWbn0Qci0kLqI5BJG2kW1wdg4T1XSxneypnF05DxFqJhEzuaOR9S2SjiyknMng==}
    peerDependencies:
      three: '>=0.125.0'

  troika-three-utils@0.47.2:
    resolution: {integrity: sha512-/28plhCxfKtH7MSxEGx8e3b/OXU5A0xlwl+Sbdp0H8FXUHKZDoksduEKmjQayXYtxAyuUiCRunYIv/8Vi7aiyg==}
    peerDependencies:
      three: '>=0.125.0'

  troika-worker-utils@0.47.2:
    resolution: {integrity: sha512-mzss4MeyzUkYBppn4x5cdAqrhBHFEuVmMMgLMTyFV23x6GvQMyo+/R5E5Lsbrt7WSt5RfvewjcwD1DChRTA9lA==}

  tslib@2.3.0:
    resolution: {integrity: sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tsx@4.20.3:
    resolution: {integrity: sha512-qjbnuR9Tr+FJOMBqJCW5ehvIo/buZq7vH7qD7JziU98h6l3qGy0a/yPFjwO+y0/T7GFpNgNAvEcPPVfyT8rrPQ==}
    engines: {node: '>=18.0.0'}
    hasBin: true

  tunnel-agent@0.6.0:
    resolution: {integrity: sha512-McnNiV1l8RYeY8tBgEpuodCC1mLUdbSN+CYBL7kJsJNInOP8UjDDEwdk6Mw60vdLLrr5NHKZhMAOSrR2NZuQ+w==}

  type-fest@2.19.0:
    resolution: {integrity: sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==}
    engines: {node: '>=12.20'}

  type-fest@4.41.0:
    resolution: {integrity: sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==}
    engines: {node: '>=16'}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  typewise-core@1.2.0:
    resolution: {integrity: sha512-2SCC/WLzj2SbUwzFOzqMCkz5amXLlxtJqDKTICqg30x+2DZxcfZN2MvQZmGfXWKNWaKK9pBPsvkcwv8bF/gxKg==}

  typewise@1.0.3:
    resolution: {integrity: sha512-aXofE06xGhaQSPzt8hlTY+/YWQhm9P0jYUp1f2XtmW/3Bk0qzXcyFWAtPoo2uTGQj1ZwbDuSyuxicq+aDo8lCQ==}

  undici-types@6.21.0:
    resolution: {integrity: sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==}

  union-value@1.0.1:
    resolution: {integrity: sha512-tJfXmxMeWYnczCVs7XAEvIV7ieppALdyepWMkHkwciRpZraG/xwT+s2JN8+pr1+8jCRf80FFzvr+MpQeeoF4Xg==}
    engines: {node: '>=0.10.0'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  update-check@1.5.4:
    resolution: {integrity: sha512-5YHsflzHP4t1G+8WGPlvKbJEbAJGCgw+Em+dGR1KmBUbr1J36SJBqlHLjR7oob7sco5hWHGQVcr9B2poIVDDTQ==}

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  utility-types@3.11.0:
    resolution: {integrity: sha512-6Z7Ma2aVEWisaL6TvBCy7P8rm2LQoPv6dJ7ecIaIixHcwfbJ0x7mWdbcwlIM5IGQxPZSFYeqRCqlOOeKoJYMkw==}
    engines: {node: '>= 4'}

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  vite-plugin-svgr@4.3.0:
    resolution: {integrity: sha512-Jy9qLB2/PyWklpYy0xk0UU3TlU0t2UMpJXZvf+hWII1lAmRHrOUKi11Uw8N3rxoNk7atZNYO3pR3vI1f7oi+6w==}
    peerDependencies:
      vite: '>=2.6.0'

  vite@7.0.6:
    resolution: {integrity: sha512-MHFiOENNBd+Bd9uvc8GEsIzdkn1JxMmEeYX35tI3fv0sJBUTfW5tQsoaOwuY4KhBI09A3dUJ/DXf2yxPVPUceg==}
    engines: {node: ^20.19.0 || >=22.12.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^20.19.0 || >=22.12.0
      jiti: '>=1.21.0'
      less: ^4.0.0
      lightningcss: ^1.21.0
      sass: ^1.70.0
      sass-embedded: ^1.70.0
      stylus: '>=0.54.8'
      sugarss: ^5.0.0
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vt-pbf@3.1.3:
    resolution: {integrity: sha512-2LzDFzt0mZKZ9IpVF2r69G9bXaP2Q2sArJCmcCgvfTdCCZzSyz4aCLoQyUilu37Ll56tCblIZrXFIjNUpGIlmA==}

  web-streams-polyfill@3.3.3:
    resolution: {integrity: sha512-d2JWLCivmZYTSIoge9MsgFCZrt571BikcWGYkjC1khllbTeDlGqZ2D8vD8E/lJa8WGWbb7Plm8/XJYV7IJHZZw==}
    engines: {node: '>= 8'}

  webgl-constants@1.1.1:
    resolution: {integrity: sha512-LkBXKjU5r9vAW7Gcu3T5u+5cvSvh5WwINdr0C+9jpzVB41cjQAP5ePArDtk/WHYdVj0GefCgM73BA7FlIiNtdg==}

  webgl-sdf-generator@1.1.1:
    resolution: {integrity: sha512-9Z0JcMTFxeE+b2x1LJTdnaT8rT8aEp7MVxkNwoycNmJWwPdzoXzMh0BjJSh/AEFP+KPYZUli814h8bJZFIZ2jA==}

  wgsl_reflect@1.2.3:
    resolution: {integrity: sha512-BQWBIsOn411M+ffBxmA6QRLvAOVbuz3Uk4NusxnqC1H7aeQcVLhdA3k2k/EFFFtqVjhz3z7JOOZF1a9hj2tv4Q==}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  which@4.0.0:
    resolution: {integrity: sha512-GlaYyEb07DPxYCKhKzplCWBJtvxZcZMrL+4UkrTSJHHPyZU4mYYTv3qaOe77H7EODLSSopAUFAc6W8U4yqvscg==}
    engines: {node: ^16.13.0 || >=18.0.0}
    hasBin: true

  widest-line@4.0.1:
    resolution: {integrity: sha512-o0cyEG0e8GPzT4iGHphIOh0cJOV8fivsXxddQasHPHfoZf1ZexrfeA21w2NaEN1RHE+fXlfISmOE8R9N3u3Qig==}
    engines: {node: '>=12'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  xss@1.0.13:
    resolution: {integrity: sha512-clu7dxTm1e8Mo5fz3n/oW3UCXBfV89xZ72jM8yzo1vR/pIS0w3sgB3XV2H8Vm6zfGnHL0FzvLJPJEBhd86/z4Q==}
    engines: {node: '>= 0.10.0'}
    hasBin: true

  xtend@4.0.2:
    resolution: {integrity: sha512-LKYU1iAXJXUgAXn9URjiu+MWhyUXHsvfp7mcuYm9dSUKK0/CjtrUwFAxD82/mCWbtLsGjFIad0wIsod4zrTAEQ==}
    engines: {node: '>=0.4'}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@5.0.0:
    resolution: {integrity: sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==}
    engines: {node: '>=18'}

  yaml@2.8.0:
    resolution: {integrity: sha512-4lLa/EcQCB0cJkyts+FpIRx5G/llPxfP6VQU5KByHEhLxY3IJCH0f0Hy1MHI8sClTvsIb8qwRJ6R/ZdlDJ/leQ==}
    engines: {node: '>= 14.6'}
    hasBin: true

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  zod@3.25.76:
    resolution: {integrity: sha512-gzUt/qt81nXsFGKIFcC3YnfEAx5NkunCfnDlvuBSSFS02bcXu4Lmea0AFIUwbLWxWPx3d9p8S5QoaujKcNQxcQ==}

  zrender@5.6.1:
    resolution: {integrity: sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==}

  zstd-codec@0.1.5:
    resolution: {integrity: sha512-v3fyjpK8S/dpY/X5WxqTK3IoCnp/ZOLxn144GZVlNUjtwAchzrVo03h+oMATFhCIiJ5KTr4V3vDQQYz4RU684g==}

  zustand@3.7.2:
    resolution: {integrity: sha512-PIJDIZKtokhof+9+60cpockVOq05sJzHCriyvaLBmEJixseQ1a5Kdov6fWZfWOu5SK9c+FhH1jU0tntLxRJYMA==}
    engines: {node: '>=12.7.0'}
    peerDependencies:
      react: '>=16.8'
    peerDependenciesMeta:
      react:
        optional: true

  zustand@5.0.6:
    resolution: {integrity: sha512-ihAqNeUVhe0MAD+X8M5UzqyZ9k3FFZLBTtqo6JLPwV53cbRB/mJwBI0PxcIgqhBBHlEs8G45OTDTMq3gNcLq3A==}
    engines: {node: '>=12.20.0'}
    peerDependencies:
      '@types/react': '>=18.0.0'
      immer: '>=9.0.6'
      react: '>=18.0.0'
      use-sync-external-store: '>=1.2.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
      use-sync-external-store:
        optional: true

snapshots:

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29

  '@arcgis/components-utils@4.33.11':
    dependencies:
      tslib: 2.8.1

  '@arcgis/core@4.33.9':
    dependencies:
      '@esri/arcgis-html-sanitizer': 4.1.0
      '@esri/calcite-components': 3.2.1
      '@vaadin/grid': 24.7.11
      '@zip.js/zip.js': 2.7.68
      luxon: 3.6.1
      marked: 15.0.12

  '@arcgis/lumina@4.33.11':
    dependencies:
      '@arcgis/components-utils': 4.33.11
      '@lit-labs/ssr': 3.3.1
      '@lit-labs/ssr-client': 1.1.7
      '@lit/context': 1.1.6
      csstype: 3.1.3
      lit: 3.3.1
      tslib: 2.8.1

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.28.0': {}

  '@babel/core@7.28.0':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.3(@babel/core@7.28.0)
      '@babel/helpers': 7.28.2
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.2
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.28.0':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.2
      '@jridgewell/gen-mapping': 0.3.12
      '@jridgewell/trace-mapping': 0.3.29
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.28.0
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.25.1
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-globals@7.28.0': {}

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.28.0
      '@babel/types': 7.28.2
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.28.0
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.28.2':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.28.2

  '@babel/parser@7.28.0':
    dependencies:
      '@babel/types': 7.28.2

  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/runtime@7.28.2': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.2

  '@babel/traverse@7.28.0':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.28.0
      '@babel/helper-globals': 7.28.0
      '@babel/parser': 7.28.0
      '@babel/template': 7.27.2
      '@babel/types': 7.28.2
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.28.2':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@carto/api-client@0.4.9(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@turf/bbox-clip': 7.2.0
      '@turf/bbox-polygon': 7.2.0
      '@turf/boolean-equal': 7.2.0
      '@turf/boolean-intersects': 7.2.0
      '@turf/boolean-within': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/intersect': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/union': 7.2.0
      '@types/geojson': 7946.0.16
      d3-scale: 4.0.2
      h3-js: 4.1.0
    transitivePeerDependencies:
      - '@loaders.gl/core'

  '@deck.gl/aggregation-layers@9.1.13(@deck.gl/core@9.1.13)(@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@deck.gl/layers': 9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@luma.gl/constants': 9.1.9
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/core': 4.1.0
      '@math.gl/web-mercator': 4.1.0
      d3-hexbin: 0.2.2

  '@deck.gl/arcgis@9.1.13(@arcgis/core@4.33.9)(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))(@luma.gl/webgl@9.1.9(@luma.gl/core@9.1.9))':
    dependencies:
      '@arcgis/core': 4.33.9
      '@deck.gl/core': 9.1.13
      '@luma.gl/constants': 9.1.9
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/webgl': 9.1.9(@luma.gl/core@9.1.9)
      esri-loader: 3.7.0

  '@deck.gl/carto@9.1.13(yduymzwkmjlkqajmgfv23a5xca)':
    dependencies:
      '@carto/api-client': 0.4.9(@loaders.gl/core@4.3.4)
      '@deck.gl/aggregation-layers': 9.1.13(@deck.gl/core@9.1.13)(@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/core': 9.1.13
      '@deck.gl/extensions': 9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/geo-layers': 9.1.13(@deck.gl/core@9.1.13)(@deck.gl/extensions@9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/mesh-layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/layers': 9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@loaders.gl/compression': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/gis': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/mvt': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/tiles': 4.3.4(@loaders.gl/core@4.3.4)
      '@luma.gl/core': 9.1.9
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/web-mercator': 4.1.0
      '@types/d3-array': 3.2.1
      '@types/d3-color': 1.4.5
      '@types/d3-scale': 3.3.5
      cartocolor: 5.0.2
      d3-array: 3.2.4
      d3-color: 3.1.0
      d3-format: 3.1.0
      d3-scale: 4.0.2
      earcut: 2.2.4
      h3-js: 4.2.1
      moment-timezone: 0.5.48
      pbf: 3.3.0
      quadbin: 0.4.2

  '@deck.gl/core@9.1.13':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@luma.gl/constants': 9.1.9
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@luma.gl/webgl': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/core': 4.1.0
      '@math.gl/sun': 4.1.0
      '@math.gl/types': 4.1.0
      '@math.gl/web-mercator': 4.1.0
      '@probe.gl/env': 4.1.0
      '@probe.gl/log': 4.1.0
      '@probe.gl/stats': 4.1.0
      '@types/offscreencanvas': 2019.7.3
      gl-matrix: 3.4.3
      mjolnir.js: 3.0.0

  '@deck.gl/extensions@9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@luma.gl/constants': 9.1.9
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/core': 4.1.0

  '@deck.gl/geo-layers@9.1.13(@deck.gl/core@9.1.13)(@deck.gl/extensions@9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/mesh-layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@deck.gl/extensions': 9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/layers': 9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/mesh-layers': 9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@loaders.gl/3d-tiles': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/gis': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/mvt': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/terrain': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/tiles': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/wms': 4.3.4(@loaders.gl/core@4.3.4)
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/gltf': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/core': 4.1.0
      '@math.gl/culling': 4.1.0
      '@math.gl/web-mercator': 4.1.0
      '@types/geojson': 7946.0.16
      h3-js: 4.2.1
      long: 3.2.0

  '@deck.gl/google-maps@9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/webgl@9.1.9(@luma.gl/core@9.1.9))':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@luma.gl/constants': 9.1.9
      '@luma.gl/core': 9.1.9
      '@luma.gl/webgl': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/core': 4.1.0
      '@types/google.maps': 3.58.1

  '@deck.gl/json@9.1.13(@deck.gl/core@9.1.13)':
    dependencies:
      '@deck.gl/core': 9.1.13
      jsep: 0.3.5

  '@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@mapbox/tiny-sdf': 2.0.6
      '@math.gl/core': 4.1.0
      '@math.gl/polygon': 4.1.0
      '@math.gl/web-mercator': 4.1.0
      earcut: 2.2.4

  '@deck.gl/mapbox@9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@luma.gl/constants': 9.1.9
      '@luma.gl/core': 9.1.9
      '@math.gl/web-mercator': 4.1.0

  '@deck.gl/mesh-layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@loaders.gl/gltf': 4.3.4(@loaders.gl/core@4.3.4)
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/gltf': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
    transitivePeerDependencies:
      - '@loaders.gl/core'

  '@deck.gl/react@9.1.13(@deck.gl/core@9.1.13)(@deck.gl/widgets@9.1.13(@deck.gl/core@9.1.13))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@deck.gl/core': 9.1.13
      '@deck.gl/widgets': 9.1.13(@deck.gl/core@9.1.13)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@deck.gl/widgets@9.1.13(@deck.gl/core@9.1.13)':
    dependencies:
      '@deck.gl/core': 9.1.13
      preact: 10.26.9

  '@esbuild/aix-ppc64@0.25.6':
    optional: true

  '@esbuild/aix-ppc64@0.25.8':
    optional: true

  '@esbuild/android-arm64@0.25.6':
    optional: true

  '@esbuild/android-arm64@0.25.8':
    optional: true

  '@esbuild/android-arm@0.25.6':
    optional: true

  '@esbuild/android-arm@0.25.8':
    optional: true

  '@esbuild/android-x64@0.25.6':
    optional: true

  '@esbuild/android-x64@0.25.8':
    optional: true

  '@esbuild/darwin-arm64@0.25.6':
    optional: true

  '@esbuild/darwin-arm64@0.25.8':
    optional: true

  '@esbuild/darwin-x64@0.25.6':
    optional: true

  '@esbuild/darwin-x64@0.25.8':
    optional: true

  '@esbuild/freebsd-arm64@0.25.6':
    optional: true

  '@esbuild/freebsd-arm64@0.25.8':
    optional: true

  '@esbuild/freebsd-x64@0.25.6':
    optional: true

  '@esbuild/freebsd-x64@0.25.8':
    optional: true

  '@esbuild/linux-arm64@0.25.6':
    optional: true

  '@esbuild/linux-arm64@0.25.8':
    optional: true

  '@esbuild/linux-arm@0.25.6':
    optional: true

  '@esbuild/linux-arm@0.25.8':
    optional: true

  '@esbuild/linux-ia32@0.25.6':
    optional: true

  '@esbuild/linux-ia32@0.25.8':
    optional: true

  '@esbuild/linux-loong64@0.25.6':
    optional: true

  '@esbuild/linux-loong64@0.25.8':
    optional: true

  '@esbuild/linux-mips64el@0.25.6':
    optional: true

  '@esbuild/linux-mips64el@0.25.8':
    optional: true

  '@esbuild/linux-ppc64@0.25.6':
    optional: true

  '@esbuild/linux-ppc64@0.25.8':
    optional: true

  '@esbuild/linux-riscv64@0.25.6':
    optional: true

  '@esbuild/linux-riscv64@0.25.8':
    optional: true

  '@esbuild/linux-s390x@0.25.6':
    optional: true

  '@esbuild/linux-s390x@0.25.8':
    optional: true

  '@esbuild/linux-x64@0.25.6':
    optional: true

  '@esbuild/linux-x64@0.25.8':
    optional: true

  '@esbuild/netbsd-arm64@0.25.6':
    optional: true

  '@esbuild/netbsd-arm64@0.25.8':
    optional: true

  '@esbuild/netbsd-x64@0.25.6':
    optional: true

  '@esbuild/netbsd-x64@0.25.8':
    optional: true

  '@esbuild/openbsd-arm64@0.25.6':
    optional: true

  '@esbuild/openbsd-arm64@0.25.8':
    optional: true

  '@esbuild/openbsd-x64@0.25.6':
    optional: true

  '@esbuild/openbsd-x64@0.25.8':
    optional: true

  '@esbuild/openharmony-arm64@0.25.6':
    optional: true

  '@esbuild/openharmony-arm64@0.25.8':
    optional: true

  '@esbuild/sunos-x64@0.25.6':
    optional: true

  '@esbuild/sunos-x64@0.25.8':
    optional: true

  '@esbuild/win32-arm64@0.25.6':
    optional: true

  '@esbuild/win32-arm64@0.25.8':
    optional: true

  '@esbuild/win32-ia32@0.25.6':
    optional: true

  '@esbuild/win32-ia32@0.25.8':
    optional: true

  '@esbuild/win32-x64@0.25.6':
    optional: true

  '@esbuild/win32-x64@0.25.8':
    optional: true

  '@esri/arcgis-html-sanitizer@4.1.0':
    dependencies:
      xss: 1.0.13

  '@esri/calcite-components@3.2.1':
    dependencies:
      '@arcgis/components-utils': 4.33.11
      '@arcgis/lumina': 4.33.11
      '@esri/calcite-ui-icons': 4.2.0
      '@floating-ui/dom': 1.7.2
      '@floating-ui/utils': 0.2.10
      '@types/sortablejs': 1.15.8
      color: 5.0.0
      composed-offset-position: 0.0.6(@floating-ui/utils@0.2.10)
      dayjs: 1.11.13
      focus-trap: 7.6.5
      interactjs: 1.10.27
      lodash-es: 4.17.21
      sortablejs: 1.15.6
      timezone-groups: 0.10.4
      type-fest: 4.41.0

  '@esri/calcite-ui-icons@4.2.0': {}

  '@fastify/accept-negotiator@1.1.0': {}

  '@fastify/ajv-compiler@3.6.0':
    dependencies:
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      fast-uri: 2.4.0

  '@fastify/cors@9.0.1':
    dependencies:
      fastify-plugin: 4.5.1
      mnemonist: 0.39.6

  '@fastify/error@3.4.1': {}

  '@fastify/fast-json-stringify-compiler@4.3.0':
    dependencies:
      fast-json-stringify: 5.16.1

  '@fastify/jwt@8.0.1':
    dependencies:
      '@fastify/error': 3.4.1
      '@lukeed/ms': 2.0.2
      fast-jwt: 4.0.5
      fastify-plugin: 4.5.1
      steed: 1.1.3

  '@fastify/merge-json-schemas@0.1.1':
    dependencies:
      fast-deep-equal: 3.1.3

  '@fastify/send@2.1.0':
    dependencies:
      '@lukeed/ms': 2.0.2
      escape-html: 1.0.3
      fast-decode-uri-component: 1.0.1
      http-errors: 2.0.0
      mime: 3.0.0

  '@fastify/static@7.0.4':
    dependencies:
      '@fastify/accept-negotiator': 1.1.0
      '@fastify/send': 2.1.0
      content-disposition: 0.5.4
      fastify-plugin: 4.5.1
      fastq: 1.19.1
      glob: 10.4.5

  '@fastify/swagger-ui@4.2.0':
    dependencies:
      '@fastify/static': 7.0.4
      fastify-plugin: 4.5.1
      openapi-types: 12.1.3
      rfdc: 1.4.1
      yaml: 2.8.0

  '@fastify/swagger@8.15.0':
    dependencies:
      fastify-plugin: 4.5.1
      json-schema-resolver: 2.0.0
      openapi-types: 12.1.3
      rfdc: 1.4.1
      yaml: 2.8.0
    transitivePeerDependencies:
      - supports-color

  '@floating-ui/core@1.7.2':
    dependencies:
      '@floating-ui/utils': 0.2.10

  '@floating-ui/dom@1.7.2':
    dependencies:
      '@floating-ui/core': 1.7.2
      '@floating-ui/utils': 0.2.10

  '@floating-ui/react-dom@2.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/dom': 1.7.2
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@floating-ui/react@0.26.28(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/react-dom': 2.1.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@floating-ui/utils': 0.2.10
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      tabbable: 6.2.0

  '@floating-ui/utils@0.2.10': {}

  '@headlessui/react@2.2.6(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@floating-ui/react': 0.26.28(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/focus': 3.21.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/interactions': 3.25.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@tanstack/react-virtual': 3.13.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      use-sync-external-store: 1.5.0(react@18.3.1)

  '@interactjs/types@1.10.27': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@isaacs/fs-minipass@4.0.1':
    dependencies:
      minipass: 7.1.2

  '@jridgewell/gen-mapping@0.3.12':
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4
      '@jridgewell/trace-mapping': 0.3.29

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/sourcemap-codec@1.5.4': {}

  '@jridgewell/trace-mapping@0.3.29':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.4

  '@lit-labs/ssr-client@1.1.7':
    dependencies:
      '@lit/reactive-element': 2.1.1
      lit: 3.3.1
      lit-html: 3.3.1

  '@lit-labs/ssr-dom-shim@1.4.0': {}

  '@lit-labs/ssr@3.3.1':
    dependencies:
      '@lit-labs/ssr-client': 1.1.7
      '@lit-labs/ssr-dom-shim': 1.4.0
      '@lit/reactive-element': 2.1.1
      '@parse5/tools': 0.3.0
      '@types/node': 16.18.126
      enhanced-resolve: 5.18.2
      lit: 3.3.1
      lit-element: 4.2.1
      lit-html: 3.3.1
      node-fetch: 3.3.2
      parse5: 7.3.0

  '@lit/context@1.1.6':
    dependencies:
      '@lit/reactive-element': 2.1.1

  '@lit/reactive-element@2.1.1':
    dependencies:
      '@lit-labs/ssr-dom-shim': 1.4.0

  '@loaders.gl/3d-tiles@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/compression': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/crypto': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/draco': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/gltf': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/math': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/tiles': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/zip': 4.3.4(@loaders.gl/core@4.3.4)
      '@math.gl/core': 4.1.0
      '@math.gl/culling': 4.1.0
      '@math.gl/geospatial': 4.1.0
      '@probe.gl/log': 4.1.0
      long: 5.3.2

  '@loaders.gl/compression@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/worker-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@types/brotli': 1.3.4
      '@types/pako': 1.0.7
      fflate: 0.7.4
      lzo-wasm: 0.0.4
      pako: 1.0.11
      snappyjs: 0.6.1
    optionalDependencies:
      brotli: 1.3.3
      lz4js: 0.2.0
      zstd-codec: 0.1.5

  '@loaders.gl/core@4.3.4':
    dependencies:
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/worker-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@probe.gl/log': 4.1.0

  '@loaders.gl/crypto@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/worker-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@types/crypto-js': 4.2.2

  '@loaders.gl/draco@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/worker-utils': 4.3.4(@loaders.gl/core@4.3.4)
      draco3d: 1.5.7

  '@loaders.gl/gis@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@mapbox/vector-tile': 1.3.1
      '@math.gl/polygon': 4.1.0
      pbf: 3.3.0

  '@loaders.gl/gltf@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/draco': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/textures': 4.3.4(@loaders.gl/core@4.3.4)
      '@math.gl/core': 4.1.0

  '@loaders.gl/images@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)

  '@loaders.gl/loader-utils@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/worker-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@probe.gl/log': 4.1.0
      '@probe.gl/stats': 4.1.0

  '@loaders.gl/math@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@math.gl/core': 4.1.0

  '@loaders.gl/mvt@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/gis': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@math.gl/polygon': 4.1.0
      '@probe.gl/stats': 4.1.0
      pbf: 3.3.0

  '@loaders.gl/schema@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@types/geojson': 7946.0.16

  '@loaders.gl/terrain@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@mapbox/martini': 0.2.0

  '@loaders.gl/textures@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/worker-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@math.gl/types': 4.1.0
      ktx-parse: 0.7.1
      texture-compressor: 1.0.2

  '@loaders.gl/tiles@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/math': 4.3.4(@loaders.gl/core@4.3.4)
      '@math.gl/core': 4.1.0
      '@math.gl/culling': 4.1.0
      '@math.gl/geospatial': 4.1.0
      '@math.gl/web-mercator': 4.1.0
      '@probe.gl/stats': 4.1.0

  '@loaders.gl/wms@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/images': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/xml': 4.3.4(@loaders.gl/core@4.3.4)
      '@turf/rewind': 5.1.5
      deep-strict-equal: 0.2.0

  '@loaders.gl/worker-utils@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4

  '@loaders.gl/xml@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/schema': 4.3.4(@loaders.gl/core@4.3.4)
      fast-xml-parser: 4.5.3

  '@loaders.gl/zip@4.3.4(@loaders.gl/core@4.3.4)':
    dependencies:
      '@loaders.gl/compression': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/crypto': 4.3.4(@loaders.gl/core@4.3.4)
      '@loaders.gl/loader-utils': 4.3.4(@loaders.gl/core@4.3.4)
      jszip: 3.10.1
      md5: 2.3.0

  '@lukeed/ms@2.0.2': {}

  '@luma.gl/constants@9.1.9': {}

  '@luma.gl/core@9.1.9':
    dependencies:
      '@math.gl/types': 4.1.0
      '@probe.gl/env': 4.1.0
      '@probe.gl/log': 4.1.0
      '@probe.gl/stats': 4.1.0
      '@types/offscreencanvas': 2019.7.3

  '@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))':
    dependencies:
      '@luma.gl/core': 9.1.9
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/core': 4.1.0
      '@math.gl/types': 4.1.0
      '@probe.gl/log': 4.1.0
      '@probe.gl/stats': 4.1.0

  '@luma.gl/gltf@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))':
    dependencies:
      '@loaders.gl/core': 4.3.4
      '@loaders.gl/textures': 4.3.4(@loaders.gl/core@4.3.4)
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
      '@luma.gl/shadertools': 9.1.9(@luma.gl/core@9.1.9)
      '@math.gl/core': 4.1.0

  '@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)':
    dependencies:
      '@luma.gl/core': 9.1.9
      '@math.gl/core': 4.1.0
      '@math.gl/types': 4.1.0
      wgsl_reflect: 1.2.3

  '@luma.gl/webgl@9.1.9(@luma.gl/core@9.1.9)':
    dependencies:
      '@luma.gl/constants': 9.1.9
      '@luma.gl/core': 9.1.9
      '@math.gl/types': 4.1.0
      '@probe.gl/env': 4.1.0

  '@mapbox/geojson-rewind@0.5.2':
    dependencies:
      get-stream: 6.0.1
      minimist: 1.2.8

  '@mapbox/jsonlint-lines-primitives@2.0.2': {}

  '@mapbox/martini@0.2.0': {}

  '@mapbox/point-geometry@0.1.0': {}

  '@mapbox/tiny-sdf@2.0.6': {}

  '@mapbox/unitbezier@0.0.1': {}

  '@mapbox/vector-tile@1.3.1':
    dependencies:
      '@mapbox/point-geometry': 0.1.0

  '@mapbox/whoots-js@3.1.0': {}

  '@maplibre/maplibre-gl-style-spec@19.3.3':
    dependencies:
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/unitbezier': 0.0.1
      json-stringify-pretty-compact: 3.0.0
      minimist: 1.2.8
      rw: 1.3.3
      sort-object: 3.0.3

  '@maplibre/maplibre-gl-style-spec@23.3.0':
    dependencies:
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/unitbezier': 0.0.1
      json-stringify-pretty-compact: 4.0.0
      minimist: 1.2.8
      quickselect: 3.0.0
      rw: 1.3.3
      tinyqueue: 3.0.0

  '@math.gl/core@4.1.0':
    dependencies:
      '@math.gl/types': 4.1.0

  '@math.gl/culling@4.1.0':
    dependencies:
      '@math.gl/core': 4.1.0
      '@math.gl/types': 4.1.0

  '@math.gl/geospatial@4.1.0':
    dependencies:
      '@math.gl/core': 4.1.0
      '@math.gl/types': 4.1.0

  '@math.gl/polygon@4.1.0':
    dependencies:
      '@math.gl/core': 4.1.0

  '@math.gl/sun@4.1.0': {}

  '@math.gl/types@4.1.0': {}

  '@math.gl/web-mercator@4.1.0':
    dependencies:
      '@math.gl/core': 4.1.0

  '@mediapipe/tasks-vision@0.10.2': {}

  '@open-wc/dedupe-mixin@1.4.0': {}

  '@parse5/tools@0.3.0':
    dependencies:
      parse5: 7.3.0

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@polymer/polymer@3.5.2':
    dependencies:
      '@webcomponents/shadycss': 1.11.2

  '@probe.gl/env@4.1.0': {}

  '@probe.gl/log@4.1.0':
    dependencies:
      '@probe.gl/env': 4.1.0

  '@probe.gl/stats@4.1.0': {}

  '@react-aria/focus@3.21.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/interactions': 3.25.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-aria/utils': 3.30.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-types/shared': 3.31.0(react@18.3.1)
      '@swc/helpers': 0.5.17
      clsx: 2.1.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/interactions@3.25.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.10(react@18.3.1)
      '@react-aria/utils': 3.30.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@react-stately/flags': 3.1.2
      '@react-types/shared': 3.31.0(react@18.3.1)
      '@swc/helpers': 0.5.17
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-aria/ssr@3.9.10(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.17
      react: 18.3.1

  '@react-aria/utils@3.30.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@react-aria/ssr': 3.9.10(react@18.3.1)
      '@react-stately/flags': 3.1.2
      '@react-stately/utils': 3.10.8(react@18.3.1)
      '@react-types/shared': 3.31.0(react@18.3.1)
      '@swc/helpers': 0.5.17
      clsx: 2.1.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@react-dnd/asap@5.0.2': {}

  '@react-dnd/invariant@4.0.2': {}

  '@react-dnd/shallowequal@4.0.2': {}

  '@react-spring/animated@9.6.1(react@18.3.1)':
    dependencies:
      '@react-spring/shared': 9.6.1(react@18.3.1)
      '@react-spring/types': 9.6.1
      react: 18.3.1

  '@react-spring/core@9.6.1(react@18.3.1)':
    dependencies:
      '@react-spring/animated': 9.6.1(react@18.3.1)
      '@react-spring/rafz': 9.6.1
      '@react-spring/shared': 9.6.1(react@18.3.1)
      '@react-spring/types': 9.6.1
      react: 18.3.1

  '@react-spring/rafz@9.6.1': {}

  '@react-spring/shared@9.6.1(react@18.3.1)':
    dependencies:
      '@react-spring/rafz': 9.6.1
      '@react-spring/types': 9.6.1
      react: 18.3.1

  '@react-spring/three@9.6.1(@react-three/fiber@8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0))(react@18.3.1)(three@0.152.0)':
    dependencies:
      '@react-spring/animated': 9.6.1(react@18.3.1)
      '@react-spring/core': 9.6.1(react@18.3.1)
      '@react-spring/shared': 9.6.1(react@18.3.1)
      '@react-spring/types': 9.6.1
      '@react-three/fiber': 8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0)
      react: 18.3.1
      three: 0.152.0

  '@react-spring/types@9.6.1': {}

  '@react-stately/flags@3.1.2':
    dependencies:
      '@swc/helpers': 0.5.17

  '@react-stately/utils@3.10.8(react@18.3.1)':
    dependencies:
      '@swc/helpers': 0.5.17
      react: 18.3.1

  '@react-three/drei@9.88.13(@react-three/fiber@8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0))(@types/three@0.152.0)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0)':
    dependencies:
      '@babel/runtime': 7.28.2
      '@mediapipe/tasks-vision': 0.10.2
      '@react-spring/three': 9.6.1(@react-three/fiber@8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0))(react@18.3.1)(three@0.152.0)
      '@react-three/fiber': 8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0)
      '@use-gesture/react': 10.3.1(react@18.3.1)
      camera-controls: 2.10.1(three@0.152.0)
      cross-env: 7.0.3
      detect-gpu: 5.0.70
      glsl-noise: 0.0.0
      lodash.clamp: 4.0.3
      lodash.omit: 4.5.0
      lodash.pick: 4.4.0
      maath: 0.9.0(@types/three@0.152.0)(three@0.152.0)
      meshline: 3.3.1(three@0.152.0)
      react: 18.3.1
      react-composer: 5.0.3(react@18.3.1)
      react-merge-refs: 1.1.0
      stats-gl: 1.0.7
      stats.js: 0.17.0
      suspend-react: 0.1.3(react@18.3.1)
      three: 0.152.0
      three-mesh-bvh: 0.6.8(three@0.152.0)
      three-stdlib: 2.36.0(three@0.152.0)
      troika-three-text: 0.47.2(three@0.152.0)
      utility-types: 3.11.0
      uuid: 9.0.1
      zustand: 3.7.2(react@18.3.1)
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@types/three'

  '@react-three/fiber@8.15.19(@types/react@18.3.23)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(three@0.152.0)':
    dependencies:
      '@babel/runtime': 7.28.2
      '@types/react-reconciler': 0.26.7
      '@types/webxr': 0.5.22
      base64-js: 1.5.1
      buffer: 6.0.3
      its-fine: 1.2.5(@types/react@18.3.23)(react@18.3.1)
      react: 18.3.1
      react-reconciler: 0.27.0(react@18.3.1)
      react-use-measure: 2.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      scheduler: 0.21.0
      suspend-react: 0.1.3(react@18.3.1)
      three: 0.152.0
      zustand: 3.7.2(react@18.3.1)
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@types/react'

  '@react-types/shared@3.31.0(react@18.3.1)':
    dependencies:
      react: 18.3.1

  '@remix-run/router@1.23.0': {}

  '@rolldown/pluginutils@1.0.0-beta.27': {}

  '@rollup/pluginutils@5.2.0(rollup@4.45.1)':
    dependencies:
      '@types/estree': 1.0.8
      estree-walker: 2.0.2
      picomatch: 4.0.3
    optionalDependencies:
      rollup: 4.45.1

  '@rollup/rollup-android-arm-eabi@4.45.1':
    optional: true

  '@rollup/rollup-android-arm64@4.45.1':
    optional: true

  '@rollup/rollup-darwin-arm64@4.45.1':
    optional: true

  '@rollup/rollup-darwin-x64@4.45.1':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.45.1':
    optional: true

  '@rollup/rollup-freebsd-x64@4.45.1':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.45.1':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.45.1':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.45.1':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.45.1':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.45.1':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.45.1':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.45.1':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.45.1':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.45.1':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.45.1':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.45.1':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.45.1':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.45.1':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.45.1':
    optional: true

  '@svgr/babel-plugin-add-jsx-attribute@8.0.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-plugin-remove-jsx-attribute@8.0.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-plugin-svg-dynamic-title@8.0.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-plugin-svg-em-dimensions@8.0.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-plugin-transform-react-native-svg@8.1.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-plugin-transform-svg-component@8.0.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0

  '@svgr/babel-preset@8.1.0(@babel/core@7.28.0)':
    dependencies:
      '@babel/core': 7.28.0
      '@svgr/babel-plugin-add-jsx-attribute': 8.0.0(@babel/core@7.28.0)
      '@svgr/babel-plugin-remove-jsx-attribute': 8.0.0(@babel/core@7.28.0)
      '@svgr/babel-plugin-remove-jsx-empty-expression': 8.0.0(@babel/core@7.28.0)
      '@svgr/babel-plugin-replace-jsx-attribute-value': 8.0.0(@babel/core@7.28.0)
      '@svgr/babel-plugin-svg-dynamic-title': 8.0.0(@babel/core@7.28.0)
      '@svgr/babel-plugin-svg-em-dimensions': 8.0.0(@babel/core@7.28.0)
      '@svgr/babel-plugin-transform-react-native-svg': 8.1.0(@babel/core@7.28.0)
      '@svgr/babel-plugin-transform-svg-component': 8.0.0(@babel/core@7.28.0)

  '@svgr/core@8.1.0(typescript@5.8.3)':
    dependencies:
      '@babel/core': 7.28.0
      '@svgr/babel-preset': 8.1.0(@babel/core@7.28.0)
      camelcase: 6.3.0
      cosmiconfig: 8.3.6(typescript@5.8.3)
      snake-case: 3.0.4
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@svgr/hast-util-to-babel-ast@8.0.0':
    dependencies:
      '@babel/types': 7.28.2
      entities: 4.5.0

  '@svgr/plugin-jsx@8.1.0(@svgr/core@8.1.0(typescript@5.8.3))':
    dependencies:
      '@babel/core': 7.28.0
      '@svgr/babel-preset': 8.1.0(@babel/core@7.28.0)
      '@svgr/core': 8.1.0(typescript@5.8.3)
      '@svgr/hast-util-to-babel-ast': 8.0.0
      svg-parser: 2.0.4
    transitivePeerDependencies:
      - supports-color

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@tailwindcss/node@4.1.11':
    dependencies:
      '@ampproject/remapping': 2.3.0
      enhanced-resolve: 5.18.2
      jiti: 2.5.1
      lightningcss: 1.30.1
      magic-string: 0.30.17
      source-map-js: 1.2.1
      tailwindcss: 4.1.11

  '@tailwindcss/oxide-android-arm64@4.1.11':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.11':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.11':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.11':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.11':
    optional: true

  '@tailwindcss/oxide-wasm32-wasi@4.1.11':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.11':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.11':
    optional: true

  '@tailwindcss/oxide@4.1.11':
    dependencies:
      detect-libc: 2.0.4
      tar: 7.4.3
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.11
      '@tailwindcss/oxide-darwin-arm64': 4.1.11
      '@tailwindcss/oxide-darwin-x64': 4.1.11
      '@tailwindcss/oxide-freebsd-x64': 4.1.11
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.11
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.11
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.11
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.11
      '@tailwindcss/oxide-linux-x64-musl': 4.1.11
      '@tailwindcss/oxide-wasm32-wasi': 4.1.11
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.11
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.11

  '@tailwindcss/vite@4.1.11(vite@7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))':
    dependencies:
      '@tailwindcss/node': 4.1.11
      '@tailwindcss/oxide': 4.1.11
      tailwindcss: 4.1.11
      vite: 7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)

  '@tanstack/query-core@5.83.0': {}

  '@tanstack/react-query@5.83.0(react@18.3.1)':
    dependencies:
      '@tanstack/query-core': 5.83.0
      react: 18.3.1

  '@tanstack/react-virtual@3.13.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@tanstack/virtual-core': 3.13.12
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@tanstack/virtual-core@3.13.12': {}

  '@turf/along@7.2.0':
    dependencies:
      '@turf/bearing': 7.2.0
      '@turf/destination': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/angle@7.2.0':
    dependencies:
      '@turf/bearing': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/rhumb-bearing': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/area@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/bbox-clip@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/bbox-polygon@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/bbox@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/bearing@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/bezier-spline@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-clockwise@5.1.5':
    dependencies:
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0

  '@turf/boolean-clockwise@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-concave@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-contains@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/boolean-point-on-line': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-crosses@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/line-intersect': 7.2.0
      '@turf/polygon-to-line': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-disjoint@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/line-intersect': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/polygon-to-line': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-equal@7.2.0':
    dependencies:
      '@turf/clean-coords': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      geojson-equality-ts: 1.0.2
      tslib: 2.8.1

  '@turf/boolean-intersects@7.2.0':
    dependencies:
      '@turf/boolean-disjoint': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-overlap@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/line-intersect': 7.2.0
      '@turf/line-overlap': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      geojson-equality-ts: 1.0.2
      tslib: 2.8.1

  '@turf/boolean-parallel@7.2.0':
    dependencies:
      '@turf/clean-coords': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/line-segment': 7.2.0
      '@turf/rhumb-bearing': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-point-in-polygon@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      point-in-polygon-hao: 1.2.4
      tslib: 2.8.1

  '@turf/boolean-point-on-line@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-touches@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/boolean-point-on-line': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/boolean-valid@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/boolean-crosses': 7.2.0
      '@turf/boolean-disjoint': 7.2.0
      '@turf/boolean-overlap': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/boolean-point-on-line': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/line-intersect': 7.2.0
      '@types/geojson': 7946.0.16
      geojson-polygon-self-intersections: 1.2.1
      tslib: 2.8.1

  '@turf/boolean-within@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/boolean-point-on-line': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/buffer@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/center': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/jsts': 2.7.2
      '@turf/meta': 7.2.0
      '@turf/projection': 7.2.0
      '@types/geojson': 7946.0.16
      d3-geo: 1.7.1

  '@turf/center-mean@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/center-median@7.2.0':
    dependencies:
      '@turf/center-mean': 7.2.0
      '@turf/centroid': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/center-of-mass@7.2.0':
    dependencies:
      '@turf/centroid': 7.2.0
      '@turf/convex': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/center@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/centroid@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/circle@7.2.0':
    dependencies:
      '@turf/destination': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/clean-coords@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/clone@5.1.5':
    dependencies:
      '@turf/helpers': 5.1.5

  '@turf/clone@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/clusters-dbscan@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      rbush: 3.0.1
      tslib: 2.8.1

  '@turf/clusters-kmeans@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      skmeans: 0.9.7
      tslib: 2.8.1

  '@turf/clusters@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/collect@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      rbush: 3.0.1
      tslib: 2.8.1

  '@turf/combine@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/concave@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/tin': 7.2.0
      '@types/geojson': 7946.0.16
      topojson-client: 3.1.0
      topojson-server: 3.0.1
      tslib: 2.8.1

  '@turf/convex@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      concaveman: 1.2.1
      tslib: 2.8.1

  '@turf/destination@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/difference@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      polyclip-ts: 0.16.8
      tslib: 2.8.1

  '@turf/dissolve@7.2.0':
    dependencies:
      '@turf/flatten': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      polyclip-ts: 0.16.8
      tslib: 2.8.1

  '@turf/distance-weight@7.2.0':
    dependencies:
      '@turf/centroid': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/distance@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/ellipse@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/rhumb-destination': 7.2.0
      '@turf/transform-rotate': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/envelope@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/bbox-polygon': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/explode@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/flatten@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/flip@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/geojson-rbush@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      rbush: 3.0.1

  '@turf/great-circle@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/helpers@5.1.5': {}

  '@turf/helpers@7.2.0':
    dependencies:
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/hex-grid@7.2.0':
    dependencies:
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/intersect': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/interpolate@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/centroid': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/hex-grid': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/point-grid': 7.2.0
      '@turf/square-grid': 7.2.0
      '@turf/triangle-grid': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/intersect@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      polyclip-ts: 0.16.8
      tslib: 2.8.1

  '@turf/invariant@5.2.0':
    dependencies:
      '@turf/helpers': 5.1.5

  '@turf/invariant@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/isobands@7.2.0':
    dependencies:
      '@turf/area': 7.2.0
      '@turf/bbox': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/explode': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      marchingsquares: 1.3.3
      tslib: 2.8.1

  '@turf/isolines@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      marchingsquares: 1.3.3
      tslib: 2.8.1

  '@turf/jsts@2.7.2':
    dependencies:
      jsts: 2.7.1

  '@turf/kinks@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/length@7.2.0':
    dependencies:
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/line-arc@7.2.0':
    dependencies:
      '@turf/circle': 7.2.0
      '@turf/destination': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/line-chunk@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/length': 7.2.0
      '@turf/line-slice-along': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/line-intersect@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      sweepline-intersections: 1.5.0
      tslib: 2.8.1

  '@turf/line-offset@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/line-overlap@7.2.0':
    dependencies:
      '@turf/boolean-point-on-line': 7.2.0
      '@turf/geojson-rbush': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/line-segment': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/nearest-point-on-line': 7.2.0
      '@types/geojson': 7946.0.16
      fast-deep-equal: 3.1.3
      tslib: 2.8.1

  '@turf/line-segment@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/line-slice-along@7.2.0':
    dependencies:
      '@turf/bearing': 7.2.0
      '@turf/destination': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/line-slice@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/nearest-point-on-line': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/line-split@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/geojson-rbush': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/line-intersect': 7.2.0
      '@turf/line-segment': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/nearest-point-on-line': 7.2.0
      '@turf/square': 7.2.0
      '@turf/truncate': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/line-to-polygon@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/mask@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      polyclip-ts: 0.16.8
      tslib: 2.8.1

  '@turf/meta@5.2.0':
    dependencies:
      '@turf/helpers': 5.1.5

  '@turf/meta@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16

  '@turf/midpoint@7.2.0':
    dependencies:
      '@turf/bearing': 7.2.0
      '@turf/destination': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/moran-index@7.2.0':
    dependencies:
      '@turf/distance-weight': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/nearest-neighbor-analysis@7.2.0':
    dependencies:
      '@turf/area': 7.2.0
      '@turf/bbox': 7.2.0
      '@turf/bbox-polygon': 7.2.0
      '@turf/centroid': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/nearest-point': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/nearest-point-on-line@7.2.0':
    dependencies:
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/nearest-point-to-line@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/point-to-line-distance': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/nearest-point@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/planepoint@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/point-grid@7.2.0':
    dependencies:
      '@turf/boolean-within': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/point-on-feature@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/center': 7.2.0
      '@turf/explode': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/nearest-point': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/point-to-line-distance@7.2.0':
    dependencies:
      '@turf/bearing': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/nearest-point-on-line': 7.2.0
      '@turf/projection': 7.2.0
      '@turf/rhumb-bearing': 7.2.0
      '@turf/rhumb-distance': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/point-to-polygon-distance@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/point-to-line-distance': 7.2.0
      '@turf/polygon-to-line': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/points-within-polygon@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/polygon-smooth@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/polygon-tangents@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/boolean-within': 7.2.0
      '@turf/explode': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/nearest-point': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/polygon-to-line@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/polygonize@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/envelope': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/projection@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/quadrat-analysis@7.2.0':
    dependencies:
      '@turf/area': 7.2.0
      '@turf/bbox': 7.2.0
      '@turf/bbox-polygon': 7.2.0
      '@turf/centroid': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/point-grid': 7.2.0
      '@turf/random': 7.2.0
      '@turf/square-grid': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/random@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/rectangle-grid@7.2.0':
    dependencies:
      '@turf/boolean-intersects': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/rewind@5.1.5':
    dependencies:
      '@turf/boolean-clockwise': 5.1.5
      '@turf/clone': 5.1.5
      '@turf/helpers': 5.1.5
      '@turf/invariant': 5.2.0
      '@turf/meta': 5.2.0

  '@turf/rewind@7.2.0':
    dependencies:
      '@turf/boolean-clockwise': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/rhumb-bearing@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/rhumb-destination@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/rhumb-distance@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/sample@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/sector@7.2.0':
    dependencies:
      '@turf/circle': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/line-arc': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/shortest-path@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/bbox-polygon': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/clean-coords': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/transform-scale': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/simplify@7.2.0':
    dependencies:
      '@turf/clean-coords': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/square-grid@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/rectangle-grid': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/square@7.2.0':
    dependencies:
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/standard-deviational-ellipse@7.2.0':
    dependencies:
      '@turf/center-mean': 7.2.0
      '@turf/ellipse': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/points-within-polygon': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/tag@7.2.0':
    dependencies:
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/tesselate@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      earcut: 2.2.4
      tslib: 2.8.1

  '@turf/tin@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/transform-rotate@7.2.0':
    dependencies:
      '@turf/centroid': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/rhumb-bearing': 7.2.0
      '@turf/rhumb-destination': 7.2.0
      '@turf/rhumb-distance': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/transform-scale@7.2.0':
    dependencies:
      '@turf/bbox': 7.2.0
      '@turf/center': 7.2.0
      '@turf/centroid': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/rhumb-bearing': 7.2.0
      '@turf/rhumb-destination': 7.2.0
      '@turf/rhumb-distance': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/transform-translate@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/rhumb-destination': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/triangle-grid@7.2.0':
    dependencies:
      '@turf/distance': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/intersect': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/truncate@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/turf@7.2.0':
    dependencies:
      '@turf/along': 7.2.0
      '@turf/angle': 7.2.0
      '@turf/area': 7.2.0
      '@turf/bbox': 7.2.0
      '@turf/bbox-clip': 7.2.0
      '@turf/bbox-polygon': 7.2.0
      '@turf/bearing': 7.2.0
      '@turf/bezier-spline': 7.2.0
      '@turf/boolean-clockwise': 7.2.0
      '@turf/boolean-concave': 7.2.0
      '@turf/boolean-contains': 7.2.0
      '@turf/boolean-crosses': 7.2.0
      '@turf/boolean-disjoint': 7.2.0
      '@turf/boolean-equal': 7.2.0
      '@turf/boolean-intersects': 7.2.0
      '@turf/boolean-overlap': 7.2.0
      '@turf/boolean-parallel': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/boolean-point-on-line': 7.2.0
      '@turf/boolean-touches': 7.2.0
      '@turf/boolean-valid': 7.2.0
      '@turf/boolean-within': 7.2.0
      '@turf/buffer': 7.2.0
      '@turf/center': 7.2.0
      '@turf/center-mean': 7.2.0
      '@turf/center-median': 7.2.0
      '@turf/center-of-mass': 7.2.0
      '@turf/centroid': 7.2.0
      '@turf/circle': 7.2.0
      '@turf/clean-coords': 7.2.0
      '@turf/clone': 7.2.0
      '@turf/clusters': 7.2.0
      '@turf/clusters-dbscan': 7.2.0
      '@turf/clusters-kmeans': 7.2.0
      '@turf/collect': 7.2.0
      '@turf/combine': 7.2.0
      '@turf/concave': 7.2.0
      '@turf/convex': 7.2.0
      '@turf/destination': 7.2.0
      '@turf/difference': 7.2.0
      '@turf/dissolve': 7.2.0
      '@turf/distance': 7.2.0
      '@turf/distance-weight': 7.2.0
      '@turf/ellipse': 7.2.0
      '@turf/envelope': 7.2.0
      '@turf/explode': 7.2.0
      '@turf/flatten': 7.2.0
      '@turf/flip': 7.2.0
      '@turf/geojson-rbush': 7.2.0
      '@turf/great-circle': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/hex-grid': 7.2.0
      '@turf/interpolate': 7.2.0
      '@turf/intersect': 7.2.0
      '@turf/invariant': 7.2.0
      '@turf/isobands': 7.2.0
      '@turf/isolines': 7.2.0
      '@turf/kinks': 7.2.0
      '@turf/length': 7.2.0
      '@turf/line-arc': 7.2.0
      '@turf/line-chunk': 7.2.0
      '@turf/line-intersect': 7.2.0
      '@turf/line-offset': 7.2.0
      '@turf/line-overlap': 7.2.0
      '@turf/line-segment': 7.2.0
      '@turf/line-slice': 7.2.0
      '@turf/line-slice-along': 7.2.0
      '@turf/line-split': 7.2.0
      '@turf/line-to-polygon': 7.2.0
      '@turf/mask': 7.2.0
      '@turf/meta': 7.2.0
      '@turf/midpoint': 7.2.0
      '@turf/moran-index': 7.2.0
      '@turf/nearest-neighbor-analysis': 7.2.0
      '@turf/nearest-point': 7.2.0
      '@turf/nearest-point-on-line': 7.2.0
      '@turf/nearest-point-to-line': 7.2.0
      '@turf/planepoint': 7.2.0
      '@turf/point-grid': 7.2.0
      '@turf/point-on-feature': 7.2.0
      '@turf/point-to-line-distance': 7.2.0
      '@turf/point-to-polygon-distance': 7.2.0
      '@turf/points-within-polygon': 7.2.0
      '@turf/polygon-smooth': 7.2.0
      '@turf/polygon-tangents': 7.2.0
      '@turf/polygon-to-line': 7.2.0
      '@turf/polygonize': 7.2.0
      '@turf/projection': 7.2.0
      '@turf/quadrat-analysis': 7.2.0
      '@turf/random': 7.2.0
      '@turf/rectangle-grid': 7.2.0
      '@turf/rewind': 7.2.0
      '@turf/rhumb-bearing': 7.2.0
      '@turf/rhumb-destination': 7.2.0
      '@turf/rhumb-distance': 7.2.0
      '@turf/sample': 7.2.0
      '@turf/sector': 7.2.0
      '@turf/shortest-path': 7.2.0
      '@turf/simplify': 7.2.0
      '@turf/square': 7.2.0
      '@turf/square-grid': 7.2.0
      '@turf/standard-deviational-ellipse': 7.2.0
      '@turf/tag': 7.2.0
      '@turf/tesselate': 7.2.0
      '@turf/tin': 7.2.0
      '@turf/transform-rotate': 7.2.0
      '@turf/transform-scale': 7.2.0
      '@turf/transform-translate': 7.2.0
      '@turf/triangle-grid': 7.2.0
      '@turf/truncate': 7.2.0
      '@turf/union': 7.2.0
      '@turf/unkink-polygon': 7.2.0
      '@turf/voronoi': 7.2.0
      '@types/geojson': 7946.0.16
      tslib: 2.8.1

  '@turf/union@7.2.0':
    dependencies:
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      polyclip-ts: 0.16.8
      tslib: 2.8.1

  '@turf/unkink-polygon@7.2.0':
    dependencies:
      '@turf/area': 7.2.0
      '@turf/boolean-point-in-polygon': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/meta': 7.2.0
      '@types/geojson': 7946.0.16
      rbush: 3.0.1
      tslib: 2.8.1

  '@turf/voronoi@7.2.0':
    dependencies:
      '@turf/clone': 7.2.0
      '@turf/helpers': 7.2.0
      '@turf/invariant': 7.2.0
      '@types/d3-voronoi': 1.1.12
      '@types/geojson': 7946.0.16
      d3-voronoi: 1.1.2
      tslib: 2.8.1

  '@tweenjs/tween.js@18.6.4': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.2
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.28.2

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.28.0
      '@babel/types': 7.28.2

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.28.2

  '@types/bcryptjs@2.4.6': {}

  '@types/better-sqlite3@7.6.13':
    dependencies:
      '@types/node': 20.19.9

  '@types/brotli@1.3.4':
    dependencies:
      '@types/node': 20.19.9

  '@types/crypto-js@4.2.2': {}

  '@types/d3-array@3.2.1': {}

  '@types/d3-axis@3.0.6':
    dependencies:
      '@types/d3-selection': 3.0.11

  '@types/d3-brush@3.0.6':
    dependencies:
      '@types/d3-selection': 3.0.11

  '@types/d3-chord@3.0.6': {}

  '@types/d3-color@1.4.5': {}

  '@types/d3-color@3.1.3': {}

  '@types/d3-contour@3.0.6':
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/geojson': 7946.0.16

  '@types/d3-delaunay@6.0.4': {}

  '@types/d3-dispatch@3.0.6': {}

  '@types/d3-drag@3.0.7':
    dependencies:
      '@types/d3-selection': 3.0.11

  '@types/d3-dsv@3.0.7': {}

  '@types/d3-ease@3.0.2': {}

  '@types/d3-fetch@3.0.7':
    dependencies:
      '@types/d3-dsv': 3.0.7

  '@types/d3-force@3.0.10': {}

  '@types/d3-format@3.0.4': {}

  '@types/d3-geo@3.1.0':
    dependencies:
      '@types/geojson': 7946.0.16

  '@types/d3-hierarchy@3.1.7': {}

  '@types/d3-interpolate@3.0.4':
    dependencies:
      '@types/d3-color': 3.1.3

  '@types/d3-path@3.1.1': {}

  '@types/d3-polygon@3.0.2': {}

  '@types/d3-quadtree@3.0.6': {}

  '@types/d3-random@3.0.3': {}

  '@types/d3-scale-chromatic@3.1.0': {}

  '@types/d3-scale@3.3.5':
    dependencies:
      '@types/d3-time': 2.1.4

  '@types/d3-scale@4.0.9':
    dependencies:
      '@types/d3-time': 3.0.4

  '@types/d3-selection@3.0.11': {}

  '@types/d3-shape@3.1.7':
    dependencies:
      '@types/d3-path': 3.1.1

  '@types/d3-time-format@4.0.3': {}

  '@types/d3-time@2.1.4': {}

  '@types/d3-time@3.0.4': {}

  '@types/d3-timer@3.0.2': {}

  '@types/d3-transition@3.0.9':
    dependencies:
      '@types/d3-selection': 3.0.11

  '@types/d3-voronoi@1.1.12': {}

  '@types/d3-zoom@3.0.8':
    dependencies:
      '@types/d3-interpolate': 3.0.4
      '@types/d3-selection': 3.0.11

  '@types/d3@7.4.3':
    dependencies:
      '@types/d3-array': 3.2.1
      '@types/d3-axis': 3.0.6
      '@types/d3-brush': 3.0.6
      '@types/d3-chord': 3.0.6
      '@types/d3-color': 3.1.3
      '@types/d3-contour': 3.0.6
      '@types/d3-delaunay': 6.0.4
      '@types/d3-dispatch': 3.0.6
      '@types/d3-drag': 3.0.7
      '@types/d3-dsv': 3.0.7
      '@types/d3-ease': 3.0.2
      '@types/d3-fetch': 3.0.7
      '@types/d3-force': 3.0.10
      '@types/d3-format': 3.0.4
      '@types/d3-geo': 3.1.0
      '@types/d3-hierarchy': 3.1.7
      '@types/d3-interpolate': 3.0.4
      '@types/d3-path': 3.1.1
      '@types/d3-polygon': 3.0.2
      '@types/d3-quadtree': 3.0.6
      '@types/d3-random': 3.0.3
      '@types/d3-scale': 4.0.9
      '@types/d3-scale-chromatic': 3.1.0
      '@types/d3-selection': 3.0.11
      '@types/d3-shape': 3.1.7
      '@types/d3-time': 3.0.4
      '@types/d3-time-format': 4.0.3
      '@types/d3-timer': 3.0.2
      '@types/d3-transition': 3.0.9
      '@types/d3-zoom': 3.0.8

  '@types/draco3d@1.4.10': {}

  '@types/estree@1.0.8': {}

  '@types/geojson-vt@3.2.5':
    dependencies:
      '@types/geojson': 7946.0.16

  '@types/geojson@7946.0.16': {}

  '@types/google.maps@3.58.1': {}

  '@types/mapbox__point-geometry@0.1.4': {}

  '@types/mapbox__vector-tile@1.3.4':
    dependencies:
      '@types/geojson': 7946.0.16
      '@types/mapbox__point-geometry': 0.1.4
      '@types/pbf': 3.0.5

  '@types/node@16.18.126': {}

  '@types/node@20.19.7':
    dependencies:
      undici-types: 6.21.0

  '@types/node@20.19.9':
    dependencies:
      undici-types: 6.21.0

  '@types/offscreencanvas@2019.7.3': {}

  '@types/pako@1.0.7': {}

  '@types/pbf@3.0.5': {}

  '@types/prop-types@15.7.15': {}

  '@types/react-dom@18.3.7(@types/react@18.3.23)':
    dependencies:
      '@types/react': 18.3.23

  '@types/react-reconciler@0.26.7':
    dependencies:
      '@types/react': 18.3.23

  '@types/react-reconciler@0.28.9(@types/react@18.3.23)':
    dependencies:
      '@types/react': 18.3.23

  '@types/react@18.3.23':
    dependencies:
      '@types/prop-types': 15.7.15
      csstype: 3.1.3

  '@types/sortablejs@1.15.8': {}

  '@types/stats.js@0.17.4': {}

  '@types/supercluster@7.1.3':
    dependencies:
      '@types/geojson': 7946.0.16

  '@types/three@0.152.0':
    dependencies:
      '@tweenjs/tween.js': 18.6.4
      '@types/stats.js': 0.17.4
      '@types/webxr': 0.5.22
      fflate: 0.6.10
      lil-gui: 0.17.0

  '@types/trusted-types@2.0.7': {}

  '@types/webxr@0.5.22': {}

  '@use-gesture/core@10.3.1': {}

  '@use-gesture/react@10.3.1(react@18.3.1)':
    dependencies:
      '@use-gesture/core': 10.3.1
      react: 18.3.1

  '@vaadin/a11y-base@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      '@polymer/polymer': 3.5.2
      '@vaadin/component-base': 24.7.11
      lit: 3.3.1

  '@vaadin/checkbox@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      '@polymer/polymer': 3.5.2
      '@vaadin/a11y-base': 24.7.11
      '@vaadin/component-base': 24.7.11
      '@vaadin/field-base': 24.7.11
      '@vaadin/vaadin-lumo-styles': 24.7.11
      '@vaadin/vaadin-material-styles': 24.7.11
      '@vaadin/vaadin-themable-mixin': 24.7.11
      lit: 3.3.1

  '@vaadin/component-base@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      '@polymer/polymer': 3.5.2
      '@vaadin/vaadin-development-mode-detector': 2.0.7
      '@vaadin/vaadin-usage-statistics': 2.1.3
      lit: 3.3.1

  '@vaadin/field-base@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      '@polymer/polymer': 3.5.2
      '@vaadin/a11y-base': 24.7.11
      '@vaadin/component-base': 24.7.11
      lit: 3.3.1

  '@vaadin/grid@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      '@polymer/polymer': 3.5.2
      '@vaadin/a11y-base': 24.7.11
      '@vaadin/checkbox': 24.7.11
      '@vaadin/component-base': 24.7.11
      '@vaadin/lit-renderer': 24.7.11
      '@vaadin/text-field': 24.7.11
      '@vaadin/vaadin-lumo-styles': 24.7.11
      '@vaadin/vaadin-material-styles': 24.7.11
      '@vaadin/vaadin-themable-mixin': 24.7.11
      lit: 3.3.1

  '@vaadin/icon@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      '@polymer/polymer': 3.5.2
      '@vaadin/component-base': 24.7.11
      '@vaadin/vaadin-lumo-styles': 24.7.11
      '@vaadin/vaadin-themable-mixin': 24.7.11
      lit: 3.3.1

  '@vaadin/input-container@24.7.11':
    dependencies:
      '@polymer/polymer': 3.5.2
      '@vaadin/component-base': 24.7.11
      '@vaadin/vaadin-lumo-styles': 24.7.11
      '@vaadin/vaadin-material-styles': 24.7.11
      '@vaadin/vaadin-themable-mixin': 24.7.11
      lit: 3.3.1

  '@vaadin/lit-renderer@24.7.11':
    dependencies:
      lit: 3.3.1

  '@vaadin/text-field@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      '@polymer/polymer': 3.5.2
      '@vaadin/a11y-base': 24.7.11
      '@vaadin/component-base': 24.7.11
      '@vaadin/field-base': 24.7.11
      '@vaadin/input-container': 24.7.11
      '@vaadin/vaadin-lumo-styles': 24.7.11
      '@vaadin/vaadin-material-styles': 24.7.11
      '@vaadin/vaadin-themable-mixin': 24.7.11
      lit: 3.3.1

  '@vaadin/vaadin-development-mode-detector@2.0.7': {}

  '@vaadin/vaadin-lumo-styles@24.7.11':
    dependencies:
      '@polymer/polymer': 3.5.2
      '@vaadin/component-base': 24.7.11
      '@vaadin/icon': 24.7.11
      '@vaadin/vaadin-themable-mixin': 24.7.11

  '@vaadin/vaadin-material-styles@24.7.11':
    dependencies:
      '@polymer/polymer': 3.5.2
      '@vaadin/component-base': 24.7.11
      '@vaadin/vaadin-themable-mixin': 24.7.11

  '@vaadin/vaadin-themable-mixin@24.7.11':
    dependencies:
      '@open-wc/dedupe-mixin': 1.4.0
      lit: 3.3.1

  '@vaadin/vaadin-usage-statistics@2.1.3':
    dependencies:
      '@vaadin/vaadin-development-mode-detector': 2.0.7

  '@vis.gl/react-mapbox@8.0.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  '@vis.gl/react-maplibre@8.0.4(maplibre-gl@5.6.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    dependencies:
      '@maplibre/maplibre-gl-style-spec': 19.3.3
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      maplibre-gl: 5.6.1

  '@vitejs/plugin-react@4.7.0(vite@7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0))':
    dependencies:
      '@babel/core': 7.28.0
      '@babel/plugin-transform-react-jsx-self': 7.27.1(@babel/core@7.28.0)
      '@babel/plugin-transform-react-jsx-source': 7.27.1(@babel/core@7.28.0)
      '@rolldown/pluginutils': 1.0.0-beta.27
      '@types/babel__core': 7.20.5
      react-refresh: 0.17.0
      vite: 7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
    transitivePeerDependencies:
      - supports-color

  '@webcomponents/shadycss@1.11.2': {}

  '@zeit/schemas@2.36.0': {}

  '@zip.js/zip.js@2.7.68': {}

  abstract-logging@2.0.1: {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  ajv-formats@2.1.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv-formats@3.0.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv@8.12.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-align@3.0.1:
    dependencies:
      string-width: 4.2.3

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  arch@2.2.0: {}

  arg@5.0.2: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  arr-union@3.1.0: {}

  asn1.js@5.4.1:
    dependencies:
      bn.js: 4.12.2
      inherits: 2.0.4
      minimalistic-assert: 1.0.1
      safer-buffer: 2.1.2

  assign-symbols@1.0.0: {}

  atomic-sleep@1.0.0: {}

  automation-events@7.1.11:
    dependencies:
      '@babel/runtime': 7.28.2
      tslib: 2.8.1

  avvio@8.4.0:
    dependencies:
      '@fastify/error': 3.4.1
      fastq: 1.19.1

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  bcryptjs@2.4.3: {}

  better-sqlite3@12.2.0:
    dependencies:
      bindings: 1.5.0
      prebuild-install: 7.1.3

  bidi-js@1.0.3:
    dependencies:
      require-from-string: 2.0.2

  bignumber.js@9.3.1: {}

  bindings@1.5.0:
    dependencies:
      file-uri-to-path: 1.0.0

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  bn.js@4.12.2: {}

  boxen@7.0.0:
    dependencies:
      ansi-align: 3.0.1
      camelcase: 7.0.1
      chalk: 5.0.1
      cli-boxes: 3.0.0
      string-width: 5.1.2
      type-fest: 2.19.0
      widest-line: 4.0.1
      wrap-ansi: 8.1.0

  brace-expansion@1.1.12:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.2:
    dependencies:
      balanced-match: 1.0.2

  brotli@1.3.3:
    dependencies:
      base64-js: 1.5.1
    optional: true

  browserslist@4.25.1:
    dependencies:
      caniuse-lite: 1.0.30001727
      electron-to-chromium: 1.5.190
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.25.1)

  buf-compare@1.0.1: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  bytes@3.0.0: {}

  bytewise-core@1.2.3:
    dependencies:
      typewise-core: 1.2.0

  bytewise@1.1.0:
    dependencies:
      bytewise-core: 1.2.3
      typewise: 1.0.3

  callsites@3.1.0: {}

  camelcase@6.3.0: {}

  camelcase@7.0.1: {}

  camera-controls@2.10.1(three@0.152.0):
    dependencies:
      three: 0.152.0

  caniuse-lite@1.0.30001727: {}

  cartocolor@5.0.2:
    dependencies:
      colorbrewer: 1.5.6

  chalk-template@0.4.0:
    dependencies:
      chalk: 4.1.2

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.0.1: {}

  charenc@0.0.2: {}

  chownr@1.1.4: {}

  chownr@3.0.0: {}

  cli-boxes@3.0.0: {}

  clipboardy@3.0.0:
    dependencies:
      arch: 2.2.0
      execa: 5.1.1
      is-wsl: 2.2.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clsx@2.1.1: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-convert@3.1.0:
    dependencies:
      color-name: 2.0.0

  color-name@1.1.4: {}

  color-name@2.0.0: {}

  color-string@2.0.1:
    dependencies:
      color-name: 2.0.0

  color@5.0.0:
    dependencies:
      color-convert: 3.1.0
      color-string: 2.0.1

  colorbrewer@1.5.6: {}

  commander@2.20.3: {}

  commander@7.2.0: {}

  composed-offset-position@0.0.6(@floating-ui/utils@0.2.10):
    dependencies:
      '@floating-ui/utils': 0.2.10

  compressible@2.0.18:
    dependencies:
      mime-db: 1.54.0

  compression@1.7.4:
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  concaveman@1.2.1:
    dependencies:
      point-in-polygon: 1.1.0
      rbush: 3.0.1
      robust-predicates: 2.0.4
      tinyqueue: 2.0.3

  concurrently@8.2.2:
    dependencies:
      chalk: 4.1.2
      date-fns: 2.30.0
      lodash: 4.17.21
      rxjs: 7.8.2
      shell-quote: 1.8.3
      spawn-command: 0.0.2
      supports-color: 8.1.1
      tree-kill: 1.2.2
      yargs: 17.7.2

  content-disposition@0.5.2: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  convert-source-map@2.0.0: {}

  cookie@0.7.2: {}

  core-assert@0.2.1:
    dependencies:
      buf-compare: 1.0.1
      is-error: 2.2.2

  core-util-is@1.0.3: {}

  cosmiconfig@8.3.6(typescript@5.8.3):
    dependencies:
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
      path-type: 4.0.0
    optionalDependencies:
      typescript: 5.8.3

  cross-env@7.0.3:
    dependencies:
      cross-spawn: 7.0.6

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypt@0.0.2: {}

  cssfilter@0.0.10: {}

  csstype@3.1.3: {}

  d3-array@1.2.4: {}

  d3-array@3.2.4:
    dependencies:
      internmap: 2.0.3

  d3-axis@3.0.0: {}

  d3-brush@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1(d3-selection@3.0.0)

  d3-chord@3.0.1:
    dependencies:
      d3-path: 3.1.0

  d3-color@3.1.0: {}

  d3-contour@4.0.2:
    dependencies:
      d3-array: 3.2.4

  d3-delaunay@6.0.4:
    dependencies:
      delaunator: 5.0.1

  d3-dispatch@3.0.1: {}

  d3-drag@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-selection: 3.0.0

  d3-dsv@3.0.1:
    dependencies:
      commander: 7.2.0
      iconv-lite: 0.6.3
      rw: 1.3.3

  d3-ease@3.0.1: {}

  d3-fetch@3.0.1:
    dependencies:
      d3-dsv: 3.0.1

  d3-force@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-quadtree: 3.0.1
      d3-timer: 3.0.1

  d3-format@3.1.0: {}

  d3-geo@1.7.1:
    dependencies:
      d3-array: 1.2.4

  d3-geo@3.1.1:
    dependencies:
      d3-array: 3.2.4

  d3-hexbin@0.2.2: {}

  d3-hierarchy@3.1.2: {}

  d3-interpolate@3.0.1:
    dependencies:
      d3-color: 3.1.0

  d3-path@3.1.0: {}

  d3-polygon@3.0.1: {}

  d3-quadtree@3.0.1: {}

  d3-random@3.0.1: {}

  d3-scale-chromatic@3.1.0:
    dependencies:
      d3-color: 3.1.0
      d3-interpolate: 3.0.1

  d3-scale@4.0.2:
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0

  d3-selection@3.0.0: {}

  d3-shape@3.2.0:
    dependencies:
      d3-path: 3.1.0

  d3-time-format@4.1.0:
    dependencies:
      d3-time: 3.1.0

  d3-time@3.1.0:
    dependencies:
      d3-array: 3.2.4

  d3-timer@3.0.1: {}

  d3-transition@3.0.1(d3-selection@3.0.0):
    dependencies:
      d3-color: 3.1.0
      d3-dispatch: 3.0.1
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-timer: 3.0.1

  d3-voronoi@1.1.2: {}

  d3-zoom@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1(d3-selection@3.0.0)

  d3@7.9.0:
    dependencies:
      d3-array: 3.2.4
      d3-axis: 3.0.0
      d3-brush: 3.0.0
      d3-chord: 3.0.1
      d3-color: 3.1.0
      d3-contour: 4.0.2
      d3-delaunay: 6.0.4
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-dsv: 3.0.1
      d3-ease: 3.0.1
      d3-fetch: 3.0.1
      d3-force: 3.0.0
      d3-format: 3.1.0
      d3-geo: 3.1.1
      d3-hierarchy: 3.1.2
      d3-interpolate: 3.0.1
      d3-path: 3.1.0
      d3-polygon: 3.0.1
      d3-quadtree: 3.0.1
      d3-random: 3.0.1
      d3-scale: 4.0.2
      d3-scale-chromatic: 3.1.0
      d3-selection: 3.0.0
      d3-shape: 3.2.0
      d3-time: 3.1.0
      d3-time-format: 4.1.0
      d3-timer: 3.0.1
      d3-transition: 3.0.1(d3-selection@3.0.0)
      d3-zoom: 3.0.0

  data-uri-to-buffer@4.0.1: {}

  date-fns@2.30.0:
    dependencies:
      '@babel/runtime': 7.28.2

  dayjs@1.11.13: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  deck.gl@9.1.13(@arcgis/core@4.33.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))(@luma.gl/webgl@9.1.9(@luma.gl/core@9.1.9))(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@deck.gl/aggregation-layers': 9.1.13(@deck.gl/core@9.1.13)(@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/arcgis': 9.1.13(@arcgis/core@4.33.9)(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))(@luma.gl/webgl@9.1.9(@luma.gl/core@9.1.9))
      '@deck.gl/carto': 9.1.13(yduymzwkmjlkqajmgfv23a5xca)
      '@deck.gl/core': 9.1.13
      '@deck.gl/extensions': 9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/geo-layers': 9.1.13(@deck.gl/core@9.1.13)(@deck.gl/extensions@9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@deck.gl/mesh-layers@9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))))(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/google-maps': 9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)(@luma.gl/webgl@9.1.9(@luma.gl/core@9.1.9))
      '@deck.gl/json': 9.1.13(@deck.gl/core@9.1.13)
      '@deck.gl/layers': 9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/mapbox': 9.1.13(@deck.gl/core@9.1.13)(@luma.gl/core@9.1.9)
      '@deck.gl/mesh-layers': 9.1.13(@deck.gl/core@9.1.13)(@loaders.gl/core@4.3.4)(@luma.gl/core@9.1.9)(@luma.gl/engine@9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9)))
      '@deck.gl/react': 9.1.13(@deck.gl/core@9.1.13)(@deck.gl/widgets@9.1.13(@deck.gl/core@9.1.13))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@deck.gl/widgets': 9.1.13(@deck.gl/core@9.1.13)
      '@loaders.gl/core': 4.3.4
      '@luma.gl/core': 9.1.9
      '@luma.gl/engine': 9.1.9(@luma.gl/core@9.1.9)(@luma.gl/shadertools@9.1.9(@luma.gl/core@9.1.9))
    optionalDependencies:
      '@arcgis/core': 4.33.9
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - '@luma.gl/shadertools'
      - '@luma.gl/webgl'

  decompress-response@6.0.0:
    dependencies:
      mimic-response: 3.1.0

  deep-extend@0.6.0: {}

  deep-strict-equal@0.2.0:
    dependencies:
      core-assert: 0.2.1

  delaunator@5.0.1:
    dependencies:
      robust-predicates: 3.0.2

  depd@2.0.0: {}

  detect-gpu@5.0.70:
    dependencies:
      webgl-constants: 1.1.1

  detect-libc@2.0.4: {}

  dnd-core@16.0.1:
    dependencies:
      '@react-dnd/asap': 5.0.2
      '@react-dnd/invariant': 4.0.2
      redux: 4.2.1

  dot-case@3.0.4:
    dependencies:
      no-case: 3.0.4
      tslib: 2.8.1

  dotenv@17.2.0: {}

  draco3d@1.5.7: {}

  earcut@2.2.4: {}

  earcut@3.0.2: {}

  eastasianwidth@0.2.0: {}

  ecdsa-sig-formatter@1.0.11:
    dependencies:
      safe-buffer: 5.2.1

  echarts-for-react@3.0.2(echarts@5.6.0)(react@18.3.1):
    dependencies:
      echarts: 5.6.0
      fast-deep-equal: 3.1.3
      react: 18.3.1
      size-sensor: 1.0.2

  echarts@5.6.0:
    dependencies:
      tslib: 2.3.0
      zrender: 5.6.1

  electron-to-chromium@1.5.190: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  end-of-stream@1.4.5:
    dependencies:
      once: 1.4.0

  enhanced-resolve@5.18.2:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  entities@4.5.0: {}

  entities@6.0.1: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  esbuild@0.25.6:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.6
      '@esbuild/android-arm': 0.25.6
      '@esbuild/android-arm64': 0.25.6
      '@esbuild/android-x64': 0.25.6
      '@esbuild/darwin-arm64': 0.25.6
      '@esbuild/darwin-x64': 0.25.6
      '@esbuild/freebsd-arm64': 0.25.6
      '@esbuild/freebsd-x64': 0.25.6
      '@esbuild/linux-arm': 0.25.6
      '@esbuild/linux-arm64': 0.25.6
      '@esbuild/linux-ia32': 0.25.6
      '@esbuild/linux-loong64': 0.25.6
      '@esbuild/linux-mips64el': 0.25.6
      '@esbuild/linux-ppc64': 0.25.6
      '@esbuild/linux-riscv64': 0.25.6
      '@esbuild/linux-s390x': 0.25.6
      '@esbuild/linux-x64': 0.25.6
      '@esbuild/netbsd-arm64': 0.25.6
      '@esbuild/netbsd-x64': 0.25.6
      '@esbuild/openbsd-arm64': 0.25.6
      '@esbuild/openbsd-x64': 0.25.6
      '@esbuild/openharmony-arm64': 0.25.6
      '@esbuild/sunos-x64': 0.25.6
      '@esbuild/win32-arm64': 0.25.6
      '@esbuild/win32-ia32': 0.25.6
      '@esbuild/win32-x64': 0.25.6

  esbuild@0.25.8:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.8
      '@esbuild/android-arm': 0.25.8
      '@esbuild/android-arm64': 0.25.8
      '@esbuild/android-x64': 0.25.8
      '@esbuild/darwin-arm64': 0.25.8
      '@esbuild/darwin-x64': 0.25.8
      '@esbuild/freebsd-arm64': 0.25.8
      '@esbuild/freebsd-x64': 0.25.8
      '@esbuild/linux-arm': 0.25.8
      '@esbuild/linux-arm64': 0.25.8
      '@esbuild/linux-ia32': 0.25.8
      '@esbuild/linux-loong64': 0.25.8
      '@esbuild/linux-mips64el': 0.25.8
      '@esbuild/linux-ppc64': 0.25.8
      '@esbuild/linux-riscv64': 0.25.8
      '@esbuild/linux-s390x': 0.25.8
      '@esbuild/linux-x64': 0.25.8
      '@esbuild/netbsd-arm64': 0.25.8
      '@esbuild/netbsd-x64': 0.25.8
      '@esbuild/openbsd-arm64': 0.25.8
      '@esbuild/openbsd-x64': 0.25.8
      '@esbuild/openharmony-arm64': 0.25.8
      '@esbuild/sunos-x64': 0.25.8
      '@esbuild/win32-arm64': 0.25.8
      '@esbuild/win32-ia32': 0.25.8
      '@esbuild/win32-x64': 0.25.8

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  esri-loader@3.7.0: {}

  estree-walker@2.0.2: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  expand-template@2.0.3: {}

  extend-shallow@2.0.1:
    dependencies:
      is-extendable: 0.1.1

  extend-shallow@3.0.2:
    dependencies:
      assign-symbols: 1.0.0
      is-extendable: 1.0.1

  fast-content-type-parse@1.1.0: {}

  fast-decode-uri-component@1.0.1: {}

  fast-deep-equal@3.1.3: {}

  fast-json-stringify@5.16.1:
    dependencies:
      '@fastify/merge-json-schemas': 0.1.1
      ajv: 8.17.1
      ajv-formats: 3.0.1(ajv@8.17.1)
      fast-deep-equal: 3.1.3
      fast-uri: 2.4.0
      json-schema-ref-resolver: 1.0.1
      rfdc: 1.4.1

  fast-jwt@4.0.5:
    dependencies:
      '@lukeed/ms': 2.0.2
      asn1.js: 5.4.1
      ecdsa-sig-formatter: 1.0.11
      mnemonist: 0.39.8

  fast-querystring@1.1.2:
    dependencies:
      fast-decode-uri-component: 1.0.1

  fast-redact@3.5.0: {}

  fast-uri@2.4.0: {}

  fast-uri@3.0.6: {}

  fast-xml-parser@4.5.3:
    dependencies:
      strnum: 1.1.2

  fastfall@1.5.1:
    dependencies:
      reusify: 1.1.0

  fastify-plugin@4.5.1: {}

  fastify@4.29.1:
    dependencies:
      '@fastify/ajv-compiler': 3.6.0
      '@fastify/error': 3.4.1
      '@fastify/fast-json-stringify-compiler': 4.3.0
      abstract-logging: 2.0.1
      avvio: 8.4.0
      fast-content-type-parse: 1.1.0
      fast-json-stringify: 5.16.1
      find-my-way: 8.2.2
      light-my-request: 5.14.0
      pino: 9.7.0
      process-warning: 3.0.0
      proxy-addr: 2.0.7
      rfdc: 1.4.1
      secure-json-parse: 2.7.0
      semver: 7.7.2
      toad-cache: 3.7.0

  fastparallel@2.4.1:
    dependencies:
      reusify: 1.1.0
      xtend: 4.0.2

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fastseries@1.7.2:
    dependencies:
      reusify: 1.1.0
      xtend: 4.0.2

  fdir@6.4.6(picomatch@4.0.3):
    optionalDependencies:
      picomatch: 4.0.3

  fetch-blob@3.2.0:
    dependencies:
      node-domexception: 1.0.0
      web-streams-polyfill: 3.3.3

  fflate@0.6.10: {}

  fflate@0.7.4: {}

  file-uri-to-path@1.0.0: {}

  find-my-way@8.2.2:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-querystring: 1.1.2
      safe-regex2: 3.1.0

  focus-trap@7.6.5:
    dependencies:
      tabbable: 6.2.0

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  formdata-polyfill@4.0.10:
    dependencies:
      fetch-blob: 3.2.0

  forwarded@0.2.0: {}

  fs-constants@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  gensync@1.0.0-beta.2: {}

  geojson-equality-ts@1.0.2:
    dependencies:
      '@types/geojson': 7946.0.16

  geojson-polygon-self-intersections@1.2.1:
    dependencies:
      rbush: 2.0.2

  geojson-vt@4.0.2: {}

  get-caller-file@2.0.5: {}

  get-stream@6.0.1: {}

  get-tsconfig@4.10.1:
    dependencies:
      resolve-pkg-maps: 1.0.0

  get-value@2.0.6: {}

  github-from-package@0.0.0: {}

  gl-matrix@3.4.3: {}

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  global-prefix@4.0.0:
    dependencies:
      ini: 4.1.3
      kind-of: 6.0.3
      which: 4.0.0

  glsl-noise@0.0.0: {}

  graceful-fs@4.2.11: {}

  h3-js@4.1.0: {}

  h3-js@4.2.1: {}

  has-flag@4.0.0: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  human-signals@2.1.0: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ieee754@1.2.1: {}

  image-size@0.7.5: {}

  immediate@3.0.6: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  inherits@2.0.4: {}

  ini@1.3.8: {}

  ini@4.1.3: {}

  interactjs@1.10.27:
    dependencies:
      '@interactjs/types': 1.10.27

  internmap@2.0.3: {}

  ipaddr.js@1.9.1: {}

  is-arrayish@0.2.1: {}

  is-buffer@1.1.6: {}

  is-docker@2.2.1: {}

  is-error@2.2.2: {}

  is-extendable@0.1.1: {}

  is-extendable@1.0.1:
    dependencies:
      is-plain-object: 2.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-port-reachable@4.0.0: {}

  is-stream@2.0.1: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@1.0.0: {}

  isexe@2.0.0: {}

  isexe@3.1.1: {}

  isobject@3.0.1: {}

  its-fine@1.2.5(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      '@types/react-reconciler': 0.28.9(@types/react@18.3.23)
      react: 18.3.1
    transitivePeerDependencies:
      - '@types/react'

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jiti@2.5.1: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsep@0.3.5: {}

  jsesc@3.1.0: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-ref-resolver@1.0.1:
    dependencies:
      fast-deep-equal: 3.1.3

  json-schema-resolver@2.0.0:
    dependencies:
      debug: 4.4.1
      rfdc: 1.4.1
      uri-js: 4.4.1
    transitivePeerDependencies:
      - supports-color

  json-schema-traverse@1.0.0: {}

  json-stringify-pretty-compact@3.0.0: {}

  json-stringify-pretty-compact@4.0.0: {}

  json5@2.2.3: {}

  jsts@2.7.1: {}

  jszip@3.10.1:
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  kdbush@4.0.2: {}

  kind-of@6.0.3: {}

  ktx-parse@0.7.1: {}

  lie@3.3.0:
    dependencies:
      immediate: 3.0.6

  light-my-request@5.14.0:
    dependencies:
      cookie: 0.7.2
      process-warning: 3.0.0
      set-cookie-parser: 2.7.1

  lightningcss-darwin-arm64@1.30.1:
    optional: true

  lightningcss-darwin-x64@1.30.1:
    optional: true

  lightningcss-freebsd-x64@1.30.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.30.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.30.1:
    optional: true

  lightningcss-linux-arm64-musl@1.30.1:
    optional: true

  lightningcss-linux-x64-gnu@1.30.1:
    optional: true

  lightningcss-linux-x64-musl@1.30.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.30.1:
    optional: true

  lightningcss-win32-x64-msvc@1.30.1:
    optional: true

  lightningcss@1.30.1:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.30.1
      lightningcss-darwin-x64: 1.30.1
      lightningcss-freebsd-x64: 1.30.1
      lightningcss-linux-arm-gnueabihf: 1.30.1
      lightningcss-linux-arm64-gnu: 1.30.1
      lightningcss-linux-arm64-musl: 1.30.1
      lightningcss-linux-x64-gnu: 1.30.1
      lightningcss-linux-x64-musl: 1.30.1
      lightningcss-win32-arm64-msvc: 1.30.1
      lightningcss-win32-x64-msvc: 1.30.1

  lil-gui@0.17.0: {}

  lines-and-columns@1.2.4: {}

  lit-element@4.2.1:
    dependencies:
      '@lit-labs/ssr-dom-shim': 1.4.0
      '@lit/reactive-element': 2.1.1
      lit-html: 3.3.1

  lit-html@3.3.1:
    dependencies:
      '@types/trusted-types': 2.0.7

  lit@3.3.1:
    dependencies:
      '@lit/reactive-element': 2.1.1
      lit-element: 4.2.1
      lit-html: 3.3.1

  lodash-es@4.17.21: {}

  lodash.clamp@4.0.3: {}

  lodash.omit@4.5.0: {}

  lodash.pick@4.4.0: {}

  lodash@4.17.21: {}

  long@3.2.0: {}

  long@5.3.2: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lower-case@2.0.2:
    dependencies:
      tslib: 2.8.1

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lucide-react@0.292.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  luxon@3.6.1: {}

  lz4js@0.2.0:
    optional: true

  lzo-wasm@0.0.4: {}

  maath@0.9.0(@types/three@0.152.0)(three@0.152.0):
    dependencies:
      '@types/three': 0.152.0
      three: 0.152.0

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.4

  maplibre-gl@5.6.1:
    dependencies:
      '@mapbox/geojson-rewind': 0.5.2
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/tiny-sdf': 2.0.6
      '@mapbox/unitbezier': 0.0.1
      '@mapbox/vector-tile': 1.3.1
      '@mapbox/whoots-js': 3.1.0
      '@maplibre/maplibre-gl-style-spec': 23.3.0
      '@types/geojson': 7946.0.16
      '@types/geojson-vt': 3.2.5
      '@types/mapbox__point-geometry': 0.1.4
      '@types/mapbox__vector-tile': 1.3.4
      '@types/pbf': 3.0.5
      '@types/supercluster': 7.1.3
      earcut: 3.0.2
      geojson-vt: 4.0.2
      gl-matrix: 3.4.3
      global-prefix: 4.0.0
      kdbush: 4.0.2
      murmurhash-js: 1.0.0
      pbf: 3.3.0
      potpack: 2.1.0
      quickselect: 3.0.0
      supercluster: 8.0.1
      tinyqueue: 3.0.0
      vt-pbf: 3.1.3

  marchingsquares@1.3.3: {}

  marked@15.0.12: {}

  md5@2.3.0:
    dependencies:
      charenc: 0.0.2
      crypt: 0.0.2
      is-buffer: 1.1.6

  merge-stream@2.0.0: {}

  meshline@3.3.1(three@0.152.0):
    dependencies:
      three: 0.152.0

  mime-db@1.33.0: {}

  mime-db@1.52.0: {}

  mime-db@1.54.0: {}

  mime-types@2.1.18:
    dependencies:
      mime-db: 1.33.0

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@3.0.0: {}

  mimic-fn@2.1.0: {}

  mimic-response@3.1.0: {}

  minimalistic-assert@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.12

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.2

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  minizlib@3.0.2:
    dependencies:
      minipass: 7.1.2

  mjolnir.js@3.0.0: {}

  mkdirp-classic@0.5.3: {}

  mkdirp@3.0.1: {}

  mnemonist@0.39.6:
    dependencies:
      obliterator: 2.0.5

  mnemonist@0.39.8:
    dependencies:
      obliterator: 2.0.5

  moment-timezone@0.5.48:
    dependencies:
      moment: 2.30.1

  moment@2.30.1: {}

  ms@2.0.0: {}

  ms@2.1.3: {}

  murmurhash-js@1.0.0: {}

  nanoid@3.3.11: {}

  napi-build-utils@2.0.0: {}

  negotiator@0.6.3: {}

  no-case@3.0.4:
    dependencies:
      lower-case: 2.0.2
      tslib: 2.8.1

  node-abi@3.75.0:
    dependencies:
      semver: 7.7.2

  node-domexception@1.0.0: {}

  node-fetch@3.3.2:
    dependencies:
      data-uri-to-buffer: 4.0.1
      fetch-blob: 3.2.0
      formdata-polyfill: 4.0.10

  node-releases@2.0.19: {}

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  object-assign@4.1.1: {}

  obliterator@2.0.5: {}

  on-exit-leak-free@2.1.2: {}

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  openapi-types@12.1.3: {}

  package-json-from-dist@1.0.1: {}

  pako@1.0.11: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse5@7.3.0:
    dependencies:
      entities: 6.0.1

  path-is-inside@1.0.2: {}

  path-key@3.1.1: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-to-regexp@3.3.0: {}

  path-type@4.0.0: {}

  pbf@3.3.0:
    dependencies:
      ieee754: 1.2.1
      resolve-protobuf-schema: 2.1.0

  picocolors@1.1.1: {}

  picomatch@4.0.3: {}

  pino-abstract-transport@2.0.0:
    dependencies:
      split2: 4.2.0

  pino-std-serializers@7.0.0: {}

  pino@9.7.0:
    dependencies:
      atomic-sleep: 1.0.0
      fast-redact: 3.5.0
      on-exit-leak-free: 2.1.2
      pino-abstract-transport: 2.0.0
      pino-std-serializers: 7.0.0
      process-warning: 5.0.0
      quick-format-unescaped: 4.0.4
      real-require: 0.2.0
      safe-stable-stringify: 2.5.0
      sonic-boom: 4.2.0
      thread-stream: 3.1.0

  point-in-polygon-hao@1.2.4:
    dependencies:
      robust-predicates: 3.0.2

  point-in-polygon@1.1.0: {}

  polyclip-ts@0.16.8:
    dependencies:
      bignumber.js: 9.3.1
      splaytree-ts: 1.0.2

  postcss@8.5.6:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  potpack@1.0.2: {}

  potpack@2.1.0: {}

  preact@10.26.9: {}

  prebuild-install@7.1.3:
    dependencies:
      detect-libc: 2.0.4
      expand-template: 2.0.3
      github-from-package: 0.0.0
      minimist: 1.2.8
      mkdirp-classic: 0.5.3
      napi-build-utils: 2.0.0
      node-abi: 3.75.0
      pump: 3.0.3
      rc: 1.2.8
      simple-get: 4.0.1
      tar-fs: 2.1.3
      tunnel-agent: 0.6.0

  process-nextick-args@2.0.1: {}

  process-warning@3.0.0: {}

  process-warning@5.0.0: {}

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  protocol-buffers-schema@3.6.0: {}

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  pump@3.0.3:
    dependencies:
      end-of-stream: 1.4.5
      once: 1.4.0

  punycode@2.3.1: {}

  quadbin@0.4.2:
    dependencies:
      '@math.gl/web-mercator': 4.1.0

  quick-format-unescaped@4.0.4: {}

  quickselect@1.1.1: {}

  quickselect@2.0.0: {}

  quickselect@3.0.0: {}

  range-parser@1.2.0: {}

  rbush@2.0.2:
    dependencies:
      quickselect: 1.1.1

  rbush@3.0.1:
    dependencies:
      quickselect: 2.0.0

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1

  react-composer@5.0.3(react@18.3.1):
    dependencies:
      prop-types: 15.8.1
      react: 18.3.1

  react-dnd-html5-backend@16.0.1:
    dependencies:
      dnd-core: 16.0.1

  react-dnd@16.0.1(@types/node@20.19.9)(@types/react@18.3.23)(react@18.3.1):
    dependencies:
      '@react-dnd/invariant': 4.0.2
      '@react-dnd/shallowequal': 4.0.2
      dnd-core: 16.0.1
      fast-deep-equal: 3.1.3
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
    optionalDependencies:
      '@types/node': 20.19.9
      '@types/react': 18.3.23

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-hook-form@7.61.1(react@18.3.1):
    dependencies:
      react: 18.3.1

  react-is@16.13.1: {}

  react-map-gl@8.0.4(maplibre-gl@5.6.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@vis.gl/react-mapbox': 8.0.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      '@vis.gl/react-maplibre': 8.0.4(maplibre-gl@5.6.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    optionalDependencies:
      maplibre-gl: 5.6.1

  react-merge-refs@1.1.0: {}

  react-reconciler@0.27.0(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.21.0

  react-refresh@0.17.0: {}

  react-router-dom@6.30.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-router: 6.30.1(react@18.3.1)

  react-router@6.30.1(react@18.3.1):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 18.3.1

  react-use-measure@2.1.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  real-require@0.2.0: {}

  redux@4.2.1:
    dependencies:
      '@babel/runtime': 7.28.2

  registry-auth-token@3.3.2:
    dependencies:
      rc: 1.2.8
      safe-buffer: 5.2.1

  registry-url@3.1.0:
    dependencies:
      rc: 1.2.8

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  resolve-from@4.0.0: {}

  resolve-pkg-maps@1.0.0: {}

  resolve-protobuf-schema@2.1.0:
    dependencies:
      protocol-buffers-schema: 3.6.0

  ret@0.4.3: {}

  reusify@1.1.0: {}

  rfdc@1.4.1: {}

  robust-predicates@2.0.4: {}

  robust-predicates@3.0.2: {}

  rollup@4.45.1:
    dependencies:
      '@types/estree': 1.0.8
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.45.1
      '@rollup/rollup-android-arm64': 4.45.1
      '@rollup/rollup-darwin-arm64': 4.45.1
      '@rollup/rollup-darwin-x64': 4.45.1
      '@rollup/rollup-freebsd-arm64': 4.45.1
      '@rollup/rollup-freebsd-x64': 4.45.1
      '@rollup/rollup-linux-arm-gnueabihf': 4.45.1
      '@rollup/rollup-linux-arm-musleabihf': 4.45.1
      '@rollup/rollup-linux-arm64-gnu': 4.45.1
      '@rollup/rollup-linux-arm64-musl': 4.45.1
      '@rollup/rollup-linux-loongarch64-gnu': 4.45.1
      '@rollup/rollup-linux-powerpc64le-gnu': 4.45.1
      '@rollup/rollup-linux-riscv64-gnu': 4.45.1
      '@rollup/rollup-linux-riscv64-musl': 4.45.1
      '@rollup/rollup-linux-s390x-gnu': 4.45.1
      '@rollup/rollup-linux-x64-gnu': 4.45.1
      '@rollup/rollup-linux-x64-musl': 4.45.1
      '@rollup/rollup-win32-arm64-msvc': 4.45.1
      '@rollup/rollup-win32-ia32-msvc': 4.45.1
      '@rollup/rollup-win32-x64-msvc': 4.45.1
      fsevents: 2.3.3

  rw@1.3.3: {}

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safe-regex2@3.1.0:
    dependencies:
      ret: 0.4.3

  safe-stable-stringify@2.5.0: {}

  safer-buffer@2.1.2: {}

  scheduler@0.21.0:
    dependencies:
      loose-envify: 1.4.0

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  secure-json-parse@2.7.0: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  serve-handler@6.1.6:
    dependencies:
      bytes: 3.0.0
      content-disposition: 0.5.2
      mime-types: 2.1.18
      minimatch: 3.1.2
      path-is-inside: 1.0.2
      path-to-regexp: 3.3.0
      range-parser: 1.2.0

  serve@14.2.4:
    dependencies:
      '@zeit/schemas': 2.36.0
      ajv: 8.12.0
      arg: 5.0.2
      boxen: 7.0.0
      chalk: 5.0.1
      chalk-template: 0.4.0
      clipboardy: 3.0.0
      compression: 1.7.4
      is-port-reachable: 4.0.0
      serve-handler: 6.1.6
      update-check: 1.5.4
    transitivePeerDependencies:
      - supports-color

  set-cookie-parser@2.7.1: {}

  set-value@2.0.1:
    dependencies:
      extend-shallow: 2.0.1
      is-extendable: 0.1.1
      is-plain-object: 2.0.4
      split-string: 3.1.0

  setimmediate@1.0.5: {}

  setprototypeof@1.2.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.3: {}

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  simple-concat@1.0.1: {}

  simple-get@4.0.1:
    dependencies:
      decompress-response: 6.0.0
      once: 1.4.0
      simple-concat: 1.0.1

  size-sensor@1.0.2: {}

  skmeans@0.9.7: {}

  snake-case@3.0.4:
    dependencies:
      dot-case: 3.0.4
      tslib: 2.8.1

  snappyjs@0.6.1: {}

  sonic-boom@4.2.0:
    dependencies:
      atomic-sleep: 1.0.0

  sort-asc@0.2.0: {}

  sort-desc@0.2.0: {}

  sort-object@3.0.3:
    dependencies:
      bytewise: 1.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      sort-asc: 0.2.0
      sort-desc: 0.2.0
      union-value: 1.0.1

  sortablejs@1.15.6: {}

  source-map-js@1.2.1: {}

  spawn-command@0.0.2: {}

  splaytree-ts@1.0.2: {}

  split-string@3.1.0:
    dependencies:
      extend-shallow: 3.0.2

  split2@4.2.0: {}

  sprintf-js@1.0.3: {}

  standardized-audio-context@25.3.77:
    dependencies:
      '@babel/runtime': 7.28.2
      automation-events: 7.1.11
      tslib: 2.8.1

  stats-gl@1.0.7: {}

  stats.js@0.17.0: {}

  statuses@2.0.1: {}

  steed@1.1.3:
    dependencies:
      fastfall: 1.5.1
      fastparallel: 2.4.1
      fastq: 1.19.1
      fastseries: 1.7.2
      reusify: 1.1.0

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-final-newline@2.0.0: {}

  strip-json-comments@2.0.1: {}

  strnum@1.1.2: {}

  supercluster@8.0.1:
    dependencies:
      kdbush: 4.0.2

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  suspend-react@0.1.3(react@18.3.1):
    dependencies:
      react: 18.3.1

  svg-parser@2.0.4: {}

  sweepline-intersections@1.5.0:
    dependencies:
      tinyqueue: 2.0.3

  tabbable@6.2.0: {}

  tailwindcss@4.1.11: {}

  tapable@2.2.2: {}

  tar-fs@2.1.3:
    dependencies:
      chownr: 1.1.4
      mkdirp-classic: 0.5.3
      pump: 3.0.3
      tar-stream: 2.2.0

  tar-stream@2.2.0:
    dependencies:
      bl: 4.1.0
      end-of-stream: 1.4.5
      fs-constants: 1.0.0
      inherits: 2.0.4
      readable-stream: 3.6.2

  tar@7.4.3:
    dependencies:
      '@isaacs/fs-minipass': 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.2
      mkdirp: 3.0.1
      yallist: 5.0.0

  terra-draw-maplibre-gl-adapter@1.1.1(maplibre-gl@5.6.1)(terra-draw@1.10.0):
    dependencies:
      maplibre-gl: 5.6.1
      terra-draw: 1.10.0

  terra-draw@1.10.0: {}

  texture-compressor@1.0.2:
    dependencies:
      argparse: 1.0.10
      image-size: 0.7.5

  thread-stream@3.1.0:
    dependencies:
      real-require: 0.2.0

  three-mesh-bvh@0.6.8(three@0.152.0):
    dependencies:
      three: 0.152.0

  three-stdlib@2.36.0(three@0.152.0):
    dependencies:
      '@types/draco3d': 1.4.10
      '@types/offscreencanvas': 2019.7.3
      '@types/webxr': 0.5.22
      draco3d: 1.5.7
      fflate: 0.6.10
      potpack: 1.0.2
      three: 0.152.0

  three@0.152.0: {}

  timezone-groups@0.10.4: {}

  tinyglobby@0.2.14:
    dependencies:
      fdir: 6.4.6(picomatch@4.0.3)
      picomatch: 4.0.3

  tinyqueue@2.0.3: {}

  tinyqueue@3.0.0: {}

  toad-cache@3.7.0: {}

  toidentifier@1.0.1: {}

  tone@15.1.22:
    dependencies:
      standardized-audio-context: 25.3.77
      tslib: 2.8.1

  topojson-client@3.1.0:
    dependencies:
      commander: 2.20.3

  topojson-server@3.0.1:
    dependencies:
      commander: 2.20.3

  tree-kill@1.2.2: {}

  troika-three-text@0.47.2(three@0.152.0):
    dependencies:
      bidi-js: 1.0.3
      three: 0.152.0
      troika-three-utils: 0.47.2(three@0.152.0)
      troika-worker-utils: 0.47.2
      webgl-sdf-generator: 1.1.1

  troika-three-utils@0.47.2(three@0.152.0):
    dependencies:
      three: 0.152.0

  troika-worker-utils@0.47.2: {}

  tslib@2.3.0: {}

  tslib@2.8.1: {}

  tsx@4.20.3:
    dependencies:
      esbuild: 0.25.6
      get-tsconfig: 4.10.1
    optionalDependencies:
      fsevents: 2.3.3

  tunnel-agent@0.6.0:
    dependencies:
      safe-buffer: 5.2.1

  type-fest@2.19.0: {}

  type-fest@4.41.0: {}

  typescript@5.8.3: {}

  typewise-core@1.2.0: {}

  typewise@1.0.3:
    dependencies:
      typewise-core: 1.2.0

  undici-types@6.21.0: {}

  union-value@1.0.1:
    dependencies:
      arr-union: 3.1.0
      get-value: 2.0.6
      is-extendable: 0.1.1
      set-value: 2.0.1

  update-browserslist-db@1.1.3(browserslist@4.25.1):
    dependencies:
      browserslist: 4.25.1
      escalade: 3.2.0
      picocolors: 1.1.1

  update-check@1.5.4:
    dependencies:
      registry-auth-token: 3.3.2
      registry-url: 3.1.0

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  use-sync-external-store@1.5.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  util-deprecate@1.0.2: {}

  utility-types@3.11.0: {}

  uuid@9.0.1: {}

  vary@1.1.2: {}

  vite-plugin-svgr@4.3.0(rollup@4.45.1)(typescript@5.8.3)(vite@7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)):
    dependencies:
      '@rollup/pluginutils': 5.2.0(rollup@4.45.1)
      '@svgr/core': 8.1.0(typescript@5.8.3)
      '@svgr/plugin-jsx': 8.1.0(@svgr/core@8.1.0(typescript@5.8.3))
      vite: 7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0)
    transitivePeerDependencies:
      - rollup
      - supports-color
      - typescript

  vite@7.0.6(@types/node@20.19.9)(jiti@2.5.1)(lightningcss@1.30.1)(tsx@4.20.3)(yaml@2.8.0):
    dependencies:
      esbuild: 0.25.8
      fdir: 6.4.6(picomatch@4.0.3)
      picomatch: 4.0.3
      postcss: 8.5.6
      rollup: 4.45.1
      tinyglobby: 0.2.14
    optionalDependencies:
      '@types/node': 20.19.9
      fsevents: 2.3.3
      jiti: 2.5.1
      lightningcss: 1.30.1
      tsx: 4.20.3
      yaml: 2.8.0

  vt-pbf@3.1.3:
    dependencies:
      '@mapbox/point-geometry': 0.1.0
      '@mapbox/vector-tile': 1.3.1
      pbf: 3.3.0

  web-streams-polyfill@3.3.3: {}

  webgl-constants@1.1.1: {}

  webgl-sdf-generator@1.1.1: {}

  wgsl_reflect@1.2.3: {}

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  which@4.0.0:
    dependencies:
      isexe: 3.1.1

  widest-line@4.0.1:
    dependencies:
      string-width: 5.1.2

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  xss@1.0.13:
    dependencies:
      commander: 2.20.3
      cssfilter: 0.0.10

  xtend@4.0.2: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@5.0.0: {}

  yaml@2.8.0: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  zod@3.25.76: {}

  zrender@5.6.1:
    dependencies:
      tslib: 2.3.0

  zstd-codec@0.1.5:
    optional: true

  zustand@3.7.2(react@18.3.1):
    optionalDependencies:
      react: 18.3.1

  zustand@5.0.6(@types/react@18.3.23)(react@18.3.1)(use-sync-external-store@1.5.0(react@18.3.1)):
    optionalDependencies:
      '@types/react': 18.3.23
      react: 18.3.1
      use-sync-external-store: 1.5.0(react@18.3.1)
