# 坐标转换工具

## zone-prefix-converter.js

**CGCS2000带前缀坐标转换器** - 将带投影带号前缀的CGCS2000坐标转换为WGS84经纬度坐标。

### 功能说明

处理格式为 `XX000000` 的CGCS2000投影坐标，其中：
- `XX`: 投影带号（如35表示第35带）
- `000000`: 实际东坐标值（米）

### 使用方法

```bash
# 测试单点转换
node scripts/zone-prefix-converter.js --test

# 转换整个GeoJSON文件
node scripts/zone-prefix-converter.js --convert
```

### 转换示例

**输入**（CGCS2000带前缀）：
- X坐标: `35525589.631` (带号35 + 东坐标525589.631m)
- Y坐标: `3230577.588` (北坐标3230577.588m)

**输出**（WGS84）：
- 经度: `105.263°E`
- 纬度: `29.192°N`
- 位置: 四川省泸州市泸县

### 技术细节

- **坐标系**: CGCS2000 → WGS84 (EPSG:4326)
- **投影**: 3度分带高斯-克吕格投影
- **第35带**: 中央经线105°E，覆盖103.5°-106.5°E
- **适用区域**: 四川东部、重庆西部等地区

### 文件格式

**输入**: C:\Users\<USER>\Desktop\risemap\geojson\Layer_2020.12-县级行政区.geojson

**输出**: C:\Users\<USER>\Desktop\risemap\geojson\Layer_2020.12-县级行政区_ZonePrefix.geojson

转换后的文件包含正确的WGS84坐标和CRS信息。