#!/usr/bin/env node

/**
 * 批量CGCS2000带前缀坐标转换器
 * 支持批量转换指定目录下的GeoJSON文件
 */

const fs = require('fs');
const path = require('path');

// CGCS2000椭球参数
const CGCS2000_ELLIPSOID = {
  a: 6378137.0,
  f: 1 / 298.257222101,
  e2: 0.0066943799901413165,
};

/**
 * 解析带前缀的坐标
 */
function parseZonePrefixedCoordinate(x, y) {
  const xStr = Math.floor(x).toString();
  
  if (xStr.length >= 7) {
    const zoneNumber = parseInt(xStr.substring(0, 2));
    const easting = parseFloat(xStr.substring(2) + '.' + (x.toString().split('.')[1] || '0'));
    
    return {
      zoneNumber,
      easting,
      northing: y
    };
  }
  
  throw new Error('无法解析带前缀的坐标');
}

/**
 * 根据带号计算中央经线
 */
function getCentralMeridianFromZone(zoneNumber) {
  return 75 + (zoneNumber - 25) * 3;
}

/**
 * 角度转换
 */
function degToRad(deg) {
  return deg * Math.PI / 180.0;
}

function radToDeg(rad) {
  return rad * 180.0 / Math.PI;
}

/**
 * 高斯-克吕格投影反算
 */
function gaussKrugerInverse(easting, northing, centralMeridian) {
  const { a, e2 } = CGCS2000_ELLIPSOID;
  
  const x = easting - 500000;
  const y = northing;
  
  const e1 = (1 - Math.sqrt(1 - e2)) / (1 + Math.sqrt(1 - e2));
  
  const M = y;
  const mu = M / (a * (1 - e2/4 - 3*e2*e2/64 - 5*e2*e2*e2/256));
  
  const phi1 = mu + 
    (3*e1/2 - 27*e1*e1*e1/32) * Math.sin(2*mu) +
    (21*e1*e1/16 - 55*e1*e1*e1*e1/32) * Math.sin(4*mu) +
    (151*e1*e1*e1/96) * Math.sin(6*mu);
  
  const sinPhi1 = Math.sin(phi1);
  const cosPhi1 = Math.cos(phi1);
  const tanPhi1 = Math.tan(phi1);
  const sin2Phi1 = sinPhi1 * sinPhi1;
  
  const rho1 = a * (1 - e2) / Math.pow(1 - e2 * sin2Phi1, 1.5);
  const nu1 = a / Math.sqrt(1 - e2 * sin2Phi1);
  
  const T1 = tanPhi1 * tanPhi1;
  const C1 = e2 / (1 - e2) * cosPhi1 * cosPhi1;
  const R1 = a * (1 - e2) / Math.pow(1 - e2 * sin2Phi1, 1.5);
  const D = x / nu1;
  
  const lat = phi1 - (nu1 * tanPhi1 / R1) * 
    (D*D/2 - 
     (5 + 3*T1 + 10*C1 - 4*C1*C1 - 9*e2/(1-e2)) * D*D*D*D/24 +
     (61 + 90*T1 + 298*C1 + 45*T1*T1 - 252*e2/(1-e2) - 3*C1*C1) * D*D*D*D*D*D/720);
  
  const lon = degToRad(centralMeridian) + 
    (D - (1 + 2*T1 + C1) * D*D*D/6 +
     (5 - 2*C1 + 28*T1 - 3*C1*C1 + 8*e2/(1-e2) + 24*T1*T1) * D*D*D*D*D/120) / cosPhi1;
  
  return {
    longitude: radToDeg(lon),
    latitude: radToDeg(lat)
  };
}

/**
 * 转换单个坐标点
 */
function convertCoordinate(x, y) {
  try {
    const parsed = parseZonePrefixedCoordinate(x, y);
    const centralMeridian = getCentralMeridianFromZone(parsed.zoneNumber);
    return gaussKrugerInverse(parsed.easting, parsed.northing, centralMeridian);
  } catch (error) {
    return null;
  }
}

/**
 * 递归转换坐标数组
 */
function convertCoordinates(coordinates) {
  if (!Array.isArray(coordinates)) return coordinates;
  
  if (Array.isArray(coordinates[0])) {
    return coordinates.map(convertCoordinates);
  } else {
    if (coordinates.length >= 2) {
      const [x, y, ...rest] = coordinates;
      const result = convertCoordinate(x, y);
      if (result) {
        return [result.longitude, result.latitude, ...rest];
      }
    }
    return coordinates;
  }
}

/**
 * 转换单个GeoJSON文件
 */
function convertGeoJSONFile(inputPath, outputPath) {
  try {
    const data = JSON.parse(fs.readFileSync(inputPath, 'utf8'));
    
    if (!data.features || !Array.isArray(data.features)) {
      return false;
    }
    
    // 转换所有要素的坐标（静默处理）
    data.features.forEach((feature) => {
      if (feature.geometry && feature.geometry.coordinates) {
        feature.geometry.coordinates = convertCoordinates(feature.geometry.coordinates);
      }
      feature.properties = feature.properties.Text ? {
        name: feature.properties.Text,
      } : {}
    });
    
    // 更新CRS信息
    data.crs = {
      type: "name",
      properties: {
        name: "EPSG:4326",
        note: "Converted from CGCS2000 zone-prefixed coordinates"
      }
    };
    
    // 确保输出目录存在
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // 保存转换后的文件
    fs.writeFileSync(outputPath, JSON.stringify(data), 'utf8');
    
    return true;
    
  } catch (error) {
    console.error(`✗ ${path.basename(inputPath)}: ${error.message}`);
    return false;
  }
}

/**
 * 生成输出文件名
 */
function generateOutputFileName(inputPath, outputDir) {
  const baseName = path.basename(inputPath, '.geojson');
  const cleanName = baseName
    .replace(/Layer_/, '')
    .replace(/2020\.12-/, '')
    .replace(/行政区/, '')
    .trim();
  
  return path.join(outputDir, `${cleanName}_wgs84.geojson`);
}

/**
 * 批量转换目录中的文件
 */
function batchConvertDirectory(inputDir, outputDir) {
  if (!fs.existsSync(inputDir)) {
    console.error(`输入目录不存在: ${inputDir}`);
    return;
  }
  
  // 确保输出目录存在
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  const files = fs.readdirSync(inputDir);
  const geojsonFiles = files.filter(file => file.toLowerCase().endsWith('.geojson'));
  
  if (geojsonFiles.length === 0) {
    return;
  }
  
  let successCount = 0;
  
  geojsonFiles.forEach((file) => {
    const inputPath = path.join(inputDir, file);
    const outputPath = generateOutputFileName(inputPath, outputDir);
    
    if (convertGeoJSONFile(inputPath, outputPath)) {
      successCount++;
      console.log(`✓ ${file}`);
    }
  });
  
  if (successCount > 0) {
    console.log(`完成 ${successCount} 个文件转换`);
  }
}

/**
 * 递归转换目录及子目录
 */
function convertDirectoryRecursive(baseInputDir, baseOutputDir) {
  console.log(`转换: ${baseInputDir} → ${baseOutputDir}`);
  
  function processDirectory(inputDir, outputDir) {
    const items = fs.readdirSync(inputDir);
    
    // 先处理当前目录的GeoJSON文件
    const geojsonFiles = items.filter(item => {
      const fullPath = path.join(inputDir, item);
      return fs.statSync(fullPath).isFile() && item.toLowerCase().endsWith('.geojson');
    });
    
    if (geojsonFiles.length > 0) {
      const relativePath = path.relative(baseInputDir, inputDir);
      const dirName = relativePath || 'root';
      console.log(`📁 ${dirName}:`);
      batchConvertDirectory(inputDir, outputDir);
    }
    
    // 递归处理子目录
    items.forEach(item => {
      const fullPath = path.join(inputDir, item);
      if (fs.statSync(fullPath).isDirectory()) {
        const subOutputDir = path.join(outputDir, item);
        processDirectory(fullPath, subOutputDir);
      }
    });
  }
  
  processDirectory(baseInputDir, baseOutputDir);
  console.log('✅ 批量转换完成');
}

// 主程序
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log(`
CGCS2000批量坐标转换工具

用法:
  node batch-converter.js <输入目录> [输出目录]

示例:
  node batch-converter.js scripts/geojson
  node batch-converter.js scripts/geojson converted

说明:
  - 自动递归处理子目录
  - 输出文件名自动优化
  - 保持目录结构
`);
  process.exit(0);
}

const inputDir = args[0];
const outputDir = args[1] || 'converted';

convertDirectoryRecursive(inputDir, outputDir);