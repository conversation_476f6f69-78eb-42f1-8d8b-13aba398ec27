#!/bin/bash

# Cloudflared 安装和配置脚本

set -e

echo "🌐 开始配置 Cloudflared 内网穿透..."

# 检查是否已安装 cloudflared
if ! command -v cloudflared &> /dev/null; then
    echo "📦 安装 Cloudflared..."
    
    # macOS 安装
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            brew install cloudflared
        else
            # 手动下载安装
            curl -L --output cloudflared.pkg https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-darwin-amd64.pkg
            sudo installer -pkg cloudflared.pkg -target /
            rm cloudflared.pkg
        fi
    # Linux 安装
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        curl -L --output cloudflared.deb https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
        sudo dpkg -i cloudflared.deb
        rm cloudflared.deb
    fi
    
    echo "✅ Cloudflared 安装完成"
else
    echo "✅ Cloudflared 已安装"
fi

# 检查版本
echo "📋 Cloudflared 版本: $(cloudflared version)"

# 创建配置目录
mkdir -p ~/.cloudflared
mkdir -p ./logs

# 登录 Cloudflare (如果还没有登录)
echo "🔐 请在浏览器中完成 Cloudflare 登录授权..."
echo "如果已经登录过，可以跳过此步骤"
read -p "是否需要登录 Cloudflare? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    cloudflared tunnel login
fi

# 创建隧道
echo "🚇 创建 Cloudflare 隧道..."
TUNNEL_NAME="rise-map-tunnel"

# 检查隧道是否已存在
if cloudflared tunnel list | grep -q "$TUNNEL_NAME"; then
    echo "✅ 隧道 '$TUNNEL_NAME' 已存在"
    TUNNEL_ID=$(cloudflared tunnel list | grep "$TUNNEL_NAME" | awk '{print $1}')
else
    echo "📡 创建新隧道 '$TUNNEL_NAME'..."
    cloudflared tunnel create $TUNNEL_NAME
    TUNNEL_ID=$(cloudflared tunnel list | grep "$TUNNEL_NAME" | awk '{print $1}')
    echo "✅ 隧道创建完成，ID: $TUNNEL_ID"
fi

# 更新配置文件中的隧道ID
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s/tunnel: rise-map-tunnel/tunnel: $TUNNEL_ID/" cloudflared.yml
    sed -i '' "s|credentials-file: /Users/<USER>/.cloudflared/rise-map-tunnel.json|credentials-file: $HOME/.cloudflared/$TUNNEL_ID.json|" cloudflared.yml
else
    sed -i "s/tunnel: rise-map-tunnel/tunnel: $TUNNEL_ID/" cloudflared.yml
    sed -i "s|credentials-file: /Users/<USER>/.cloudflared/rise-map-tunnel.json|credentials-file: $HOME/.cloudflared/$TUNNEL_ID.json|" cloudflared.yml
fi

echo "🔗 启动临时隧道获取域名..."
echo "请在另一个终端运行以下命令启动应用："
echo "  pnpm build && pnpm pm2:start"
echo ""
echo "然后运行以下命令启动隧道："
echo "  cloudflared tunnel --config cloudflared.yml run"
echo ""
echo "或者使用快速临时域名："
echo "  cloudflared tunnel --url http://localhost:3000"
echo ""
echo "�� Cloudflared 配置完成！" 