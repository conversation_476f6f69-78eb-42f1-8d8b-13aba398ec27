#!/bin/bash

# 快速启动临时 Cloudflared 隧道
# 不需要域名配置，直接获得临时域名

echo "🚀 快速启动 RiseMap 应用和临时隧道..."

# 检查 cloudflared 是否已安装
if ! command -v cloudflared &> /dev/null; then
    echo "❌ Cloudflared 未安装，请先运行: pnpm tunnel:setup"
    exit 1
fi

# 检查应用是否已构建
if [ ! -d "./apps/web/dist" ]; then
    echo "📦 构建应用..."
    pnpm build
fi

# 启动应用服务
echo "🔧 启动应用服务..."
pnpm pm2:start

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 3

# 检查服务状态
echo "📊 应用服务状态："
pnpm pm2:logs --lines 3

echo ""
echo "🌐 启动临时隧道..."
echo "正在为 Web 应用创建临时域名..."
echo ""
echo "📝 注意: 这将创建一个临时域名，适合测试使用"
echo "🔗 如需固定域名，请先运行: pnpm tunnel:setup"
echo ""

# 启动临时隧道
cloudflared tunnel --url http://localhost:3000 