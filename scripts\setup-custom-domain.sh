#!/bin/bash

# 自定义域名配置脚本
# 配置 rise-map.cosfoc.com

echo "🌐 配置自定义域名: rise-map.cosfoc.com"
echo ""

# 检查域名配置
DOMAIN="rise-map.cosfoc.com"
API_DOMAIN="api.rise-map.cosfoc.com"

echo "📋 域名配置信息:"
echo "  主域名: $DOMAIN"
echo "  API域名: $API_DOMAIN"
echo ""

# 步骤1: Cloudflare 登录
echo "🔐 步骤1: 登录 Cloudflare"
echo "请确保您已经:"
echo "  ✅ 拥有 Cloudflare 账户"
echo "  ✅ cosfoc.com 域名在 Cloudflare 上管理 DNS"
echo ""

read -p "是否继续登录 Cloudflare? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "正在打开浏览器进行登录..."
    cloudflared tunnel login
    
    # 检查登录状态
    if [ $? -eq 0 ]; then
        echo "✅ Cloudflare 登录成功"
    else
        echo "❌ 登录失败，请重试"
        exit 1
    fi
else
    echo "⏭️ 跳过登录步骤"
fi

echo ""

# 步骤2: 创建隧道
echo "🚇 步骤2: 创建命名隧道"
TUNNEL_NAME="rise-map-tunnel"

# 检查隧道是否已存在
if cloudflared tunnel list 2>/dev/null | grep -q "$TUNNEL_NAME"; then
    echo "✅ 隧道 '$TUNNEL_NAME' 已存在"
    TUNNEL_ID=$(cloudflared tunnel list | grep "$TUNNEL_NAME" | awk '{print $1}')
else
    echo "📡 创建新隧道 '$TUNNEL_NAME'..."
    cloudflared tunnel create $TUNNEL_NAME
    
    if [ $? -eq 0 ]; then
        TUNNEL_ID=$(cloudflared tunnel list | grep "$TUNNEL_NAME" | awk '{print $1}')
        echo "✅ 隧道创建完成，ID: $TUNNEL_ID"
    else
        echo "❌ 隧道创建失败"
        exit 1
    fi
fi

# 更新配置文件
echo "📝 更新配置文件..."
if [[ "$OSTYPE" == "darwin"* ]]; then
    sed -i '' "s/tunnel: rise-map-tunnel/tunnel: $TUNNEL_ID/" cloudflared.yml
    sed -i '' "s|credentials-file: /Users/<USER>/.cloudflared/rise-map-tunnel.json|credentials-file: $HOME/.cloudflared/$TUNNEL_ID.json|" cloudflared.yml
else
    sed -i "s/tunnel: rise-map-tunnel/tunnel: $TUNNEL_ID/" cloudflared.yml
    sed -i "s|credentials-file: /Users/<USER>/.cloudflared/rise-map-tunnel.json|credentials-file: $HOME/.cloudflared/$TUNNEL_ID.json|" cloudflared.yml
fi

echo ""

# 步骤3: 配置 DNS
echo "🌍 步骤3: 配置 DNS 记录"
echo "正在创建 DNS 记录..."

# 创建主域名 DNS 记录
cloudflared tunnel route dns $TUNNEL_ID $DOMAIN
if [ $? -eq 0 ]; then
    echo "✅ 已创建 DNS 记录: $DOMAIN"
else
    echo "⚠️ DNS 记录创建失败或已存在: $DOMAIN"
fi

# 创建 API 域名 DNS 记录
cloudflared tunnel route dns $TUNNEL_ID $API_DOMAIN
if [ $? -eq 0 ]; then
    echo "✅ 已创建 DNS 记录: $API_DOMAIN"
else
    echo "⚠️ DNS 记录创建失败或已存在: $API_DOMAIN"
fi

echo ""
echo "🎉 自定义域名配置完成！"
echo ""
echo "📋 访问地址:"
echo "  Web应用: https://$DOMAIN"
echo "  API服务: https://$API_DOMAIN"
echo ""
echo "🚀 启动隧道:"
echo "  pnpm tunnel:start"
echo ""
echo "⏰ DNS 传播可能需要几分钟时间" 