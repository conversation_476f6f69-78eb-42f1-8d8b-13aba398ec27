#!/bin/bash

# 启动 Cloudflared 隧道

echo "🚀 启动 RiseMap 应用和 Cloudflared 隧道..."

# 检查应用是否已构建
if [ ! -d "./apps/web/dist" ]; then
    echo "📦 构建应用..."
    pnpm build
fi

# 启动应用服务
echo "🔧 启动应用服务..."
pnpm pm2:start

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 3

# 检查服务状态
echo "📊 检查服务状态..."
pnpm pm2:logs --lines 5

# 启动隧道
echo "🌐 启动 Cloudflared 隧道..."
if [ -f "cloudflared.yml" ]; then
    echo "使用配置文件启动隧道..."
    cloudflared tunnel --config cloudflared.yml run
else
    echo "使用临时域名启动隧道..."
    echo "Web应用: "
    cloudflared tunnel --url http://localhost:3000 &
    echo "API服务: "
    cloudflared tunnel --url http://localhost:3001
fi 