#!/usr/bin/env node

/**
 * 带前缀的CGCS2000坐标转换器
 * 处理格式：XX000000 (XX=带号, 000000=东坐标)
 */

const fs = require('fs');

// CGCS2000椭球参数
const CGCS2000_ELLIPSOID = {
  a: 6378137.0,
  f: 1 / 298.257222101,
  e2: 0.0066943799901413165,
};

/**
 * 解析带前缀的坐标
 */
function parseZonePrefixedCoordinate(x, y) {
  // 解析X坐标中的带号
  const xStr = Math.floor(x).toString();
  
  if (xStr.length >= 7) {
    // 提取带号 (前2位) 和东坐标 (剩余部分)
    const zoneNumber = parseInt(xStr.substring(0, 2));
    const easting = parseFloat(xStr.substring(2) + '.' + (x.toString().split('.')[1] || '0'));
    
    return {
      zoneNumber,
      easting,
      northing: y
    };
  }
  
  throw new Error('无法解析带前缀的坐标');
}

/**
 * 根据带号计算中央经线
 */
function getCentralMeridianFromZone(zoneNumber) {
  // 3度分带：中央经线 = zoneNumber * 3 (度)
  // 但需要考虑起始经线
  
  // 中国3度分带通常从75°E开始
  // 第25带: 75°E, 第26带: 78°E, ..., 第35带: 105°E
  const centralMeridian = 75 + (zoneNumber - 25) * 3;
  
  return centralMeridian;
}

/**
 * 角度转弧度
 */
function degToRad(deg) {
  return deg * Math.PI / 180.0;
}

/**
 * 弧度转角度
 */
function radToDeg(rad) {
  return rad * 180.0 / Math.PI;
}

/**
 * 高斯-克吕格投影反算
 */
function gaussKrugerInverse(easting, northing, centralMeridian) {
  const { a, e2 } = CGCS2000_ELLIPSOID;
  
  // 移除标准偏移量 (500km)
  const x = easting - 500000;
  const y = northing;
  
  // 计算椭球参数
  const e1 = (1 - Math.sqrt(1 - e2)) / (1 + Math.sqrt(1 - e2));
  
  // 计算底点纬度
  const M = y;
  const mu = M / (a * (1 - e2/4 - 3*e2*e2/64 - 5*e2*e2*e2/256));
  
  const phi1 = mu + 
    (3*e1/2 - 27*e1*e1*e1/32) * Math.sin(2*mu) +
    (21*e1*e1/16 - 55*e1*e1*e1*e1/32) * Math.sin(4*mu) +
    (151*e1*e1*e1/96) * Math.sin(6*mu);
  
  const sinPhi1 = Math.sin(phi1);
  const cosPhi1 = Math.cos(phi1);
  const tanPhi1 = Math.tan(phi1);
  const sin2Phi1 = sinPhi1 * sinPhi1;
  
  const rho1 = a * (1 - e2) / Math.pow(1 - e2 * sin2Phi1, 1.5);
  const nu1 = a / Math.sqrt(1 - e2 * sin2Phi1);
  
  const T1 = tanPhi1 * tanPhi1;
  const C1 = e2 / (1 - e2) * cosPhi1 * cosPhi1;
  const R1 = a * (1 - e2) / Math.pow(1 - e2 * sin2Phi1, 1.5);
  const D = x / nu1;
  
  // 计算纬度
  const lat = phi1 - (nu1 * tanPhi1 / R1) * 
    (D*D/2 - 
     (5 + 3*T1 + 10*C1 - 4*C1*C1 - 9*e2/(1-e2)) * D*D*D*D/24 +
     (61 + 90*T1 + 298*C1 + 45*T1*T1 - 252*e2/(1-e2) - 3*C1*C1) * D*D*D*D*D*D/720);
  
  // 计算经度
  const lon = degToRad(centralMeridian) + 
    (D - (1 + 2*T1 + C1) * D*D*D/6 +
     (5 - 2*C1 + 28*T1 - 3*C1*C1 + 8*e2/(1-e2) + 24*T1*T1) * D*D*D*D*D/120) / cosPhi1;
  
  return {
    longitude: radToDeg(lon),
    latitude: radToDeg(lat)
  };
}

/**
 * 主转换函数
 */
function convertZonePrefixedCoordinate(x, y, silent = false) {
  try {
    // 解析坐标
    const parsed = parseZonePrefixedCoordinate(x, y);
    const { zoneNumber, easting, northing } = parsed;
    
    // 计算中央经线
    const centralMeridian = getCentralMeridianFromZone(zoneNumber);
    
    // 投影反算
    const result = gaussKrugerInverse(easting, northing, centralMeridian);
    
    if (!silent) {
      console.log('=== 带前缀CGCS2000坐标转换 ===');
      console.log(`经度: ${result.longitude.toFixed(8)}°E`);
      console.log(`纬度: ${result.latitude.toFixed(8)}°N`);
      
      // 地理验证
      const inChina = (result.longitude >= 73 && result.longitude <= 135 && 
                       result.latitude >= 16 && result.latitude <= 54);
      const inSichuan = (result.longitude >= 97 && result.longitude <= 108 && 
                         result.latitude >= 26 && result.latitude <= 34);
      const nearLuzhou = (Math.abs(result.longitude - 105.38) < 1 && 
                          Math.abs(result.latitude - 29.05) < 1);
      
      console.log(`在中国范围内: ${inChina ? '✓' : '✗'}`);
      console.log(`在四川范围内: ${inSichuan ? '✓' : '✗'}`);
      console.log(`接近泸县位置: ${nearLuzhou ? '✓' : '✗'}`);
      
      if (nearLuzhou) {
        console.log('🎉 转换成功！坐标位于四川泸县附近');
      } else if (inSichuan) {
        console.log('✓ 转换成功！坐标位于四川省内');
      } else if (inChina) {
        console.log('? 坐标在中国境内，但可能不在四川');
      } else {
        console.log('✗ 坐标超出中国范围，可能转换有误');
      }
    }
    
    return result;
    
  } catch (error) {
    if (!silent) {
      console.error('转换失败:', error.message);
    }
    return null;
  }
}

/**
 * 转换GeoJSON文件
 */
function convertGeoJSONFile() {
  const inputFile = 'C:\\Users\\<USER>\\Desktop\\risemap\\geojson\\Layer_2020.12-县级行政区.geojson';
  const outputFile = 'C:\\Users\\<USER>\\Desktop\\risemap\\geojson\\Layer_2020.12-县级行政区_ZonePrefix.geojson';
  
  try {
    console.log('=== 带前缀坐标GeoJSON转换 ===\n');
    
    const data = JSON.parse(fs.readFileSync(inputFile, 'utf8'));
    console.log(`文件类型: ${data.type}`);
    console.log(`要素数量: ${data.features.length}`);
    
    // 测试第一个坐标
    const firstCoord = data.features[0].geometry.coordinates[0];
    console.log(`\n测试第一个坐标: [${firstCoord[0]}, ${firstCoord[1]}]`);
    
    const testResult = convertZonePrefixedCoordinate(firstCoord[0], firstCoord[1]);
    
    if (!testResult) {
      console.log('测试失败，停止转换');
      return;
    }
    
    // 如果测试成功且结果合理，继续转换整个文件
    const nearLuzhou = (Math.abs(testResult.longitude - 105.38) < 1 && 
                        Math.abs(testResult.latitude - 29.05) < 1);
    
    if (nearLuzhou) {
      console.log('\n✓ 测试通过，开始转换整个文件...');
      
      function convertCoordinates(coordinates) {
        if (!Array.isArray(coordinates)) return coordinates;
        
        if (Array.isArray(coordinates[0])) {
          return coordinates.map(convertCoordinates);
        } else {
          if (coordinates.length >= 2) {
            const [x, y, ...rest] = coordinates;
            try {
              const result = convertZonePrefixedCoordinate(x, y, true); // silent mode
              if (result) {
                return [result.longitude, result.latitude, ...rest];
              }
              return coordinates;
            } catch (error) {
              return coordinates;
            }
          }
          return coordinates;
        }
      }
      
      // 转换所有坐标
      data.features.forEach((feature, index) => {
        if (feature.geometry && feature.geometry.coordinates) {
          feature.geometry.coordinates = convertCoordinates(feature.geometry.coordinates);
        }
      });
      
      // 更新CRS信息
      data.crs = {
        type: "name",
        properties: {
          name: "EPSG:4326",
          note: "Converted from CGCS2000 zone-prefixed coordinates"
        }
      };
      
      fs.writeFileSync(outputFile, JSON.stringify(data, null, 2), 'utf8');
      console.log(`\n✓ 转换完成！输出文件: ${outputFile}`);
    } else {
      console.log('\n⚠️  测试结果不在预期范围内，建议检查转换方法');
    }
    
  } catch (error) {
    console.error('文件处理失败:', error.message);
  }
}

// 主程序
const args = process.argv.slice(2);

if (args.includes('--test')) {
  const x = 35525589.631300017237663;
  const y = 3230577.587799999862909;
  convertZonePrefixedCoordinate(x, y);
} else if (args.includes('--convert')) {
  convertGeoJSONFile();
} else {
  console.log(`
带前缀CGCS2000坐标转换工具

用法:
  node zone-prefix-converter.js --test     # 测试转换
  node zone-prefix-converter.js --convert  # 转换GeoJSON文件
  
说明:
  处理格式为 XX000000 的坐标，其中：
  - XX: 投影带号 (如35表示第35带)
  - 000000: 实际东坐标值
`);
}

/**
 * 简洁的坐标转换函数（供外部调用）
 */
function convertCoordinate(x, y) {
  try {
    const parsed = parseZonePrefixedCoordinate(x, y);
    const centralMeridian = getCentralMeridianFromZone(parsed.zoneNumber);
    const result = gaussKrugerInverse(parsed.easting, parsed.northing, centralMeridian);
    return [result.longitude, result.latitude];
  } catch (error) {
    return null;
  }
}

module.exports = { 
  convertZonePrefixedCoordinate,
  parseZonePrefixedCoordinate,
  gaussKrugerInverse,
  convertCoordinate
};